package com.kbao.kbcelms.espt.service;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcelms.espt.dto.ESPTProductQuery;
import com.kbao.kbcespt.client.ProductClientService;
import com.kbao.kbcespt.mongo.product.vo.EsProductInfoVO;
import com.kbao.kbcespt.mongo.product.vo.ProductInfoSelectVO;
import com.kbao.kbcespt.mongo.product.vo.ServiceItemVO;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业生态服务类
 * <AUTHOR>
 */
@Service
public class ESPTService {

    @Autowired
    ProductClientService productClientService;

    /**
     * 获取产品列表
     * @return
     */
    public ProductInfoSelectVO getProduct() {
        setFeignRequestHeader();
        Result<ProductInfoSelectVO> result = productClientService.getAll();
        return handleResult(result);
    }

    /**
     * 获取产品详情
     * @param query
     * @return
     */
    public EsProductInfoVO getProductDetail(ESPTProductQuery query) {
        setFeignRequestHeader();
        Result<EsProductInfoVO> result = productClientService.getDetail(query.productId, query.productCode);
        return handleResult(result);
    }

    /**
     * 获取服务项目详情
     * @param serviceItemId
     * @return
     */
    public ServiceItemVO getServiceItem(String serviceItemId) {
        setFeignRequestHeader();
        Result<ServiceItemVO> result = productClientService.getServiceItem(serviceItemId);
        return handleResult(result);
    }

    private String getTenantId(){
        return SysLoginUtils.getUser().getTenantId();
    }

    /**
     * 设置Feign请求头
     */
    private void setFeignRequestHeader() {
        final Map<String, String> header = new HashMap<>();
        header.put("tenantId", getTenantId());
        FeignRequestHeader.Header.set(header);
    }

    /**
     * 统一处理Result结果
     */
    private <T> T handleResult(Result<T> result) {
        if (!ResultStatusEnum.SUCCESS.getStatus().equals(result.getResp_code())) {
            throw new BusinessException(result.getResp_code(), result.getResp_msg());
        }
        return result.getDatas();
    }
}
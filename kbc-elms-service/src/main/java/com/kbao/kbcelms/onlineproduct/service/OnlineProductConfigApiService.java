package com.kbao.kbcelms.onlineproduct.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.onlineproduct.dao.OnlineProductConfigMapper;
import com.kbao.kbcelms.onlineproduct.entity.OnlineProductConfig;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.kbcelms.util.ElmsRedisKeyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

@Service
public class OnlineProductConfigApiService extends BaseSQLServiceImpl<OnlineProductConfig, Long, OnlineProductConfigMapper> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    private static final String INSURANCE_TYPES_CACHE_PREFIX = "ELMS_INSURANCE_TYPES_CACHE";
    private static final long CACHE_EXPIRE_TIME = 60 * 60 * 24; // 缓存1天

    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;
    
    @Autowired
    private RedisUtil redisUtil;


    /**
     * 根据企业客户ID获取可配置的险种类型
     * 带Redis缓存，缓存时间1天
     * @param enterpriseId 企业客户ID
     * @return 险种类型编码数组
     */
    public List<String> getInsuranceTypesByEnterprise(Integer enterpriseId) {
        try {
            logger.info("开始根据企业客户ID获取险种类型，企业ID: {}", enterpriseId);
            
            String tenantId = ElmsContext.getTenantId();
            
            // 生成缓存key
            String cacheKey = ElmsRedisKeyUtil.generateKey(tenantId, INSURANCE_TYPES_CACHE_PREFIX, String.valueOf(enterpriseId));
            
            // 先从缓存中获取
            Object cachedValue = redisUtil.get(cacheKey);
            if (EmptyUtils.isNotEmpty(cachedValue)) {
                logger.info("从缓存中获取到险种类型，企业ID: {}", enterpriseId);
                return (List<String>) cachedValue;
            }

            // 1. 根据企业客户ID获取企业基本信息
            GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(enterpriseId);
            if (enterprise == null) {
                logger.warn("未找到企业信息，企业ID: {}", enterpriseId);
                return new ArrayList<>();
            }

            // 2. 获取企业的风险类型代码（简化逻辑，直接使用企业的categoryCode）
            String industryCode = enterprise.getCategoryCode();
            if (!StringUtils.hasText(industryCode)) {
                logger.warn("企业未配置行业类型，企业ID: {}, 企业名称: {}", enterpriseId, enterprise.getName());
                return new ArrayList<>();
            }

            logger.info("企业行业类型代码: {}, 企业ID: {}", industryCode, enterpriseId);

            // 3. 根据风险类型查询线上产品配置（支持模糊匹配）
            List<OnlineProductConfig> configs = mapper.selectByIndustryCode(industryCode);
            if (configs.isEmpty()) {
                logger.warn("未找到匹配的线上产品配置，企业ID: {}, 行业类型: {}", enterpriseId, industryCode);
                return new ArrayList<>();
            }

            // 4. 汇总所有可配置的险种类型
            Set<String> allInsuranceTypes = new HashSet<>();
            for (OnlineProductConfig config : configs) {
                if (config.getEnabled() == 1 && config.getIsDeleted() == 0) {
                    List<String> configInsuranceTypes = parseInsuranceTypesFromConfig(config);
                    allInsuranceTypes.addAll(configInsuranceTypes);
                }
            }

            List<String> result = new ArrayList<>(allInsuranceTypes);
            logger.info("获取到险种类型: {}, 企业ID: {}", result, enterpriseId);
            
            // 将结果存入缓存
            redisUtil.set(cacheKey, result, CACHE_EXPIRE_TIME);

            return result;

        } catch (Exception e) {
            logger.error("根据企业客户ID获取险种类型失败，企业ID: {}", enterpriseId, e);
            throw new RuntimeException("获取险种类型失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从配置中解析险种类型
     * @param config 线上产品配置
     * @return 险种类型列表
     */
    private List<String> parseInsuranceTypesFromConfig(OnlineProductConfig config) {
        if (StringUtils.hasText(config.getInsuranceTypes())) {
            try {
                return objectMapper.readValue(config.getInsuranceTypes(),
                        new TypeReference<List<String>>() {});
            } catch (Exception e) {
                logger.warn("解析配置险种类型失败，配置ID: {}, 险种类型字符串: {}",
                        config.getId(), config.getInsuranceTypes(), e);
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }
}

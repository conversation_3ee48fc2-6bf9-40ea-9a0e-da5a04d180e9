package com.kbao.kbcelms.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtils {

    public static String getDtType(String m, String n) {
        if (m == null && n == null) return null;
        if (m == null) return n;
        if (n == null) return m;

        return m.compareTo(n) >= 0 ? n : m;
    }

    /**
     * 通用的下划线转驼峰方法
     */
    public static String underscoreToCamelCase(String underscore) {
        if (underscore == null || underscore.isEmpty()) {
            return underscore;
        }
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        for (int i = 0; i < underscore.length(); i++) {
            char c = underscore.charAt(i);
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        return result.toString();
    }

    /**
     * 将Long类型金额转换为带万、亿单位的字符串显示
     *
     * @param amount 金额（单位：元）
     * @return 格式化后的金额字符串
     *
     * 示例：
     * 1000 -> "1000元"
     * 12000 -> "1.2万元"
     * 100000000 -> "1亿元"
     * 123456789 -> "1.23亿元"
     */
    public static String formatAmount(Long amount) {
        if (amount == null || amount == 0) {
            return null;
        }
        // 负数处理
        boolean isNegative = amount < 0;
        long absAmount = Math.abs(amount);

        String result;
        if (absAmount < 10000) {
            // 小于1万，直接显示
            result = absAmount + "元";
        } else if (absAmount < 100000000) {
            // 1万到1亿之间，显示万
            BigDecimal wan = new BigDecimal(absAmount).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP);
            // 去掉末尾的0
            String wanStr = wan.stripTrailingZeros().toPlainString();
            result = wanStr + "万元";
        } else {
            // 1亿及以上，显示亿
            BigDecimal yi = new BigDecimal(absAmount).divide(new BigDecimal(100000000), 2, RoundingMode.HALF_UP);
            // 去掉末尾的0
            String yiStr = yi.stripTrailingZeros().toPlainString();
            result = yiStr + "亿元";
        }
        return isNegative ? "-" + result : result;
    }

    /**
     * 获取字符串中的第一个完整数字
     *
     * @param str 输入字符串
     * @return 第一个完整数字，如果没有找到数字则返回null
     *
     * 示例：
     * "1000万元" -> "1000"
     * "预算500万" -> "500"
     * "abc123def456" -> "123"
     * "12.5万元" -> "12.5"
     * "无数字" -> null
     * "" -> null
     * null -> null
     */
    public static String extractFirstNumber(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }

        // 匹配第一个完整的数字（包括小数）
        Pattern pattern = Pattern.compile("\\d+(?:\\.\\d+)?");
        Matcher matcher = pattern.matcher(str);

        if (matcher.find()) {
            return matcher.group();
        }

        return null;
    }
}

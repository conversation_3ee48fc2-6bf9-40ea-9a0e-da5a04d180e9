package com.kbao.kbcelms.util;

import com.kbao.tool.util.EmptyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ELMS项目统一Redis键生成工具类
 * 解决不同项目（API和WEB）因applicationName不同导致Redis键不一致的问题
 * 
 * <AUTHOR>
 * @date 2025/1/20
 */
public class ElmsRedisKeyUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ElmsRedisKeyUtil.class);
    
    /**
     * 统一的应用名称前缀，确保API和WEB项目生成相同的Redis键
     */
    private static final String UNIFIED_APP_NAME = "kbc-elms";
    
    /**
     * Redis键分隔符
     */
    private static final String SEPARATOR = ":";
    
    /**
     * 生成统一的Redis键
     * 格式：kbc-elms:param1:param2:...
     * 
     * @param params 键参数，可变参数
     * @return 生成的Redis键
     */
    public static String generateKey(String... params) {
        if (EmptyUtils.isEmpty(params)) {
            logger.warn("生成Redis键时参数为空");
            return UNIFIED_APP_NAME;
        }
        
        StringBuilder keyBuilder = new StringBuilder(UNIFIED_APP_NAME);
        
        for (String param : params) {
            if (EmptyUtils.isNotEmpty(param)) {
                keyBuilder.append(SEPARATOR).append(param);
            }
        }
        
        String key = keyBuilder.toString();
        logger.debug("生成Redis键: {}", key);
        return key;
    }
}
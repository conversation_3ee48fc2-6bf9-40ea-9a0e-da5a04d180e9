package com.kbao.kbcelms.enterpriseconfirmation.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.bascode.entity.BasCode;
import com.kbao.kbcelms.bascode.service.BasCodeService;
import com.kbao.kbcelms.bsc.BscUtil;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseScaleEnumVO;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseScaleMapsVO;import com.kbao.kbcelms.enterprise.type.bean.ScaleEnumItem;
import com.kbao.kbcelms.enterprise.type.service.EnterpriseTypeService;
import com.kbao.kbcelms.enterpriseconfirmation.bean.*;
import com.kbao.kbcelms.enterpriseconfirmation.dao.EnterpriseConfirmationMapper;
import com.kbao.kbcelms.enterpriseconfirmation.entity.ConfirmationProcessRecord;
import com.kbao.kbcelms.enterpriseconfirmation.entity.EnterpriseConfirmation;
import com.kbao.kbcelms.formConfig.service.FormConfigService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.service.IndustryService;
import com.kbao.kbcelms.opportunity.service.OpportunityService;
import com.kbao.kbcelms.ucs.service.ElmsUcsClientService;
import com.kbao.kbcelms.userorg.service.UserOrgService;
import com.kbao.kbcelms.util.StringUtils;
import com.kbao.kbcucs.agent.model.AgentFullInfoVO;
import com.kbao.kbcucs.context.RequestContext;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 企业确权Service
 * @Date 2025-08-20
 */
@Service
public class EnterpriseConfirmationService extends BaseSQLServiceImpl<EnterpriseConfirmation, Integer, EnterpriseConfirmationMapper> {

    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;
    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;
    @Autowired
    private ConfirmationProcessRecordService confirmationProcessRecordService;
    @Autowired
    private UserOrgService userOrgService;
    @Autowired
    private ElmsUcsClientService elmsUcsClientService;
    @Autowired
    private OpportunityService opportunityService;
    @Autowired
    private FormConfigService formConfigService;
    @Autowired
    private BasCodeService basCodeService;
    @Autowired
    private EnterpriseTypeService enterpriseTypeService;
    @Autowired
    private IndustryService industryService;

    /**
     * 分页查询企业确权列表（仅查询重复记录）
     */
    public PageInfo<EnterpriseConfirmationListVo> getConfirmationList(PageRequest<EnterpriseConfirmationSearchVo> pageRequest) {
        // TODO: 获取当前登录用户所在机构信息，后续补充
        String userId = ElmsContext.getUser().getUserId();
        List<String> orgList = userOrgService.getOrgListByUserId(userId);
        if (EmptyUtils.isEmpty(orgList)) {
            return new PageInfo<>();
        }
        boolean isAdmin = "dept".equals(orgList.get(0));
        EnterpriseConfirmationSearchVo param = pageRequest.getParam();
        if (!isAdmin) {
            param.setOrgList(orgList); // 只查询当前机构的记录
        }
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<EnterpriseConfirmationListVo> list = mapper.getConfirmationList(param);

        // 计算处理状态描述和超时天数，并根据搜索条件过滤
        Date now = new Date();
        List<EnterpriseConfirmationListVo> filteredList = new ArrayList<>();

        for (EnterpriseConfirmationListVo vo : list) {
            calculateProcessStatus(vo, now);

            // 根据处理状态筛选
            if (param.getProcessStatus() != null) {
                if (param.getProcessStatus() == 0 && vo.getProcessStatus() == 0 && vo.getOverdueDays() == null) {
                    // 未处理且未超时
                    filteredList.add(vo);
                } else if (param.getProcessStatus() == 1 && vo.getProcessStatus() == 1) {
                    // 已处理
                    filteredList.add(vo);
                } else if (param.getProcessStatus() == 2 && vo.getProcessStatus() == 0 && vo.getOverdueDays() != null) {
                    // 已超时
                    filteredList.add(vo);
                }
            } else {
                // 不筛选，显示所有
                filteredList.add(vo);
            }
        }

        // 重新构建分页信息
        PageInfo<EnterpriseConfirmationListVo> pageInfo = new PageInfo<>(list);
        pageInfo.setList(filteredList);
        return pageInfo;
    }

    /**
     * 获取企业确权详情
     */
    public EnterpriseConfirmationDetailVo getConfirmationDetail(Integer id) {
        EnterpriseConfirmation confirmation = super.selectByPrimaryKey(id);
        if (confirmation == null) {
            throw new BusinessException("企业确权记录不存在");
        }
        String legalCode = confirmation.getLegalCode();
        EnterpriseConfirmationDetailVo result = new EnterpriseConfirmationDetailVo();
        result.setIsProcessed(confirmation.getIsProcessed());

        // 获取企业基本信息
        EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.findByCreditCode(confirmation.getCreditCode());
        EnterpriseBaseInfoVo baseInfoVo = new EnterpriseBaseInfoVo();
        baseInfoVo.setName(basicInfo.getName());
        baseInfoVo.setCreditCode(basicInfo.getCreditCode());
        baseInfoVo.setAnnualIncome(confirmation.getAnnualIncome());
        baseInfoVo.setScale(basicInfo.getScale());
        baseInfoVo.setCity(basicInfo.getCity());
        baseInfoVo.setStaffScale(confirmation.getStaffScale());
        String districtCode = basicInfo.getDistrictCode();
        if (EmptyUtils.isNotEmpty(districtCode)) {
            BasCode byCode = basCodeService.getByCode(districtCode);
            if (byCode != null) {
                baseInfoVo.setCity(byCode.getFullPathName());
            }
        }
        result.setEnterpriseBasicInfo(baseInfoVo);

        // 获取创建企业时的录入信息
        List<GenAgentEnterprise> agentEnterpriseList = genAgentEnterpriseService.getByCreditCode(confirmation.getCreditCode());
        GenAgentEnterprise enterprise = new GenAgentEnterprise();
        for (int i = 0; i < agentEnterpriseList.size(); i++) {
            GenAgentEnterprise ae = agentEnterpriseList.get(i);
            if (legalCode.equals(ae.getLegalCode())) {
                enterprise = ae;
                agentEnterpriseList.remove(i);
                break;
            }
        }
        Map<String,String> typeMap = enterpriseTypeService.getEnterpriseTypeMap();
        EnterpriseScaleMapsVO enterpriseScaleMaps = enterpriseTypeService.getEnterpriseScaleMaps();
        Map<String,String> employeeScales = enterpriseScaleMaps.getEmployeeScales();
        Map<String,String> revenueScales = enterpriseScaleMaps.getRevenueScales();
        AgentFullInfoVO agentInfo = this.getUserByAgentCode(enterprise.getAgentCode());
        AgentEnterpriseMainVo mainVo = new AgentEnterpriseMainVo();
        BeanUtils.copyProperties(enterprise, mainVo);
        if (agentInfo != null) {
            mainVo.setCreator(agentInfo.getAgentName());
            mainVo.setAreaCenterName(agentInfo.getAreaCenterName());
            mainVo.setTradingCenterName(agentInfo.getTradingCenterName());
            mainVo.setBpGroupName(agentInfo.getBpGroupName());
        }
        mainVo.setDtType(typeMap.get(enterprise.getDtType()));
        mainVo.setStaffScale(employeeScales.get(enterprise.getStaffScale()));
        mainVo.setAnnualIncome(revenueScales.get(enterprise.getAnnualIncome()));
        String mainDistrictCode = mainVo.getDistrictCode();
        if (EmptyUtils.isNotEmpty(mainDistrictCode)) {
            BasCode byCode = basCodeService.getByCode(mainDistrictCode);
            if (byCode != null) {
                mainVo.setCity(byCode.getFullPathName());
            }
        }
        String categoryCode = mainVo.getCategoryCode();
        if (EmptyUtils.isNotEmpty(categoryCode)) {
            Industry industry = industryService.selectByCode(categoryCode);
            if (industry != null) {
                mainVo.setCategoryName(industry.getFullName());
            }
        }
        result.setGenAgentEnterprise(mainVo);

        Map<Integer,String> insureTypeMap = formConfigService.getInsureTypeMap();
        // 获取重复企业创建人信息列表
        List<AgentEnterpriseOtherVo> otherList = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(agentEnterpriseList)) {
            List<Integer> agentEnterpriseIds = agentEnterpriseList.stream().map(GenAgentEnterprise::getId).collect(Collectors.toList());
            List<AgentEnterpriseOtherDto> agentEnterpriseOtherList = opportunityService.getOpportunityNum(agentEnterpriseIds);
            Map<Integer,AgentEnterpriseOtherDto> opportunityOtherMap = new HashMap<>();
            if (EmptyUtils.isNotEmpty(agentEnterpriseOtherList)) {
                opportunityOtherMap = agentEnterpriseOtherList.stream().collect(Collectors.toMap(AgentEnterpriseOtherDto::getAgentEnterpriseId, Function.identity(), (v1, v2) -> v1));
            }
            for (GenAgentEnterprise e : agentEnterpriseList) {
                AgentEnterpriseOtherVo otherVo = new AgentEnterpriseOtherVo();
                AgentFullInfoVO otherAgentInfo = this.getUserByAgentCode(e.getAgentCode());
                if (otherAgentInfo != null) {
                    otherVo.setCreator(otherAgentInfo.getAgentName());
                    otherVo.setAreaCenterName(otherAgentInfo.getAreaCenterName());
                    otherVo.setTradingCenterName(otherAgentInfo.getTradingCenterName());
                    otherVo.setBpGroupName(otherAgentInfo.getBpGroupName());
                }
                otherVo.setIsDeleted(e.getIsDeleted());
                otherVo.setCreateTime(e.getCreateTime());
                AgentEnterpriseOtherDto dto = opportunityOtherMap.get(e.getId());
                if (dto != null) {
                    otherVo.setOpportunityNum(dto.getOpportunityNum());
                    String underwayEmployeeInsureTypes = this.getInsureTypeNames(insureTypeMap, dto.getUnderwayEmployeeInsureTypes(), dto.getUnderwayGeneralInsureTypes());
                    otherVo.setUnderwayInsureTypes(underwayEmployeeInsureTypes);
                    String lockEmployeeInsureTypes = this.getInsureTypeNames(insureTypeMap, dto.getLockEmployeeInsureTypes(), dto.getLockGeneralInsureTypes());
                    otherVo.setLockInsureTypes(lockEmployeeInsureTypes);
                    String issuedEmployeeInsureTypes = this.getInsureTypeNames(insureTypeMap, dto.getIssuedEmployeeInsureTypes(), dto.getIssuedGeneralInsureTypes());
                    otherVo.setIssuedInsureTypes(issuedEmployeeInsureTypes);
                }
                otherList.add(otherVo);
            }
        }
        result.setDuplicateCreators(otherList);

        // 获取处理记录
        List<ConfirmationProcessRecord> processRecords = confirmationProcessRecordService.getByCreditCode(confirmation.getCreditCode());
        result.setProcessRecords(processRecords);

        return result;
    }

    private AgentFullInfoVO getUserByAgentCode(String agentCode) {
        BscUtil.setFeignRequestHeader();
        Result<AgentFullInfoVO> agentFullInfo = elmsUcsClientService.getAgentFullInfo(agentCode);
        if (agentFullInfo == null) {
            return null;
        }
        return agentFullInfo.getDatas();
    }

    private String getInsureTypeNames(Map<Integer,String> insureTypeMap, String insureTypeIds, String names) {
        List<Integer> idList = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(insureTypeIds)) {
            String[] insureTypeIdArr = insureTypeIds.split(",");
            idList = Arrays.stream(insureTypeIdArr).filter(EmptyUtils::isNotEmpty).map(Integer::parseInt).collect(Collectors.toList());
        }
        if (EmptyUtils.isNotEmpty(names)) {
            names = Arrays.stream(names.split(",")).filter(EmptyUtils::isNotEmpty).collect(Collectors.joining(","));
        }
        if (EmptyUtils.isEmpty(insureTypeMap) || EmptyUtils.isEmpty(idList)) {
            return names;
        }
        StringBuilder insureTypeName = new StringBuilder();
        for (Integer id : idList) {
            String name = insureTypeMap.get(id);
            if (EmptyUtils.isEmpty(name)) {
                continue;
            }
            insureTypeName.append(insureTypeMap.get(id)).append(",");
        }
        if (EmptyUtils.isNotEmpty(names)) {
            insureTypeName.append(names);
        }
        if (insureTypeName.length() == 0) {
            return "";
        }
        return insureTypeName.substring(0, insureTypeName.length() - 1);
    }

    /**
     * 插入企业确权记录并检查重复
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertConfirmationRecord(EnterpriseBasicInfo basicInfo) {
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();

        // 创建确权记录
        EnterpriseConfirmation confirmation = new EnterpriseConfirmation();
        confirmation.setLegalCode(userInfo.getLegalCode());
        confirmation.setLegalName(userInfo.getLegalName());
        confirmation.setCreditCode(basicInfo.getCreditCode());
        confirmation.setEnterpriseName(basicInfo.getName());
        confirmation.setDistrictCode(basicInfo.getDistrictCode());
        confirmation.setCity(basicInfo.getCity());
        confirmation.setStaffScale(basicInfo.getStaffNumRange());
        confirmation.setAnnualIncome(StringUtils.formatAmount(basicInfo.getLatestBusinessIncome()));
        confirmation.setTenantId(userInfo.getTenantId());
        confirmation.setCreateTime(new Date());

        // 检查是否有相同统一信用代码的记录
        DuplicateInfoVo duplicateInfo = mapper.getDuplicateNum(userInfo.getLegalCode(), basicInfo.getCreditCode());
        if (duplicateInfo.getDuplicateLegalNum() == 0) {
            confirmation.setIsDuplicate(0);
            confirmation.setIsProcessed(0);
            confirmation.setLegalEnterpriseNum(1);
            mapper.insert(confirmation);
        } else {
            mapper.addLegalEnterpriseNum(userInfo.getLegalCode(), basicInfo.getCreditCode());
        }
        Integer enterpriseNum = duplicateInfo.getEnterpriseNum();
        if (enterpriseNum != null && enterpriseNum > 0) {
            mapper.batchUpdateDuplicateFlag(basicInfo.getCreditCode(), 1, 0);
        }
    }

    public void reduceLegalEnterpriseNum(String legalCode, String creditCode) {
        mapper.reduceLegalEnterpriseNum(legalCode, creditCode);
        mapper.resetDuplicateFlag(creditCode);
    }

    /**
     * 保存处理记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveProcessRecord(ProcessRecordRequest request) {
        // 验证企业确权记录是否存在
        EnterpriseConfirmation confirmation = super.selectByPrimaryKey(request.getConfirmationId());
        if (confirmation == null) {
            throw new BusinessException("企业确权记录不存在");
        }

        // 创建处理记录
        ConfirmationProcessRecord processRecord = new ConfirmationProcessRecord();
        processRecord.setCreditCode(confirmation.getCreditCode());
        processRecord.setRemark(request.getProcessRemark());
        processRecord.setProcessTime(new Date());

        // 获取当前用户信息
        String userId = ElmsContext.getUser().getUserId();
        String userName = ElmsContext.getUser().getNickName();
        processRecord.setUserId(userId);
        processRecord.setUserName(userName);
        processRecord.setLegalCode(confirmation.getLegalCode());
        processRecord.setLegalName(confirmation.getLegalName());
        processRecord.setTenantId(confirmation.getTenantId());

        // 保存处理记录
        confirmationProcessRecordService.insert(processRecord);

        // 更新企业确权记录的处理状态
        confirmation.setIsProcessed(1);
        super.updateByPrimaryKey(confirmation);
    }

    /**
     * 计算处理状态描述和超时天数
     */
    private void calculateProcessStatus(EnterpriseConfirmationListVo vo, Date now) {
        if (vo.getProcessStatus() == 1) {
            vo.setProcessStatusDesc("已处理");
            vo.setOverdueDays(null);
        } else {
            // 计算创建时间到现在的天数
            long diffInMillies = now.getTime() - vo.getCreateTime().getTime();
            long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);

            if (diffInDays > 14) {
                // 超时
                int overdueDays = (int) (diffInDays - 14);
                vo.setProcessStatusDesc("已超时（超时" + overdueDays + "天）");
                vo.setOverdueDays(overdueDays);
            } else {
                // 未处理
                vo.setProcessStatusDesc("未处理");
                vo.setOverdueDays(null);
            }
        }
    }
}

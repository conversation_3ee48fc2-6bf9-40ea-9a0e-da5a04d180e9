package com.kbao.kbcelms.opportunity.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbpm.process.vo.*;
import com.kbao.kbcbsc.adapter.UserClientAdapter;
import com.kbao.kbcbsc.organization.entity.Organization;
import com.kbao.kbcbsc.organization.model.OrgIdReq;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.user.model.UserIdReq;
import com.kbao.kbcelms.bpm.ElmsBpmWebService;
import com.kbao.kbcelms.bsc.BscClientService;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcelms.enterpriseconfirmation.bean.AgentEnterpriseOtherDto;
import com.kbao.kbcelms.enums.OpportunityCloseReasonEnum;
import com.kbao.kbcelms.enums.OpportunityStatusEnum;
import com.kbao.kbcelms.enums.OpportunityStepEnum;
import com.kbao.kbcelms.enums.RoleTypeEnum;
import com.kbao.kbcelms.formConfig.service.FormConfigService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.service.IndustryService;
import com.kbao.kbcelms.opportunity.dao.OpportunityMapper;
import com.kbao.kbcelms.opportunity.dto.OpportunityDetailQuery;
import com.kbao.kbcelms.opportunity.dto.OpportunityTodoQueryDTO;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.vo.*;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;
import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;
import com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder;
import com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService;
import com.kbao.kbcelms.opportunityprocess.entity.OpportunityProcess;
import com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService;
import com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog;
import com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityCloseReasonVO;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityCloseRequestVO;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityRestartRequestVO;
import com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember;
import com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService;
import com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService;
import com.kbao.kbcelms.role.entity.Role;
import com.kbao.kbcelms.role.service.RoleService;
import com.kbao.kbcelms.user.entity.User;
import com.kbao.kbcelms.user.service.UserService;
import com.kbao.kbcelms.usertenant.entity.UserTenant;
import com.kbao.kbcelms.usertenant.service.UserTenantService;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.StringUtil;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class OpportunityService extends BaseSQLServiceImpl<Opportunity, Integer,OpportunityMapper> {

    @Autowired
    ElmsBpmWebService elmsBpmWebService;

    @Autowired
    RoleService roleService;

    @Autowired
    UserTenantService userTenantService;

    @Autowired
    OpportunityProcessLogService opportunityProcessLogService;

    @Autowired
    UserService userService;

    @Autowired
    OpportunityProcessService opportunityProcessService;

    @Autowired
    OpportunityTeamService opportunityTeamService;

    @Autowired
    OpportunityTeamDivisionService opportunityTeamDivisionService;

    @Autowired
    OpportunityDetailService opportunityDetailService;

    @Autowired
    GenAgentEnterpriseService genAgentEnterpriseService;

    @Autowired
    private UserClientAdapter userClientAdapter;
    
    @Autowired
    BscClientService bscClientService;

    @Autowired
    private OpportunityOrderService opportunityOrderService;

    @Autowired
    private IndustryService industryService;

    @Autowired
    private FormConfigService formConfigService;

    /**
     * 解析processStep值，如果格式为"X-Y"，则将X赋值给status，Y赋值给processStep
     * @param reqVo 请求对象
     */
    private void parseProcessStepValue(PageRequest<OpportunityTodoQueryDTO> reqVo) {
        if (reqVo.getParam() != null && reqVo.getParam().getProcessStep() != null && reqVo.getParam().getProcessStep().contains("-")) {
            String processStepValue = reqVo.getParam().getProcessStep();
            reqVo.getParam().setProcessStep(null);
            String[] parts = processStepValue.split("-");
            try {
                // 第一个值给status
                Integer status = Integer.parseInt(parts[0]);
                reqVo.getParam().setStatus(status);
            } catch (NumberFormatException e) {
                // 如果解析失败，保持原值不变
            }
            if (parts.length == 2) {
                try {
                    // 第二个值回写到processStep
                    reqVo.getParam().setProcessStep(parts[1]);
                } catch (NumberFormatException e) {
                    // 如果解析失败，保持原值不变
                }
            }
        }
    }

    /**
     * 查询我的待处理任务（支持多种查询条件和分页）
     * @param reqVo 分页请求参数
     * @return 分页结果
     */
    public Result<PageInfo<OpportunityTodoVO>> getTodoTasksByUserAndCandidate(PageRequest<OpportunityTodoQueryDTO> reqVo) {
        parseProcessStepValue(reqVo);
        // 1. 先从BPM服务查询所有任务ID
        TaskQueryVO taskQuery = new TaskQueryVO();
        taskQuery.setTenantId(SysLoginUtils.getUser().getTenantId());
        taskQuery.setUserId(SysLoginUtils.getUserId());
        // 不设置分页，获取所有任务
        taskQuery.setPageNum(1);
        taskQuery.setPageSize(Integer.MAX_VALUE);

        Result<PageInfo<TaskVO>> tasks = elmsBpmWebService.getTodoTasksByUserAndCandidate(taskQuery);
        if (!ResultStatusEnum.isSuccess(tasks.getResp_code()) || EmptyUtils.isEmpty(tasks.getDatas().getList())) {
            return Result.succeedWith(new PageInfo<>());
        }

        // 2. 提取所有流程实例ID
        List<String> processInstanceIds = tasks.getDatas().getList().stream()
            .map(TaskVO::getProcessInstanceId)
            .collect(Collectors.toList());

        // 3. 构建查询参数
        OpportunityTodoQueryDTO queryDTO = reqVo.getParam();
        if (queryDTO == null) {
            queryDTO = new OpportunityTodoQueryDTO();
        }
        queryDTO.setTenantId(SysLoginUtils.getUser().getTenantId());
        queryDTO.setUserId(SysLoginUtils.getUserId());
        queryDTO.setProcessInstanceIds(processInstanceIds);

        // 4. 使用PageHelper进行分页查询
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        List<OpportunityTodoVO> todoTasks = mapper.selectTodoTasksWithPagination(queryDTO);
        
        // 5. 设置流程步骤标签
        todoTasks.forEach(obj -> {
            if (obj.getProcessStep() != null) {
                obj.setProcessStep(OpportunityStepEnum.getByValue(obj.getProcessStep()).getLabel());
            }
        });

        // 6. 构建分页结果
        PageInfo<OpportunityTodoVO> pageInfo = new PageInfo<>(todoTasks);
        
        return Result.succeedWith(pageInfo);
    }

    /**
     * 查询我的待领取任务（支持多种查询条件和分页）
     * @param reqVo 分页请求参数
     * @return 分页结果
     */
    public Result<PageInfo<OpportunityTodoVO>> getToGetTasksByUserAndCandidate(PageRequest<OpportunityTodoQueryDTO> reqVo) {
        parseProcessStepValue(reqVo);
        TaskQueryVO taskQuery = new TaskQueryVO();
        taskQuery.setTenantId(SysLoginUtils.getUser().getTenantId());
        taskQuery.setCandidateUserId(SysLoginUtils.getUserId());
        // 不设置分页，获取所有任务
        taskQuery.setPageNum(1);
        taskQuery.setPageSize(Integer.MAX_VALUE);
        
        List<Role> roles = roleService.getRolesByUserId(SysLoginUtils.getUserId());
        //判断是否包含分公司统筹角色
        boolean isBranchCoordinator = roles.stream()
                .anyMatch(role -> role.getRoleType().equals(RoleTypeEnum.BRANCH_GM.getCode()));
        taskQuery.setCandidateGroupIdIn(
                roles.stream()
                        .map(Role::getId)
                        .filter(Objects::nonNull)
                        .map(String::valueOf)
                        .collect(Collectors.toList())
        );
        //分公司统筹角色，则查询所有分公司统筹角色
        if (isBranchCoordinator) {
            taskQuery.getCandidateGroupIdIn().add("org_type_all");
        }
        // 查询所有分公司统筹角色
        Map<String, Object> param = new HashMap<>();
        param.put("userId", SysLoginUtils.getUserId());
        param.put("tenantId", SysLoginUtils.getUser().getTenantId());
        List<UserTenant> userTenants = userTenantService.selectByParam(param);
        String organCodePath = userTenants.get(0).getOrganCodePath();
        String[] organCodes = organCodePath.split("/");
        for (String organCode : organCodes) {
            taskQuery.getCandidateGroupIdIn().add("org_type_"+organCode);
        }
        
        Result<PageInfo<TaskVO>> tasks = elmsBpmWebService.getTodoTasksByUserAndCandidate(taskQuery);
        if (!ResultStatusEnum.isSuccess(tasks.getResp_code()) || EmptyUtils.isEmpty(tasks.getDatas().getList())) {
            return Result.succeedWith(new PageInfo<>());
        }
        
        // 提取所有流程实例ID
        List<String> processInstanceIds = tasks.getDatas().getList().stream()
                .map(TaskVO::getProcessInstanceId)
                .collect(Collectors.toList());
        
        // 构建查询参数
        OpportunityTodoQueryDTO queryDTO = reqVo.getParam();
        if (queryDTO == null) {
            queryDTO = new OpportunityTodoQueryDTO();
        }
        queryDTO.setTenantId(SysLoginUtils.getUser().getTenantId());
        queryDTO.setUserId(SysLoginUtils.getUserId());
        queryDTO.setProcessInstanceIds(processInstanceIds);
        
        // 使用PageHelper进行分页查询
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        List<OpportunityTodoVO> todoTasks = mapper.selectTodoTasksWithPagination(queryDTO);
        
        // 设置流程步骤标签
        todoTasks.forEach(obj -> {
            if (obj.getProcessStep() != null) {
                obj.setProcessStep(OpportunityStepEnum.getByValue(obj.getProcessStep()).getLabel());
            }
        });
        
        // 构建分页结果
        PageInfo<OpportunityTodoVO> pageInfo = new PageInfo<>(todoTasks);
        
        return Result.succeedWith(pageInfo);
    }

    /**
     * 分公司领取任务
     * @param opportunityId 机会ID
     */
    public void acceptTask(Integer opportunityId) {
        // 查询机会信息并设置提交时间
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity != null) {
            // 查询机会明细并设置提交时间
            Map<String, Object> param = new HashMap<>();
            param.put("opportunityId", opportunityId);
            param.put("tenantId", opportunity.getTenantId());
            List<OpportunityDetail> details = opportunityDetailService.selectByParam(param);
            OpportunityDetail detail = null;
            if (EmptyUtils.isEmpty(details))  {
              throw new BusinessException("机会明细数据异常！");
            }
            detail = details.get(0);
            if (detail.getSubmitTime() == null) {
                detail.setSubmitTime(new Date());
            }
            detail.setCoordinationAcceptTime(new Date());
            opportunityDetailService.updateByPrimaryKey(detail);

            opportunity.setProcessStep(OpportunityStepEnum.FOLLOW_UP.getValue());
            opportunity.setUpdateId(SysLoginUtils.getUserId());
            opportunity.setUpdateTime(new Date());
            this.updateByPrimaryKey(opportunity);

        }
        String processInstanceId = opportunityProcessService.selectByPrimaryKey(opportunity.getCurrentProcessId()).getBpmProcessId();
        //设置机会统筹人员
        opportunity.setCoordinator(SysLoginUtils.getUserId());
        this.updateByPrimaryKeySelective(opportunity);

        TaskUserUpdateVO vo = new TaskUserUpdateVO();
        vo.setProcessInstanceId(processInstanceId);
        vo.setAssignees(new ArrayList<>());
        vo.getAssignees().add(SysLoginUtils.getUserId());
        Result<List<TaskVO>> tasks = elmsBpmWebService.setTaskDealUserById(vo);
        TaskVO task = ResultStatusEnum.isSuccess(tasks.getResp_code())?tasks.getDatas().get(0):null;
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setCreateTime(new Date());
        oplog.setTenantId(SysLoginUtils.getUser().getTenantId());
        oplog.setProcessId(task.getProcessInstanceId());
        oplog.setOperatorId(SysLoginUtils.getUserId());
        oplog.setTargetId(SysLoginUtils.getUserId());
        oplog.setOperatorDesc(opportunityProcessLogService.buildOperatorDesc("分公司统筹", SysLoginUtils.getUser().getNickName(), SysLoginUtils.getUser().getUserName(), "领取了任务", null,null, null, null, null));
        opportunityProcessLogService.insert(oplog);
    }

    /**
     * 设置分公司统筹，任务转交其他统筹
     * @param opportunityId 机会ID
     * @param assignee 指派用户ID
     * @param assigneeRoleType 指派用户角色类型
     * @param assigneeOrg 指派用户机构
     */
    public void setBranchCoordination(Integer opportunityId, String assignee, Integer assigneeRoleType, String assigneeOrg) {
        TaskUserUpdateVO vo = new TaskUserUpdateVO();
        Opportunity oppo = this.selectByPrimaryKey(opportunityId);
        String processInstanceId = opportunityProcessService.selectByPrimaryKey(oppo.getCurrentProcessId()).getBpmProcessId();
        vo.setProcessInstanceId(processInstanceId);
        vo.setAssignees(new ArrayList<>());
        vo.getAssignees().add(assignee);
        Result<List<TaskVO>> tasks = elmsBpmWebService.setTaskDealUserById(vo);
        TaskVO task = ResultStatusEnum.isSuccess(tasks.getResp_code()) ? tasks.getDatas().get(0) : null;
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setProcessId(task.getProcessInstanceId());
        oplog.setOperatorId(SysLoginUtils.getUserId());
        oplog.setTargetId(assignee);
        //更新项目统筹人员
        Opportunity opportunity = new Opportunity();
        opportunity.setId(opportunityId);
        opportunity.setCoordinator(assignee);
        this.updateByPrimaryKeySelective(opportunity);
        //根据assignee查询对应userId的用户信息
        User userInfo = userService.getUserInfoByUserId(assignee);
        //北京分公司统筹 王年金（wangnj）指派机会给总公司统筹 张团财（zhangtc）
        oplog.setOperatorDesc(opportunityProcessLogService.buildOperatorDesc("分公司统筹", SysLoginUtils.getUser().getNickName(), SysLoginUtils.getUser().getUserName(), "将机会指派给",null, assigneeOrg, RoleTypeEnum.getNameByCode(assigneeRoleType), userInfo.getNickName(), userInfo.getBscUseName()));
        opportunityProcessLogService.insert(oplog);
        //设置统筹指定时间
        updateAssignCoordinationTime(opportunityId,DateUtils.dateTime2Str(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 设置项目经理
     * @param opportunityId 机会ID
     * @param businessTenantId 业务租户ID
     * @param assignee 指派用户ID
     * @param assigneeRoleType 指派用户角色类型
     * @param assigneeOrg 指派用户机构
     */
    public void setOpportunityProjecter(Integer opportunityId,String businessTenantId, String assignee, Integer assigneeRoleType, String assigneeOrg) {
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        String processInstanceId = opportunityProcessService.selectByPrimaryKey(opportunity.getCurrentProcessId()).getBpmProcessId();
        //修改机会对应租户id
        if (!opportunity.getTenantId().equals(businessTenantId)) {
            // updateOpportunityTenantId方法会返回更新后的opportunity对象，避免重复查询
            opportunity = updateOpportunityTenantId(opportunityId,businessTenantId,"指派项目经理变更租户归属");
        }

        // 设置项目经理相关信息
        UserTenant pm = userTenantService.getByUserIdAndTenantId(assignee, businessTenantId);
        opportunity.setProjectManager(assignee);
        opportunity.setProjectOrgCode(pm.getOrganCode());
        opportunity.setProjectOrgName(pm.getOrganName());
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        this.updateByPrimaryKeySelective(opportunity);

        ProcessInstanceIdVO processInstanceIdVO = new ProcessInstanceIdVO();
        processInstanceIdVO.setProcessInstanceId(processInstanceId);
        Result<List<TaskVO>> result = elmsBpmWebService.getCurrentTaskInfoById(processInstanceIdVO);
        TaskVO task = result.getDatas().get(0);
        if (EmptyUtils.isNotEmpty(task.getAssignee())){
            if (!SysLoginUtils.getUserId().equals(task.getAssignee())){
                throw new BusinessException("当前用户非任务领取人，不得指派项目经理");
            }
        }
        //执行并完成统筹节点任务
        TaskCompleteVO tcvo = new TaskCompleteVO();
        tcvo.setTaskId(task.getId());
        tcvo.setProcessInstanceId(processInstanceId);
        tcvo.setAssignee(SysLoginUtils.getUserId());
        elmsBpmWebService.completeCurrentTasksByProcessInstanceId(tcvo);
        //指派后续任务为项目经理
        TaskUserUpdateVO vo = new TaskUserUpdateVO();
        vo.setProcessInstanceId(processInstanceId);
        vo.setAssignees(new ArrayList<>());
        vo.getAssignees().add(assignee);
        Result<List<TaskVO>> tasks = elmsBpmWebService.setTaskDealUserById(vo);
        task = ResultStatusEnum.isSuccess(tasks.getResp_code())?tasks.getDatas().get(0):null;
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setProcessId(task.getProcessInstanceId());
        oplog.setOperatorId(SysLoginUtils.getUserId());
        oplog.setTargetId(assignee);
        oplog.setTenantId(businessTenantId);
        oplog.setCreateTime(new Date());
        //根据assignee查询对应userId的用户信息
        User userInfo = userService.getUserInfoByUserId(assignee);
        String assigneeOrgName = "";
        if (EmptyUtils.isNotEmpty(assigneeOrg)){
            OrgIdReq orgreq = new OrgIdReq();
            orgreq.setOrgCode(assigneeOrg);
            Result<Organization> res = bscClientService.getOrgDetail(orgreq);
            if (res!=null &&  res.getDatas()!=null){
                assigneeOrgName = res.getDatas().getOrgName();
            }
        }
        //北京分公司统筹 王年金（wangnj）指派机会给总公司统筹 张团财（zhangtc）
        oplog.setOperatorDesc(opportunityProcessLogService.buildOperatorDesc("分公司统筹", SysLoginUtils.getUser().getNickName(), SysLoginUtils.getUser().getUserName(), "将机会指派给",null, assigneeOrgName, RoleTypeEnum.getNameByCode(assigneeRoleType), userInfo.getNickName(), userInfo.getBscUseName()));
        oplog.setIsNodeCompleteLog(1);
        opportunityProcessLogService.insert(oplog);

        // 构造项目经理对象并初始化项目团队
        List<OpportunityTeamMember> members = new ArrayList<>();
        OpportunityTeamMember projectManager = new OpportunityTeamMember();
        projectManager.setUserId(userInfo.getUserId());
        projectManager.setNickname(userInfo.getNickName());
        projectManager.setOpportunityId(opportunityId);
        // 根据角色类型设置角色ID，这里假设项目经理的角色ID为3，实际需要根据业务逻辑调整
        projectManager.setRoleType(assigneeRoleType);
        projectManager.setIsDefault(1);
        projectManager.setIsAgent(0);
        members.add(projectManager);

        OpportunityTeamMember agent = new OpportunityTeamMember();
        agent.setUserId(opportunity.getAgentCode());
        agent.setNickname(opportunity.getAgentName());
        agent.setUserName(opportunity.getAgentCode());
        agent.setOrganNamePath(opportunity.getLegalName()+"\\"+opportunity.getTradingCenterName());
        agent.setOpportunityId(opportunityId);
        agent.setRoleType(RoleTypeEnum.OPPO_AGENT.getCode());
        agent.setIsAgent(1);
        agent.setIsDefault(1);
        members.add(agent);

        // 调用项目团队初始化方法
        opportunityTeamService.init(members, businessTenantId);
        // 更新项目经理设置时间
        updateAssignProjectManagerTime(opportunityId, businessTenantId, DateUtils.dateTime2Str(new Date(),null));
    }

    /**
     * 统一修改机会相关数据的租户ID
     * @param opportunityId 机会ID
     * @param newTenantId 新的租户ID
     * @param reason 修改原因
     * @return 更新后的Opportunity对象
     */
    public Opportunity updateOpportunityTenantId(Integer opportunityId, String newTenantId, String reason) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(newTenantId)) {
            throw new BusinessException("新租户ID不能为空");
        }

        // 0.修改bpmn 流程实例租户归属
        Map<String, Object> processParam = new HashMap<>();
        processParam.put("opportunityId", opportunityId);
        List<OpportunityProcess> ops = opportunityProcessService.selectByParam(processParam);
        if(EmptyUtils.isNotEmpty(ops)){
            ProcessTenantUpdateVO updateVo = new ProcessTenantUpdateVO();
            updateVo.setProcessInstanceId(ops.get(0).getBpmProcessId());
            updateVo.setNewTenantId(newTenantId);
            try{
                Result<Boolean> res = elmsBpmWebService.updateProcessInstanceTenant(updateVo);
                if (!ResultStatusEnum.isSuccess(res.getResp_code())){
                    throw new BusinessException(res.getResp_msg());
                }else if(res.getDatas()==null || !res.getDatas()){
                    throw new BusinessException("流程数据异常");
                }
            }catch (Exception e){
                throw new BusinessException(e.getMessage());
            }
        }

        // 1. 修改机会表的租户ID
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }
        String oldTenantId = opportunity.getTenantId();
        opportunity.setTenantId(newTenantId);
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        this.updateByPrimaryKey(opportunity);
        
        // 2. 修改机会流程关联表的租户ID
        processParam = new HashMap<>();
        processParam.put("opportunityId", opportunityId);
        // 这里需要注入OpportunityProcessService来修改流程关联表
        opportunityProcessService.updateTenantIdByOpportunityId(opportunityId, newTenantId);
        
        // 3. 修改机会流程日志表的租户ID
        Map<String, Object> logParam = new HashMap<>();
        logParam.put("opportunityId", opportunityId);
        List<OpportunityProcessLog> logs = opportunityProcessLogService.selectByParam(logParam);
        for (OpportunityProcessLog log : logs) {
            log.setTenantId(newTenantId);
            log.setUpdateId(SysLoginUtils.getUserId());
            log.setUpdateTime(new Date());
            opportunityProcessLogService.updateByPrimaryKey(log);
        }
        
        // 4. 修改机会详情表的租户ID
        OpportunityDetail opportunityDetail = opportunityDetailService.selectByOpportunityId(opportunityId, oldTenantId);
        if (opportunityDetail != null) {
            opportunityDetail.setTenantId(newTenantId);
            opportunityDetail.setUpdateId(SysLoginUtils.getUserId());
            opportunityDetail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(opportunityDetail);
        }

        // 5. 修改项目团队成员表的租户ID
        opportunityTeamService.updateTenantIdByOpportunityId(opportunityId, newTenantId);

        // 6. 修改项目团队分工表的租户ID
        opportunityTeamDivisionService.updateTenantIdByOpportunityId(opportunityId, newTenantId);

        // 7. 记录租户ID变更日志
        String operatorDesc = opportunityProcessLogService.buildOperatorDesc(null, SysLoginUtils.getUser().getNickName(), SysLoginUtils.getUser().getUserName(), "租户ID从 " + oldTenantId + " 变更为 " + newTenantId + "，原因：" + reason, null,null, null, null, null);
        createProcessLog(opportunityId, newTenantId, operatorDesc, null, null, null);

        // 8. 返回更新后的Opportunity对象
        return opportunity;
    }
    
    /**
     * 暂停机会
     * @param opportunityId 机会ID
     * @param reason 暂停原因
     */
    public void suspendOpportunity(Integer opportunityId, String reason) {
        if (EmptyUtils.isEmpty(reason)) {
            throw new BusinessException("暂停原因不能为空");
        }
        
        // 查询机会信息并检查状态
        Opportunity opportunity = validateAndGetOpportunity(opportunityId);
        Integer currentStatus = opportunity.getStatus();
        if (OpportunityStatusEnum.SUSPENDED.getCode().equals(currentStatus)) {
            throw new BusinessException("机会已经处于暂停状态");
        }
        if (OpportunityStatusEnum.TERMINATED.getCode().equals(currentStatus)) {
            throw new BusinessException("已终止的机会不能暂停");
        }
        
        // 更新机会明细表的暂停时间
        updateSuspendTime(opportunityId, DateUtils.dateTime2Str(new Date(), "yyyy-MM-dd HH:mm:ss"));
        
        // 更新状态并记录日志
        String operatorDesc = opportunityProcessLogService.buildOperatorDesc(null, SysLoginUtils.getUser().getNickName(), SysLoginUtils.getUser().getUserName(), "机会暂停，原因：" + reason, null,null, null, null, null);
        updateOpportunityStatusAndLog(opportunityId, OpportunityStatusEnum.SUSPENDED.getCode(), 
            operatorDesc, null, reason);
    }
    
    /**
     * 恢复暂停的机会
     * @param opportunityId 机会ID
     * @param reason 恢复原因
     */
    public void resumeOpportunity(Integer opportunityId, String reason) {
        if (EmptyUtils.isEmpty(reason)) {
            reason = "暂停恢复";
        }
        
        // 查询机会信息并检查状态
        Opportunity opportunity = validateAndGetOpportunity(opportunityId);
        Integer currentStatus = opportunity.getStatus();
        if (!OpportunityStatusEnum.SUSPENDED.getCode().equals(currentStatus)) {
            throw new BusinessException("只有暂停状态的机会才能恢复");
        }
        
        // 更新状态并记录日志
        String operatorDesc = opportunityProcessLogService.buildOperatorDesc(null, SysLoginUtils.getUser().getNickName(), SysLoginUtils.getUser().getUserName(), "机会恢复，原因：" + reason, null, null,null, null, null);
        updateOpportunityStatusAndLog(opportunityId, OpportunityStatusEnum.SUBMITTED.getCode(), 
            operatorDesc, null, null);
        
        // 更新机会明细的提交时间
        updateOpportunityDetailSubmitTime(opportunityId, opportunity.getTenantId());
    }
    
    /**
     * 关闭机会
     * @param opportunityId 机会ID
     * @param reasonType 关闭原因类型
     * @param reasonDesc 关闭原因描述
     */
    public void closeOpportunity(Integer opportunityId, Integer reasonType, String reasonDesc) {
        if (EmptyUtils.isEmpty(reasonType)) {
            throw new BusinessException("关闭原因类型不能为空");
        }

        // 如果关闭原因为"机会已成交"，需要检查是否已填写保单号
        if (OpportunityCloseReasonEnum.OPPORTUNITY_WON.getCode().equals(reasonType)) {
            checkPolicyNumberForWonOpportunity(opportunityId);
        } else if(EmptyUtils.isEmpty(reasonDesc)) {
            throw new BusinessException("关闭原因描述不能为空");
        }

        // 查询机会信息并检查状态
        Opportunity opportunity = validateAndGetOpportunity(opportunityId);
        Integer currentStatus = opportunity.getStatus();
        if (OpportunityStatusEnum.TERMINATED.getCode().equals(currentStatus)) {
            throw new BusinessException("机会已经处于关闭状态");
        }


        // 更新状态并记录日志
        String operatorDesc = opportunityProcessLogService.buildOperatorDesc(null, SysLoginUtils.getUser().getNickName(), SysLoginUtils.getUser().getUserName(), "机会关闭", null,null, null, null, null);
        updateOpportunityStatusAndLog(opportunityId, OpportunityStatusEnum.TERMINATED.getCode(), 
            operatorDesc, reasonType, reasonDesc);
    }

    /**
     * 重启关闭的机会
     * @param opportunityId 机会ID
     * @param reason 重启原因描述
     * @param assignee 执行人
     * @param assigneeRoleType 角色类型
     */
    public void restartOpportunity(Integer opportunityId, String reason,String businessTenantId, String assignee, Integer assigneeRoleType, String assigneeOrg) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(reason)) {
            throw new BusinessException("重启原因不能为空");
        }

        // 查询机会信息
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }

        // 检查机会状态是否为暂停
        Integer currentStatus = opportunity.getStatus();
        if (!OpportunityStatusEnum.TERMINATED.getCode().equals(currentStatus)) {
            throw new BusinessException("只有关闭状态的机会才能重启");
        }

        //调用流程接口回滚机会步骤到 统筹跟进
        ProcessRollbackByPropertyVO rollback = new ProcessRollbackByPropertyVO();
        rollback.setPropertyName("stepType");
        rollback.setPropertyValue(OpportunityStepEnum.FOLLOW_UP.getValue());
        elmsBpmWebService.rollbackProcessByCustomProperty(rollback);

        // 记录恢复操作日志
        String operatorDesc = opportunityProcessLogService.buildOperatorDesc(null, SysLoginUtils.getUser().getNickName(), SysLoginUtils.getUser().getUserName(), "机会重启", null,null, null, null, null);
        createProcessLog(opportunityId, opportunity.getTenantId(), operatorDesc, null, reason, null);

        //设置项目经理
        setOpportunityProjecter(opportunityId,businessTenantId,assignee,assigneeRoleType,assigneeOrg);

        // 更新机会状态为已提交（恢复）
        opportunity.setStatus(OpportunityStatusEnum.SUBMITTED.getCode());
        opportunity.setCloseReasonType(null); // 清除关闭原因类型
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        opportunity.setProcessStep(OpportunityStepEnum.TEAM_UP.getValue());
        this.updateByPrimaryKey(opportunity);

        // 更新机会明细的提交时间
        updateOpportunityDetailSubmitTime(opportunityId, opportunity.getTenantId());
        //更新重启时间
        updateRestartTime(opportunityId,DateUtils.dateTime2Str(new Date(),null));
    }

    /**
     * 查询机会详情列表
     * @param query 查询参数
     * @return 机会详情列表
     */
    public List<OpportunityDetailVO> queryOpportunityDetails(OpportunityDetailQuery query) {
        return this.mapper.selectOpportunityDetails(query);
    }

    /**
     * 分页查询机会详情列表
     * @param pageRequest 分页参数
     * @return 机会详情列表
     */
    public PageInfo<OpportunityDetailVO> queryOpportunityDetailsPage(PageRequest<OpportunityDetailQuery> pageRequest) {
        pageRequest.getParam().setTenantId(SysLoginUtils.getUser().getTenantId());
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        Page<OpportunityDetailVO> page = (Page<OpportunityDetailVO>) this.mapper.selectOpportunityDetails( pageRequest.getParam());
        if(page!=null && page.getResult()!=null && page.getResult().size()>0){
            page.getResult().forEach(obj -> {
                if (obj.getProcessStep() != null) {
                    obj.setProcessStep(OpportunityStepEnum.getByValue(obj.getProcessStep()).getLabel());
                }
            });
        }

        return new PageInfo<>(page);
    }

    /**
     * 查询机会详情列表（用于导出）
     * @param query 查询参数
     * @return 机会详情列表
     */
    public List<OpportunityExportVO> queryOpportunityDetailsForExport(OpportunityDetailQuery query) {
        query.setTenantId(SysLoginUtils.getUser().getTenantId());
        List<OpportunityExportVO> dataList = this.mapper.selectOpportunityDetailsForExport(query);
        Map<Integer, String> insureTypeMap = formConfigService.getInsureTypeMap();
        // 转换为导出VO
        List<OpportunityExportVO> exportList = dataList.stream().map(vo -> convertToExportVO(vo, insureTypeMap)).collect(Collectors.toList());
        return exportList;
    }


    /**
     * 将OpportunityExportVO进行数据转换处理
     * @param exportVO 导出VO
     * @return 处理后的导出VO
     */
    private OpportunityExportVO convertToExportVO(OpportunityExportVO exportVO, Map<Integer, String> insureTypeMap) {
        // 机会类型转换
        if (EmptyUtils.isNotEmpty(exportVO.getOpportunityType())) {
            exportVO.setOpportunityType(OpportunityExportVO.getOpportunityTypeText(exportVO.getOpportunityType()));
        }
        if (EmptyUtils.isNotEmpty(exportVO.getStatusText())){
            exportVO.setStatusText(OpportunityStatusEnum.getNameByCode(Integer.valueOf(exportVO.getStatusText())));
        }
        if (EmptyUtils.isNotEmpty(exportVO.getHasHistoryPolicyText())){
            exportVO.setHasHistoryPolicyText(OpportunityExportVO.getIsYesNoText(exportVO.getHasHistoryPolicyText()));
        }
        if (EmptyUtils.isNotEmpty(exportVO.getIsBidText())){
            exportVO.setIsBidText(OpportunityExportVO.getIsYesNoText(exportVO.getIsBidText()));
        }

        if (EmptyUtils.isNotEmpty(exportVO.getEmployeeInsuranceType())) {
            exportVO.getEmployeeInsuranceType().split(",");
            // employeeInsuranceType 用,号分割后，从typeMap中取value 用，号拼接 回填字段
            String[] typeArr = exportVO.getEmployeeInsuranceType().split(",");
            List<String> typeNameList = new ArrayList<>();
            for (String type : typeArr) {
                if (EmptyUtils.isNotEmpty(type)) {
                    try {
                        Integer typeInt = Integer.valueOf(type);
                        String typeName = insureTypeMap.get(typeInt);
                        if (typeName != null) {
                            typeNameList.add(typeName);
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无法转换的类型
                    }
                }
            }
            if (EmptyUtils.isNotEmpty(exportVO.getGeneralInsuranceType())){
                typeNameList.add(0,exportVO.getGeneralInsuranceType());
            }
            exportVO.setEmployeeInsuranceType(String.join(",", typeNameList));
        }

        return exportVO;
    }


    /**
     * 更新KYC报告生成时间
     * @param opportunityId 机会ID
     * @param kycReportTime KYC报告生成时间
     */
    public void updateKycReportTime(Integer opportunityId, Date kycReportTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            detail.setKycReportTime(kycReportTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新咨询风险报告生成时间
     * @param opportunityId 机会ID
     * @param riskReportTime 咨询风险报告生成时间
     */
    public void updateRiskReportTime(Integer opportunityId, Date riskReportTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            detail.setRiskReportTime(riskReportTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新指派统筹时间
     * @param opportunityId 机会ID
     * @param assignCoordinationTime 指派统筹时间
     */
    public void updateAssignCoordinationTime(Integer opportunityId, String assignCoordinationTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            if (EmptyUtils.isNotEmpty(detail.getAssignCoordinationTime())){
                assignCoordinationTime = detail.getAssignCoordinationTime() + "\n" + assignCoordinationTime;
            }
            detail.setAssignCoordinationTime(assignCoordinationTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新指派项目经理时间
     * @param opportunityId 机会ID
     * @param tenantId 租户ID
     * @param assignProjectManagerTime 指派项目经理时间
     */
    public void updateAssignProjectManagerTime(Integer opportunityId, String tenantId, String assignProjectManagerTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, tenantId);
        if (detail != null) {
            if (EmptyUtils.isNotEmpty(detail.getAssignProjectManagerTime())){
                assignProjectManagerTime = detail.getAssignProjectManagerTime() + "\n" + assignProjectManagerTime;
            }
            detail.setAssignProjectManagerTime(assignProjectManagerTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新项目团队信息
     * @param opportunityId 机会ID
     * @param projectTeamInfo 项目团队信息（JSON格式）
     */
    public void updateProjectTeamInfo(Integer opportunityId, String projectTeamInfo) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            detail.setProjectTeamInfo(projectTeamInfo);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新机会关闭时间
     * @param opportunityId 机会ID
     * @param closeTime 机会关闭时间
     */
    public void updateCloseTime(Integer opportunityId, String closeTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            if (EmptyUtils.isNotEmpty(detail.getCloseTime())){
                closeTime = detail.getCloseTime() + "\n" + closeTime;
            }
            detail.setCloseTime(closeTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新项目总结时间
     * @param opportunityId 机会ID
     * @param summaryTime 项目总结时间
     */
    public void updateSummaryTime(Integer opportunityId, Date summaryTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            detail.setSummaryTime(summaryTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新排分时间
     * @param opportunityId 机会ID
     * @param rankingTime 排分时间
     */
    public void updateRankingTime(Integer opportunityId, String rankingTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            detail.setRankingTime(rankingTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新出单时间
     * @param opportunityId 机会ID
     * @param policyTime 出单时间
     */
    public void updatePolicyTime(Integer opportunityId, String policyTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            detail.setPolicyTime(policyTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新暂停时间
     * @param opportunityId 机会ID
     * @param suspendTime 暂停时间
     */
    public void updateSuspendTime(Integer opportunityId, String suspendTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            if (EmptyUtils.isNotEmpty(detail.getSuspendTime())){
                suspendTime = detail.getSuspendTime() + "\n" + suspendTime;
            }
            detail.setSuspendTime(suspendTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 更新重启时间
     * @param opportunityId 机会ID
     * @param restartTime 重启时间
     */
    public void updateRestartTime(Integer opportunityId, String restartTime) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
        if (detail != null) {
            if (EmptyUtils.isNotEmpty(detail.getRestartTime())){
                restartTime = detail.getRestartTime() + "\n" + restartTime;
            }
            detail.setRestartTime(restartTime);
            detail.setUpdateId(SysLoginUtils.getUserId());
            detail.setUpdateTime(new Date());
            opportunityDetailService.updateByPrimaryKeySelective(detail);
        }
    }

    /**
     * 获取机会流程进度
     * @param opportunityId 机会ID
     * @return 流程进度列表
     */
    public List<ActivityInfoVO> getProcessProgress(Integer opportunityId){
        List<ActivityInfoVO> list = new ArrayList<>();
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (EmptyUtils.isEmpty(opportunity.getCurrentProcessId()))
            return list;
        String processInstanceId = opportunityProcessService.selectByPrimaryKey(opportunity.getCurrentProcessId()).getBpmProcessId();
        Result<ProcessProgressVO> progress = elmsBpmWebService.getProcessProgress(processInstanceId);
        if (EmptyUtils.isNotEmpty(progress.getDatas())) {
            if (progress.getDatas().getCompletedActivities()!=null){
                progress.getDatas().getCompletedActivities().forEach(obj->obj.setStatus("1"));
                list.addAll(progress.getDatas().getCompletedActivities());
            }
            if (progress.getDatas().getCurrentActivities()!=null){
                progress.getDatas().getCurrentActivities().forEach(obj->obj.setStatus("0"));
                list.addAll(progress.getDatas().getCurrentActivities());
            }
            if (progress.getDatas().getPendingActivities()!=null){
                progress.getDatas().getPendingActivities().forEach(obj->obj.setStatus("0"));
                list.addAll(progress.getDatas().getPendingActivities());
            }
        }
        return list;
    }

    /**
     * 获取机会时间线（融合流程进度和操作日志）
     * @param opportunityId 机会ID
     * @return 时间线列表（按时间正序排序）
     */
    public List<OpportunityTimelineVO> getOpportunityTimeline(Integer opportunityId) {
        if (EmptyUtils.isEmpty(opportunityId)){
            throw new BusinessException("opportunityId 不能为空");
        }

        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (EmptyUtils.isEmpty(opportunity.getCurrentProcessId()))
            return null;

        List<OpportunityTimelineVO> timelineList = new ArrayList<>();

        // 1. 获取流程进度数据
        List<ActivityInfoVO> progressList = getProcessProgress(opportunityId);
        for (ActivityInfoVO progress : progressList) {
            OpportunityTimelineVO timeline = new OpportunityTimelineVO();
            timeline.setTimelineId("PROGRESS_" + progress.getActivityId());
            timeline.setType("PROGRESS");
            timeline.setTitle(progress.getActivityName());
            timeline.setDescription("流程节点：" + progress.getActivityName());
            timeline.setStatus(progress.getStatus());
            timeline.setActivityId(progress.getActivityId());
            timeline.setActivityName(progress.getActivityName());
            timeline.setStartTime(progress.getStartTime());
            timeline.setEndTime(progress.getEndTime());
            timeline.setAssignee(progress.getAssignee());
            // 设置流程节点自定义属性
            timeline.setCustomProperties(progress.getCustomProperties());
            // timeline.setNodeType(progress.getNodeType());
            // timeline.setNodeCode(progress.getNodeCode());
            // timeline.setNodeDescription(progress.getNodeDescription());
            // timeline.setAssigneeRole(progress.getAssigneeRole());
            // timeline.setAssigneeOrg(progress.getAssigneeOrg());

            // 设置时间：已完成的使用结束时间，未完成的使用开始时间
            if ("1".equals(progress.getStatus()) && progress.getEndTime() != null) {
                timeline.setTimelineTime(progress.getEndTime());
            } else {
                timeline.setTimelineTime(new Date()); // 默认当前时间
            }

            timelineList.add(timeline);
        }

        // 2. 获取操作日志数据
        Map<String, Object> logParam = new HashMap<>();
        logParam.put("opportunityId", opportunityId);
        List<OpportunityProcessLog> logList = opportunityProcessLogService.selectByParam(logParam);

        // 转换为时间线VO
        List<OpportunityTimelineVO> logTimelineList = convertLogsToTimeline(logList,true);
        timelineList.addAll(logTimelineList);

        // 3. 按时间正序排序
        sortTimelineByTime(timelineList);

        return timelineList;
    }

    /**
     * 获取机会流水日志
     * @param opportunityId
     * @return
     */
    public List<OpportunityTimelineVO> getProcessProgessLog(Integer opportunityId){
        if (EmptyUtils.isEmpty(opportunityId)){
            throw new BusinessException("opportunityId 不能为空");
        }
        // 获取操作日志数据
        Map<String, Object> logParam = new HashMap<>();
        logParam.put("opportunityId", opportunityId);
        List<OpportunityProcessLog> logList = opportunityProcessLogService.selectByParam(logParam);

        // 转换为时间线VO并排序
        List<OpportunityTimelineVO> timelineList = convertLogsToTimeline(logList,true);
        sortTimelineByTime(timelineList);
        
        return timelineList;
    }

    /**
     * 获取关闭原因类型列表
     * @return 关闭原因类型列表
     */
    public List<OpportunityCloseReasonVO> getCloseReasonTypes() {
        List<OpportunityCloseReasonVO> reasonList = new ArrayList<>();
        for (OpportunityCloseReasonEnum reason : OpportunityCloseReasonEnum.values()) {
            OpportunityCloseReasonVO vo = new OpportunityCloseReasonVO();
            vo.setCode(reason.getCode());
            vo.setName(reason.getName());
            reasonList.add(vo);
        }
        return reasonList;
    }

    /**
     * 关闭机会（使用请求VO）
     * @param request 关闭请求
     */
    public void closeOpportunity(OpportunityCloseRequestVO request) {
        // 验证关闭原因类型
        if (!OpportunityCloseReasonEnum.containsCode(request.getReasonType())) {
            throw new BusinessException("无效的关闭原因类型");
        }
        closeOpportunity(request.getOpportunityId(), request.getReasonType(), request.getReasonDesc());
    }

    /**
     * 重启关闭的机会（使用请求VO）
     * @param request 重启请求
     */
    public void restartOpportunity(OpportunityRestartRequestVO request) {
        restartOpportunity(request.getOpportunityId(), request.getReasonDesc(), request.getBusinessTenantId(), request.getAssignee(), request.getAssigneeRoleType(), request.getAssigneeOrg());
    }

    /**
     * 查询我的待参与任务
     * 查询在项目团队中属于专家角色且join_type为待确认（0）的数据，支持多种查询条件
     *
     * @param reqVo 分页查询请求，包含查询条件和分页参数
     * @return 分页结果
     */
    public Result<PageInfo<OpportunityTodoVO>> getToJoinPage(PageRequest<OpportunityTodoQueryDTO> reqVo){
        parseProcessStepValue(reqVo);
        // 构建查询参数
        OpportunityTodoQueryDTO queryDTO = reqVo.getParam();
        if (queryDTO == null) {
            queryDTO = new OpportunityTodoQueryDTO();
        }
        
        // 设置必要的查询参数
        queryDTO.setUserId(SysLoginUtils.getUserId());
        queryDTO.setTenantId(SysLoginUtils.getUser().getTenantId());
        
        // 启动分页
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        
        // 直接使用支持查询条件的分页查询
        List<OpportunityTodoVO> pendingTasks = mapper.selectPendingParticipationTasksWithPagination(queryDTO);
        
        // 设置流程步骤标签
        pendingTasks.forEach(obj -> {
            if (obj.getProcessStep() != null) {
                obj.setProcessStep(OpportunityStepEnum.getByValue(obj.getProcessStep()).getLabel());
            }
        });
        
        // 返回分页结果
        PageInfo<OpportunityTodoVO> pageInfo = new PageInfo<>(pendingTasks);
        return Result.succeedWith(pageInfo);
    }
    
    /**
     * 查询锁定状态不为0的机会
     * 查询条件：lock_status != 0，支持多种查询条件
     *
     * @param reqVo 分页查询请求，包含查询条件和分页参数
     * @return 分页结果
     */
    public Result<PageInfo<OpportunityTodoVO>> getLockedPage(PageRequest<OpportunityTodoQueryDTO> reqVo){
        parseProcessStepValue(reqVo);
        // 构建查询参数
        OpportunityTodoQueryDTO queryDTO = reqVo.getParam();
        if (queryDTO == null) {
            queryDTO = new OpportunityTodoQueryDTO();
        }
        
        // 设置必要的查询参数
        queryDTO.setTenantId(SysLoginUtils.getUser().getTenantId());
        
        // 启动分页
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        
        // 使用支持查询条件的分页查询
        List<OpportunityTodoVO> lockedOpportunities = mapper.selectLockedOpportunitiesWithPagination(queryDTO);
        
        // 设置流程步骤标签
        lockedOpportunities.forEach(obj -> {
            if (obj.getProcessStep() != null) {
                obj.setProcessStep(OpportunityStepEnum.getByValue(obj.getProcessStep()).getLabel());
            }
        });
        
        // 返回分页结果
        PageInfo<OpportunityTodoVO> pageInfo = new PageInfo<>(lockedOpportunities);
        return Result.succeedWith(pageInfo);
    }

    /**
     * 查询我参与的机会
     * 判断依据：在项目团队中确定参与（join_type = 1），机会流程日志中存在过的人，支持多种查询条件
     *
     * @param reqVo 分页查询请求，包含查询条件和分页参数
     * @return 分页结果
     */
    public Result<PageInfo<OpportunityTodoVO>> getToHasJoin(PageRequest<OpportunityTodoQueryDTO> reqVo){
        parseProcessStepValue(reqVo);
        // 构建查询参数
        OpportunityTodoQueryDTO queryDTO = reqVo.getParam();
        if (queryDTO == null) {
            queryDTO = new OpportunityTodoQueryDTO();
        }
        
        // 设置必要的查询参数
        queryDTO.setUserId(SysLoginUtils.getUserId());
        queryDTO.setTenantId(SysLoginUtils.getUser().getTenantId());

        // 启动分页
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        
        // 使用支持查询条件的分页查询
        List<OpportunityTodoVO> participatedTasks = mapper.selectParticipatedOpportunitiesWithPaginationAndConditions(queryDTO);
        
        // 设置流程步骤标签
        participatedTasks.forEach(obj -> {
            if (obj.getProcessStep() != null) {
                obj.setProcessStep(OpportunityStepEnum.getByValue(obj.getProcessStep()).getLabel());
            }
        });
        
        // 返回分页结果
        PageInfo<OpportunityTodoVO> pageInfo = new PageInfo<>(participatedTasks);
        return Result.succeedWith(pageInfo);
    }

    /**
     * 查询所有已提交的机会，支持多种查询条件
     *
     * @param reqVo 分页查询请求，包含查询条件和分页参数
     * @return 分页结果
     */
    public Result<PageInfo<OpportunityTodoVO>> getAlloppoPage(PageRequest<OpportunityTodoQueryDTO> reqVo){
        parseProcessStepValue(reqVo);
        // 构建查询参数
        OpportunityTodoQueryDTO queryDTO = reqVo.getParam();
        if (queryDTO == null) {
            queryDTO = new OpportunityTodoQueryDTO();
        }
        
        // 设置必要的查询参数
        queryDTO.setTenantId(SysLoginUtils.getUser().getTenantId());
        
        // 启动分页
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        
        // 使用支持查询条件的分页查询
        List<OpportunityTodoVO> allOppo = mapper.selectAllOpportunitiesWithPagination(queryDTO);
        
        // 设置流程步骤标签
        allOppo.forEach(obj -> {
            if (obj.getProcessStep() != null) {
                obj.setProcessStep(OpportunityStepEnum.getByValue(obj.getProcessStep()).getLabel());
            }
        });
        
        // 返回分页结果
        PageInfo<OpportunityTodoVO> pageInfo = new PageInfo<>(allOppo);
        return Result.succeedWith(pageInfo);
    }


    /**
     * 根据机会ID集合查询机会
     */
    public List<Opportunity> selectByIds(List<Integer> opportunityIds) {
        return this.mapper.selectByIds(opportunityIds);
    }
    
    /**
     * 根据机会类型和企业ID查询进行中的机会列表
     * @param opportunityType 机会类型
     * @param agentEnterpriseId 企业ID
     * @param excludeOpportunityId 排除的机会ID
     * @return 机会列表
     */
    public List<Opportunity> selectActiveOpportunitiesByTypeAndEnterprise(String opportunityType, Integer agentEnterpriseId, Integer excludeOpportunityId) {
        return this.mapper.selectActiveOpportunitiesByTypeAndEnterprise(opportunityType, agentEnterpriseId, excludeOpportunityId);
    }
    
    /**
     * 根据机会类型和企业信用代码查询进行中的机会列表
     * @param opportunityType 机会类型
     * @param creditCode 企业信用代码
     * @param excludeOpportunityId 排除的机会ID
     * @return 机会列表
     */
    public List<Opportunity> selectActiveOpportunitiesByTypeAndCreditCode(String opportunityType, String creditCode, Integer excludeOpportunityId) {
        return this.mapper.selectActiveOpportunitiesByTypeAndCreditCode(opportunityType, creditCode, excludeOpportunityId);
    }
    
    /**
     * 批量更新机会的锁定状态
     * @param opportunityIds 机会ID列表
     * @param lockStatus 锁定状态
     * @param updateUserId 更新用户ID
     * @return 更新行数
     */
    public int batchUpdateLockStatus(List<Integer> opportunityIds, Integer lockStatus, String updateUserId) {
        if (opportunityIds == null || opportunityIds.isEmpty()) {
            return 0;
        }
        return this.mapper.batchUpdateLockStatus(opportunityIds, lockStatus, updateUserId);
    }

    /**
     * 分页查询员工线索统计
     * @param page 分页查询参数
     * @return 分页结果
     */
    public PageInfo<EmployeeLeadVO> statAgentEnterprise(PageRequest<Opportunity> page) {
        Opportunity queryParam = page.getParam();
        // 设置租户ID
        queryParam.setTenantId(SysLoginUtils.getUser().getTenantId());
        // 启动分页
        PageHelper.startPage(page.getPageNum(), page.getPageSize());
        // 执行查询
        List<EmployeeLeadVO> list = mapper.statAgentEnterprise(queryParam);
        // 返回分页结果
        return new PageInfo<>(list);
    }

    /**
     * 获取机会完整详情（包含机会信息、机会详情、企业信息）
     * @param request 请求参数
     * @return 机会完整详情
     */
    public OpportunityDetailResponseVO getOpportunityFullDetail(OpportunityDetailRequestVO request) {
        if (request == null || request.getOpportunityId() == null) {
            throw new BusinessException("机会ID不能为空");
        }

        Integer opportunityId = request.getOpportunityId();
        String tenantId = SysLoginUtils.getUser().getTenantId();

        // 1. 获取机会基本信息
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }

        // 2. 获取机会详细信息
        OpportunityDetail opportunityDetail = opportunityDetailService.selectByOpportunityId(opportunityId, tenantId);

        // 3. 获取企业信息
        GenAgentEnterprise enterprise = null;
        if (opportunity.getAgentEnterpriseId() != null) {
            enterprise = genAgentEnterpriseService.selectByPrimaryKey(opportunity.getAgentEnterpriseId());
        }

        // 4. 组装响应数据
        OpportunityDetailResponseVO response = new OpportunityDetailResponseVO();
        response.setOpportunity(opportunity);
        response.setOpportunityDetail(opportunityDetail);
        response.setEnterprise(enterprise);

        return response;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证机会ID并获取机会信息
     * @param opportunityId 机会ID
     * @return 机会信息
     */
    private Opportunity validateAndGetOpportunity(Integer opportunityId) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }
        
        return opportunity;
    }

    /**
     * 更新机会状态并记录日志
     * @param opportunityId 机会ID
     * @param newStatus 新状态
     * @param operatorDesc 操作描述
     * @param reasonType 原因类型（可选）
     * @param reasonDesc 原因描述（可选）
     */
    private void updateOpportunityStatusAndLog(Integer opportunityId, Integer newStatus, String operatorDesc, Integer reasonType, String reasonDesc) {
        Opportunity opportunity = validateAndGetOpportunity(opportunityId);
        
        // 更新机会状态
        opportunity.setStatus(newStatus);
        // 如果是关闭操作，同时设置关闭原因类型
        if (OpportunityStatusEnum.TERMINATED.getCode().equals(newStatus) && reasonType != null) {
            opportunity.setCloseReasonType(reasonType);
        }else{
            opportunity.setCloseReasonType(null);
        }
        
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        this.updateByPrimaryKey(opportunity);
        
        // 记录操作日志
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setTenantId(opportunity.getTenantId());
        oplog.setOperatorId(SysLoginUtils.getUserId());
        oplog.setOperatorDesc(operatorDesc);
        oplog.setReasonType(reasonType);
        oplog.setReasonDesc(reasonDesc);
        oplog.setIsNodeCompleteLog(0); // 不是节点完成日志
        oplog.setCreateTime(new Date());
        oplog.setIsDeleted(0);
        opportunityProcessLogService.insert(oplog);
    }

    /**
     * 更新机会明细提交时间
     * @param opportunityId 机会ID
     * @param tenantId 租户ID
     */
    private void updateOpportunityDetailSubmitTime(Integer opportunityId, String tenantId) {
        Map<String, Object> param = new HashMap<>();
        param.put("opportunityId", opportunityId);
        param.put("tenantId", tenantId);
        List<OpportunityDetail> details = opportunityDetailService.selectByParam(param);
        if (!details.isEmpty()) {
            OpportunityDetail detail = details.get(0);
            if (detail.getSubmitTime() == null) {
                detail.setSubmitTime(new Date());
                opportunityDetailService.updateByPrimaryKey(detail);
            }
        }
    }

    /**
     * 创建操作日志
     * @param opportunityId 机会ID
     * @param tenantId 租户ID
     * @param operatorDesc 操作描述
     * @param reasonType 原因类型（可选）
     * @param reasonDesc 原因描述（可选）
     * @param isNodeCompleteLog 是否节点完成日志（可选）
     */
    private void createProcessLog(Integer opportunityId, String tenantId, String operatorDesc, Integer reasonType, String reasonDesc, Integer isNodeCompleteLog) {
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setTenantId(tenantId);
        oplog.setOperatorId(SysLoginUtils.getUserId());
        oplog.setOperatorDesc(operatorDesc);
        oplog.setReasonType(reasonType);
        oplog.setReasonDesc(reasonDesc);
        oplog.setIsNodeCompleteLog(isNodeCompleteLog);
        oplog.setCreateTime(new Date());
        oplog.setIsDeleted(0);
        opportunityProcessLogService.insert(oplog);
    }



    /**
     * 转换日志列表为时间线VO列表
     * @param logList 日志列表
     * @param showNodelog 是否显示节点日志
     * @return 时间线VO列表
     */
    private List<OpportunityTimelineVO> convertLogsToTimeline(List<OpportunityProcessLog> logList,boolean showNodelog) {
        List<OpportunityTimelineVO> timelineList = new ArrayList<>();
        
        for (OpportunityProcessLog log : logList) {
            // 跳过节点完成日志
            if (!showNodelog && new Integer(1).equals(log.getIsNodeCompleteLog())) {
                continue;
            }
            
            OpportunityTimelineVO timeline = new OpportunityTimelineVO();
            timeline.setTimelineId("LOG_" + log.getId());
            timeline.setType("LOG");
            timeline.setTitle("操作记录");
            timeline.setDescription(log.getOperatorDesc());
            timeline.setOperatorId(log.getOperatorId());
            timeline.setTargetId(log.getTargetId());
            timeline.setTimelineTime(log.getCreateTime());
            timeline.setStatus("1"); // 日志都是已完成的
            timeline.setReasonType(log.getReasonType());
            timeline.setReasonDesc(log.getReasonDesc());
            timelineList.add(timeline);
        }
        
        return timelineList;
    }

    /**
     * 按时间正序排序时间线列表
     * @param timelineList 时间线列表
     */
    private void sortTimelineByTime(List<OpportunityTimelineVO> timelineList) {
        timelineList.sort((a, b) -> {
            if (a.getTimelineTime() == null && b.getTimelineTime() == null) {
                return 0;
            }
            if (a.getTimelineTime() == null) {
                return 1;
            }
            if (b.getTimelineTime() == null) {
                return -1;
            }
            return a.getTimelineTime().compareTo(b.getTimelineTime());
        });
    }

    /**
     * 参与弹窗 - 获取邀请人信息
     *
     * @param opportunityId
     * <AUTHOR>
     * @Date 2025/8/11 10:09
     */
    public OpportunityJoinResponseVO getOpportunityInviterInfo(Integer opportunityId) {
        Opportunity opportunity = mapper.selectByPrimaryKey(opportunityId);
        OpportunityJoinResponseVO responseVO = new OpportunityJoinResponseVO();
        if (EmptyUtils.isNotEmpty(opportunity)) {

            responseVO.setProjectOrgName(opportunity.getProjectOrgName());

            // 调用云服获取用户账号与昵称
            UserIdReq requestVO = new UserIdReq();
            requestVO.setUserId(opportunity.getProjectManager());
            Result<com.kbao.kbcbsc.user.entity.User> result = userClientAdapter.findById(requestVO);
            if (ResultStatusEnum.isSuccess(result.getResp_code()) && EmptyUtils.isNotEmpty(result.getDatas())) {
                responseVO.setProjectManagerName(result.getDatas().getNickName());
                responseVO.setProjectManagerId(result.getDatas().getUserName());
            }
        }
        return responseVO;
    }


    /**
     * 机会状态 结构 机会状态-流程状态
     *
     * @param
     * <AUTHOR>
     * @Date 2025/8/12 11:20
     */
    public List<OpportunityStatusResponseVO> getOpportunityStatus() {
        List<OpportunityStatusResponseVO> list = new ArrayList<>();

        for (OpportunityStepEnum stepEnum : OpportunityStepEnum.values()) {
            if (EmptyUtils.isNotEmpty(stepEnum.getValue())) {
                OpportunityStatusResponseVO responseVO = new OpportunityStatusResponseVO();
                responseVO.setDicItemCode("1-" + stepEnum.getValue());
                responseVO.setDicItemName(stepEnum.getLabel());
                list.add(responseVO);
            }
        }

        for (OpportunityStatusEnum statusEnum : OpportunityStatusEnum.values()) {
            // 排除待提交、已提交、锁定状态，流程状态中已包含
            if (!OpportunityStatusEnum.SUBMITTED.getCode().equals(statusEnum.getCode())
                    && !OpportunityStatusEnum.PENDING_SUBMIT.getCode().equals(statusEnum.getCode())
                    && !OpportunityStatusEnum.LOCKED.getCode().equals(statusEnum.getCode())) {
                OpportunityStatusResponseVO responseVO = new OpportunityStatusResponseVO();
                responseVO.setDicItemCode(statusEnum.getCode().toString() + "-");
                responseVO.setDicItemName(statusEnum.getName());
                list.add(responseVO);
            }
        }
        return list;
    }

    /**
     * 检查已成交机会是否已填写保单号
     * @param opportunityId 机会ID
     */
    private void checkPolicyNumberForWonOpportunity(Integer opportunityId) {
        // 查询该机会下的所有订单
        List<OpportunityOrder> orders =
            opportunityOrderService.queryOpportunityOrderListByOpportunityId(opportunityId.toString());

        // 检查是否有填写了保单号的订单
        boolean hasPolicyNumber = orders.stream()
            .anyMatch(order -> EmptyUtils.isNotEmpty(order.getPolicyNo()));

        if (!hasPolicyNumber) {
            throw new BusinessException("选择'机会已成交'作为关闭原因时，必须填写保单号");
        }
    }

    public void updateOpportunityManager(String userId,Integer opportunityId,String updateId) {
        if(StringUtil.isEmpty(userId) ||opportunityId==null ) {
            return;
        }
        Opportunity oppo = new Opportunity();
        oppo.setId(opportunityId);
        oppo.setProjectManager(userId);
        oppo.setUpdateTime(new Date());
        oppo.setUpdateId(updateId);

        this.mapper.updateByPrimaryKeySelective(oppo);
    }

    public List<AgentEnterpriseOtherDto> getOpportunityNum(List<Integer> agentEnterpriseIds) {
        return mapper.getOpportunityNum(agentEnterpriseIds);
    }

    /**
     * 根据机会ID数组批量查询对应的BPM流程ID
     * 通过机会表的current_process_id关联查询opportunity_process表的bpm_process_id
     *
     * @param opportunityIds 机会ID数组
     * @return BPM流程ID列表
     */
    public List<String> getBpmProcessIdsByOpportunityIds(List<Integer> opportunityIds) {
        if (EmptyUtils.isEmpty(opportunityIds)) {
            return new ArrayList<>();
        }
        return mapper.selectBpmProcessIdsByOpportunityIds(opportunityIds);
    }

    /**
     * 修改机会进程执行人
     * @param opportunityIds
     * @param userId
     */
    public void changeProcessTaskUser(List<Integer> opportunityIds,String userId){
        List<String> ids = getBpmProcessIdsByOpportunityIds(opportunityIds);
        BatchTaskUserUpdateVO vo = new BatchTaskUserUpdateVO();
        vo.setProcessInstanceIds(ids);
        vo.setAssignees(Arrays.asList(new String[]{userId}));
        elmsBpmWebService.setBatchTaskDealUserById(vo);
    }

    /**
     * 提交机会并自动启动流程
     * @param opportunityId 机会ID
     * @return 机会提交响应
     */
    public void submitOpportunity(Integer opportunityId) {
        // 1. 验证请求参数
        if (opportunityId == null) {
            throw new BusinessException("机会ID不能为空");
        }

        // 2. 获取机会信息
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }

        // 3. 检查机会状态
        Integer currentStatus = opportunity.getStatus();
        if (!OpportunityStatusEnum.PENDING_SUBMIT.getCode().equals(currentStatus)) {
            throw new BusinessException("只有待提交状态的机会才能提交，当前状态：" +
                OpportunityStatusEnum.getNameByCode(currentStatus));
        }

        // 4. 获取当前用户信息
        String userId = ElmsContext.getUser().getUserId();

        // 5. 修改状态：从待提交改为已提交
        opportunity.setStatus(OpportunityStatusEnum.SUBMITTED.getCode());
        opportunity.setUpdateId(userId);
        opportunity.setUpdateTime(new Date());
        this.updateByPrimaryKey(opportunity);

        // 6. 启动流程
        String message = "机会提交成功";
        try {
            // 根据企业ID获取企业类型
            GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(opportunity.getAgentEnterpriseId());
            if (enterprise == null) {
                throw new BusinessException("关联企业信息不存在");
            }
            String companyType = enterprise.getDtType(); // 从企业实体中获取企业类型
            opportunityProcessService.startProcess(opportunityId, companyType,true);
        } catch (Exception e) {
            log.error("启动流程失败，机会ID：{}，错误信息：{}", opportunityId, e.getMessage(), e);
            message = "机会提交成功，但流程启动失败：" + e.getMessage();
            throw new BusinessException(message);
        }
    }





    /**
     * 机会详情 - 平安询价获取客户信息
     *
     * @param opportunityId
     * <AUTHOR>
     * @Date 2025/8/25 16:19
     */
    public OpportunityInquiryEnterpriseRespVO getInquiryEnterpriseInfo(Integer opportunityId) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }

        String tenantId = SysLoginUtils.getUser().getTenantId();

        // 1. 获取机会基本信息
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }
        OpportunityInquiryEnterpriseRespVO respVO = new OpportunityInquiryEnterpriseRespVO();

        // 2. 获取机会详细信息
        OpportunityDetail opportunityDetail = opportunityDetailService.selectByOpportunityId(opportunityId, tenantId);
        if(EmptyUtils.isNotEmpty(opportunityDetail)) {
            respVO.setPolicyExpireTime(opportunityDetail.getPolicyExpireTime());
        }

        // 3. 获取投保人信息
        if (opportunity.getAgentEnterpriseId() != null) {
            GenAgentEnterprise enterprise =
                    genAgentEnterpriseService.selectByPrimaryKey(opportunity.getAgentEnterpriseId());

            Industry industry = industryService.selectByCode(enterprise.getCategoryCode());
            if (EmptyUtils.isNotEmpty(industry)) {
                String fullPath = industry.getFullPath();// 行业分类完整路径
                String[] fullPahtArr = fullPath.split("/");
                // 只去行业分类2 3级数据
                if (fullPahtArr.length == 2) {// 2级分类
                    respVO.setCategoryFirstCode(fullPahtArr[1]);
                } else if (fullPahtArr.length >= 3) {
                    respVO.setCategoryFirstCode(fullPahtArr[1]);
                    respVO.setCategorySecondCode(fullPahtArr[2]);
                }
            }
            respVO.setName(enterprise.getName());
            respVO.setCreditCode(enterprise.getCreditCode());
        }
        return respVO;
    }
}

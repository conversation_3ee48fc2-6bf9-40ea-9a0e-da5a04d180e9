package com.kbao.kbcelms.role.dao;


import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.role.entity.Role;
import com.kbao.kbcelms.role.vo.RoleResponseVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RoleMapper extends BaseMapper<Role, Integer> {

    Role getByRoleName(@Param("roleName") String roleName,@Param("tenantId") String tenantId);

    List<RoleResponseVO> findByCondition(Map<String, Object> queryParam);
    /**
     * 根据用户ID查询所有角色
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> selectRolesByUserId(@Param("userId") String userId);
}
package com.kbao.kbcelms.enterprise.base.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;import java.util.List;

/**
 * 企业基本信息DAO
 * <AUTHOR>
 * @date 2025-07-31
 */
@Repository
public class EnterpriseBasicInfoDao extends BaseMongoDaoImpl<EnterpriseBasicInfo, String> {

    /**
     * 根据统一社会信用代码查询企业基本信息
     * @param creditCode 统一社会信用代码
     * @return 企业基本信息
     */
    public EnterpriseBasicInfo findByCreditCode(String creditCode) {
        Criteria criteria = Criteria.where("creditCode").is(creditCode);
        Query query = new Query().addCriteria(criteria);
        return this.findOne(query);
    }
    
    /**
     * 根据企业名称查询企业基本信息
     * @return 企业基本信息
     */
    public List<EnterpriseBasicInfo> searchByName(String name) {
        String tenantId = ElmsContext.getTenantId();
        Criteria criteria = Criteria.where("name").regex(name);
        Query query = Query.query(criteria).limit(10);
        query.fields().include("name", "creditCode");
        return this.find(query);
    }

    public EnterpriseBasicInfo queryEnterpriseInfo(String creditCode, String name) {
        Criteria criteria = new Criteria();
        if (EmptyUtils.isNotEmpty(creditCode)) {
            criteria.and("creditCode").is(creditCode);
        } else {
            criteria.and("name").is(name);
        }
        Query query = Query.query(criteria);
        return this.findOne(query);
    }
}

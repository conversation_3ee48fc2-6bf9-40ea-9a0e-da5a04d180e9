package com.kbao.kbcelms.enterprise.query.config.service;

import com.github.pagehelper.PageInfo;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.query.config.bean.EnterpriseQueryConfigRequest;
import com.kbao.kbcelms.enterprise.query.config.bean.EnterpriseQueryConfigVO;
import com.kbao.kbcelms.enterprise.query.config.bean.QueryUsageStatisticsVO;
import com.kbao.kbcelms.enterprise.query.config.dao.EnterpriseQueryConfigDao;
import com.kbao.kbcelms.enterprise.query.config.model.EnterpriseQueryConfig;
import com.kbao.kbcelms.enterprise.query.record.service.EnterpriseQueryRecordService;import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import static com.kbao.kbcelms.common.constants.ElmsConstant.ENTERPRISE_QUERY_DEFAULT_DAILY_LIMIT;
import static com.kbao.kbcelms.common.constants.ElmsConstant.ENTERPRISE_QUERY_DEFAULT_MONTHLY_LIMIT;

/**
 * 企业查询配置业务逻辑层
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class EnterpriseQueryConfigService  extends BaseMongoServiceImpl<EnterpriseQueryConfig, String, EnterpriseQueryConfigDao> {
    @Autowired
    private EnterpriseQueryRecordService queryRecordService;

    /**
     * 获取查询使用统计列表
     */
    public PageInfo<QueryUsageStatisticsVO> getQueryUsageStatistics(
            int pageNum, int pageSize, String agentName, String agentCode) {

        String tenantId = SysLoginUtils.getUser().getTenantId();

        // 从查询记录表中统计使用情况（带分页）
        PageInfo<QueryUsageStatisticsVO> pageInfo =
            queryRecordService.getUsageStatistics(tenantId, agentName, agentCode, pageNum, pageSize);

        // 合并配置信息
        if (pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
            mergeConfigInfo(pageInfo.getList(), tenantId);
        }

        return pageInfo;
    }

    /**
     * 获取通用配置
     */
    public EnterpriseQueryConfigVO getGeneralConfig() {
        String tenantId = SysLoginUtils.getUser().getTenantId();

        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(tenantId);
        criteria.and("configType").is(EnterpriseQueryConfig.ConfigType.GENERAL);

        Query query = new Query(criteria);
        EnterpriseQueryConfig config = this.dao.findOne(query);

        if (config == null) {
            // 如果没有通用配置，返回默认值
            EnterpriseQueryConfigVO defaultConfig = new EnterpriseQueryConfigVO();
            defaultConfig.setConfigType(EnterpriseQueryConfig.ConfigType.GENERAL);
            defaultConfig.setDailyLimit(ENTERPRISE_QUERY_DEFAULT_DAILY_LIMIT);
            defaultConfig.setMonthlyLimit(ENTERPRISE_QUERY_DEFAULT_MONTHLY_LIMIT);
            return defaultConfig;
        }

        return convertToVO(config);
    }

    /**
     * 更新通用配置
     */
    public void updateGeneralConfig(EnterpriseQueryConfigRequest request) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        String currentUser = SysLoginUtils.getUser().getUserId();
        Date currentTime = new Date();

        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(tenantId);
        criteria.and("configType").is(EnterpriseQueryConfig.ConfigType.GENERAL);

        Query query = new Query(criteria);
        EnterpriseQueryConfig config = this.dao.findOne(query);

        if (config == null) {
            // 创建新的通用配置
            config = new EnterpriseQueryConfig();
            config.setConfigType(EnterpriseQueryConfig.ConfigType.GENERAL);
            config.setTenantId(tenantId);
            config.setCreateTime(currentTime);
            config.setCreateId(currentUser);
        }

        config.setDailyLimit(request.getDailyLimit());
        config.setMonthlyLimit(request.getMonthlyLimit());
        config.setUpdateTime(currentTime);
        config.setUpdateId(currentUser);

        this.dao.saveOrUpdate(config);
    }

    /**
     * 设置个人配置
     */
    public void setPersonalConfig(EnterpriseQueryConfigRequest request) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        String currentUser = SysLoginUtils.getUser().getUserId();
        Date currentTime = new Date();

        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(tenantId);
        criteria.and("configType").is(EnterpriseQueryConfig.ConfigType.PERSONAL);
        criteria.and("agentCode").is(request.getAgentCode());

        Query query = new Query(criteria);
        EnterpriseQueryConfig config = this.dao.findOne(query);

        if (config == null) {
            // 创建新的个人配置
            config = new EnterpriseQueryConfig();
            config.setConfigType(EnterpriseQueryConfig.ConfigType.PERSONAL);
            config.setAgentCode(request.getAgentCode());
            config.setAgentName(request.getAgentName());
            config.setTenantId(tenantId);
            config.setCreateTime(currentTime);
            config.setCreateId(currentUser);
        }

        config.setDailyLimit(request.getDailyLimit());
        config.setMonthlyLimit(request.getMonthlyLimit());
        config.setUpdateTime(currentTime);
        config.setUpdateId(currentUser);

        this.dao.saveOrUpdate(config);
    }

    /**
     * 删除个人配置
     */
    public void removePersonalConfig(String agentCode) {
        String tenantId = SysLoginUtils.getUser().getTenantId();

        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(tenantId);
        criteria.and("configType").is(EnterpriseQueryConfig.ConfigType.PERSONAL);
        criteria.and("agentCode").is(agentCode);

        Query query = new Query(criteria);
        this.dao.remove(query);
    }



    /**
     * 合并配置信息
     */
    private void mergeConfigInfo(List<QueryUsageStatisticsVO> statisticsList, String tenantId) {
        // 获取通用配置
        EnterpriseQueryConfigVO generalConfig = getGeneralConfig();

        // 获取所有个人配置
        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(tenantId);
        criteria.and("configType").is(EnterpriseQueryConfig.ConfigType.PERSONAL);

        Query query = new Query(criteria);
        List<EnterpriseQueryConfig> personalConfigs = this.dao.find(query);
        Map<String, EnterpriseQueryConfig> personalConfigMap = personalConfigs.stream()
                .collect(Collectors.toMap(EnterpriseQueryConfig::getAgentCode, config -> config));

        // 合并配置信息
        for (QueryUsageStatisticsVO statistics : statisticsList) {
            EnterpriseQueryConfig personalConfig = personalConfigMap.get(statistics.getAgentCode());

            if (personalConfig != null) {
                // 使用个人配置
                statistics.setDailyLimit(personalConfig.getDailyLimit());
                statistics.setMonthlyLimit(personalConfig.getMonthlyLimit());
                statistics.setIsSpecialConfig(true);
            } else {
                // 使用通用配置
                statistics.setDailyLimit(generalConfig.getDailyLimit());
                statistics.setMonthlyLimit(generalConfig.getMonthlyLimit());
                statistics.setIsSpecialConfig(false);
            }
        }
    }

    /**
     * 根据顾问工号获取查询配置（个人配置优先）
     * @param agentCode 顾问工号
     * @return 查询配置
     */
    public EnterpriseQueryConfig getQueryConfigByAgentCode(String agentCode) {
        String tenantId = ElmsContext.getTenantId();

        // 先查个人配置
        Criteria criteria = Criteria.where("agentCode").is(agentCode)
                .and("configType").is(EnterpriseQueryConfig.ConfigType.PERSONAL)
                .and("tenantId").is(tenantId);
        EnterpriseQueryConfig personalConfig = this.findOne(Query.query(criteria));
        if (personalConfig != null) {
            return personalConfig;
        }
        // 没有个人配置则使用通用配置
        criteria = Criteria.where("tenantId").is(tenantId)
            .and("configType").is(EnterpriseQueryConfig.ConfigType.GENERAL);
        EnterpriseQueryConfig generalConfig = this.dao.findOne(Query.query(criteria));
        if (generalConfig != null) {
            return generalConfig;
        }
        // 如果都没有配置，返回默认配置
        EnterpriseQueryConfig defaultConfig = new EnterpriseQueryConfig();
        defaultConfig.setDailyLimit(ENTERPRISE_QUERY_DEFAULT_DAILY_LIMIT);
        defaultConfig.setMonthlyLimit(ENTERPRISE_QUERY_DEFAULT_MONTHLY_LIMIT);
        return defaultConfig;
    }

    /**
     * 转换为VO对象
     */
    private EnterpriseQueryConfigVO convertToVO(EnterpriseQueryConfig config) {
        EnterpriseQueryConfigVO vo = new EnterpriseQueryConfigVO();
        BeanUtils.copyProperties(config, vo);
        return vo;
    }
}

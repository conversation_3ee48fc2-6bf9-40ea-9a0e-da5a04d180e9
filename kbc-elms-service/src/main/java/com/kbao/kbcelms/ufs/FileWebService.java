package com.kbao.kbcelms.ufs;

import com.alibaba.fastjson.JSON;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.SystemClock;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.appfilechannel.bean.AppFileChannelListVo;
import com.kbao.kbcbsc.tenant.entity.Tenant;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.bsc.BscClientService;
import com.kbao.kbcelms.enums.FileTypeEnum;
import com.kbao.kbcucs.context.RequestContext;
import com.kbao.kbcufs.client.FileClientService;
import com.kbao.kbcufs.file.vo.client.FileUploadRequest;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.kbcufs.utils.SignatureUtil;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件服务接口
 */
@Slf4j
@Service
public class FileWebService {

    @Autowired
    private FileClientService fileClientService;

    @Autowired
    private BscClientService bscClientService;

    /**
     *
     * @param appCode
     * @param businessNo
     * @param file
     * @param content
     * @param path
     * @param fileType
     * @return
     */
    public FileUploadResponse upload(String appCode, String businessNo, MultipartFile file, String content, String path, FileTypeEnum fileType) {
        log.info("FileServiceAdapter:upload >>" + appCode);
        if (StringUtils.isBlank(content) && file == null) {
            throw new BusinessException("文件内容不可为空");
        }
        String tenantId = null;
        String userId = null;
        if (BscUserUtils.getUser() != null) {
            tenantId = BscUserUtils.getUser().getUser().getTenantId();
            userId = BscUserUtils.getUser().getUser().getNickName();
        } else {
            tenantId = RequestContext.TenantId.get();
            userId = "U000001";
        }
        log.info("getFileChannelConfig >> tenantId:" + tenantId + "--appCode--" + appCode + "--userId--" + userId);
        AppFileChannelListVo config = bscClientService.getFileChannelConfig(tenantId, appCode, "1");
        Tenant tenant = bscClientService.getTenant(tenantId);
        FileUploadRequest request = new FileUploadRequest();
        request.setUserName(config.getAccount());
        request.setBusinessNo(businessNo);
        request.setBusinessTenantId(tenantId);
        request.setBusinessTenantName(tenant.getTenantName());
        if (StringUtils.isNotBlank(content)) {
            if (content.startsWith("data:image/") || content.startsWith("data:Image/")) {
                content = content.substring(content.indexOf(",") + 1);
                request.setType(com.kbao.kbcufs.enums.FileTypeEnum.BASE64.getType());
            } else if (content.startsWith("http://") || content.startsWith("https://")) {
                request.setType(com.kbao.kbcufs.enums.FileTypeEnum.NETWORK.getType());
            } else {
                request.setType(com.kbao.kbcufs.enums.FileTypeEnum.TEXT.getType());
            }
            request.setContent(content);
        } else {
            request.setType(com.kbao.kbcufs.enums.FileTypeEnum.FILE.getType());
            request.setFile(file);
        }
        request.setFileType(fileType.getKey());
        request.setPath(path);
        request.setCreateUser(userId);
        request.setTimestamp(SystemClock.nowDate());
        // 账号类型。取值范围：内网账号:intranet；外网账号：internet
        request.setNetwork("sts");
        request.setCover(true);
        request.setSign(SignatureUtil.getSign(request, config.getSecretKey()));
        return this.upload(request);
    }

    /**
     * 文件上传
     *
     * @param request 文件请求
     * @return
     */
    public FileUploadResponse upload(FileUploadRequest request) {
        log.info("FileWebService-upload,request:{}", request);
        Result<FileUploadResponse> response = fileClientService.upload(request);
        log.info("FileWebService-upload,rs:{}", JSON.toJSONString(response));

        if (ResultStatusEnum.SUCCESS.getStatus().equals(response.getResp_code())) {
            return response.getDatas();
        } else {
            throw new BusinessException("上传失败，失败原因:" + response.getResp_msg());
        }
    }
}

package com.kbao.kbcelms.sms;

import com.kbao.kbcelms.enums.MailTemplateEnum;
import com.kbao.kbcums.mailout.vo.Attachment;
import lombok.*;

import java.util.List;
import java.util.Map;


/**
 * 邮件发送请求
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MailRequest {

    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 应用编码
     */
    private String appCode;
    /**
     * 主题
     */
    private String title;
    /**
     * 收件人
     */
    private String mailTo;
    /**
     * 抄送
     */
    private String mailCopy;
    /**
     * 附件
     */
    private List<Attachment> attachment;
    /**
     * 内容，与内容模板不能同时为空
     */
    private String content;
    /**
     * 内容模板，与内容模板不能同时为空
     */
    private MailTemplateEnum template;
    /**
     * 内容动态参数
     */
    private Map<String, String> paramMap;
}

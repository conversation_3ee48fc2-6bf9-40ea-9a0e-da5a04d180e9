package com.kbao.kbcelms.sms;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.AppTenantClientAdapter;
import com.kbao.kbcbsc.appmsgchannel.bean.AppMsgChannelListVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantConfigResVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantIdReq;
import com.kbao.kbcums.client.mail.MailClientService;
import com.kbao.kbcums.client.util.UmsUtil;
import com.kbao.kbcums.common.bean.CommonApiResponse;
import com.kbao.kbcums.mailout.vo.MailApiRequestParam;
import com.kbao.kbcums.mailout.vo.MailApiRequestParamDetail;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 消息通知接口封装
 */
@Slf4j
@Service
public class AppSmsWebService {

    @Autowired
    AppTenantClientAdapter appTenantClientAdapter;

    @Autowired
    private MailClientService mailClientService;

    /**
     * 邮件通知
     *
     * @param request
     * @return
     */
    public Result<CommonApiResponse> sendMail(MailRequest request) {
        MailApiRequestParamDetail detail = new MailApiRequestParamDetail();
        if (request.getTemplate() != null) {
            detail.setContent(request.getTemplate().getContent(request.getParamMap()));
        }
        detail.setTitle(request.getTitle());
        detail.setMailTo(request.getMailTo());
        if (StringUtils.isNotBlank(request.getMailCopy())) {
            detail.setMailCopy(request.getMailCopy());
        }
        if (CollectionUtils.isNotEmpty(request.getAttachment())) {
            detail.setAttachment(request.getAttachment());
        }
        if (StringUtils.isNotBlank(request.getContent())) {
            detail.setContent(request.getContent());
        }
        MailApiRequestParam param = new MailApiRequestParam();
        param.setChannelTenantId(request.getTenantId());
        param.setChannelAppId(request.getAppCode());
        if (request.getTemplate() != null && StringUtils.isNotEmpty(request.getTemplate().getTemplateId())) {
            param.setTemplateId(request.getTemplate().getTemplateId());
        }
        param.setDetail(Lists.newArrayList(detail));
        String timestamp = String.valueOf(System.currentTimeMillis());
        String bodyString = UmsUtil.getBodyString(param);
        AppMsgChannelListVo config = getMsgChannelConfig(request.getTenantId(), request.getAppCode(), "2");
        String sign = SignUtil.axSign(config.getAccount(), config.getSecretKey(), timestamp, bodyString);
        Result<CommonApiResponse> rs = mailClientService.sendMail(param, sign, config.getAccount(), timestamp);
        if(EmptyUtils.isEmpty(rs) || !ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())){
            log.error("sendMail-error,timestamp:{},account:{},sign:{},param:{}", timestamp, config.getAccount(), sign, JSON.toJSONString(param));
        }
        return rs;
    }

    public AppMsgChannelListVo getMsgChannelConfig(String tenantId, String appCode, String msgType) {
        AppTenantIdReq req = new AppTenantIdReq();
        req.setTenantId(tenantId);
        req.setAppCode(appCode);
        req.setAppId(appCode);
        Result<AppTenantConfigResVo> rs = appTenantClientAdapter.getChannelConfigInfo(req);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())) {
            AppTenantConfigResVo appTenantConfigResVo = rs.getDatas();
            if (null != appTenantConfigResVo) {
                List<AppMsgChannelListVo> msgChannelList = appTenantConfigResVo.getMsgChannelList();
                if (CollectionUtils.isNotEmpty(msgChannelList)) {
                    for (AppMsgChannelListVo vo : msgChannelList) {
                        if (StringUtils.equals(msgType, vo.getMsgType())) {
                            return vo;
                        }
                    }
                }
            }
            throw new BusinessException("消息渠道未配置");
        } else {
            throw new BusinessException(rs.getResp_msg());
        }
    }
}



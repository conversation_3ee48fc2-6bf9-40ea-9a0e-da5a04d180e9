package com.kbao.kbcelms.insurance.service;

import java.util.List;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.insurance.dao.InsuranceDao;
import com.kbao.kbcelms.insurance.model.Insurance;
import com.kbao.kbcelms.insurance.vo.InsuranceResponseVO;
import com.kbao.kbcelms.insurance.vo.InsuranceResquestVO;
import com.kbao.tool.util.CopyUtils;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;

/**
 * <AUTHOR>
 * @date 2025/8/28 15:46
 */
@Service
public class InsuranceService extends BaseMongoServiceImpl<Insurance, String, InsuranceDao> {

    /**
     * 查所有险种，根据sort升序
     *
     * @param
     * <AUTHOR>
     * @Date 2025/8/28 16:02
     */
    public List<InsuranceResponseVO> list() {
        Query query = new Query();
        // 构建排序条件
        query.with(Sort.by(Sort.Order.asc("sort")));
        List<Insurance> list = dao.find(query);
        return CopyUtils.copyList(list, InsuranceResponseVO.class);
    }

    /**
     * 新增险种
     * 
     * @param insurance
     * <AUTHOR>
     * @Date 2025/8/29 11:46
     */
    public void saveInsurance(Insurance insurance) {

        Query query = new Query();
        query.addCriteria(Criteria.where("insuranceName").is(insurance.getInsuranceName()));
        List<Insurance> list = dao.find(query);
        if (EmptyUtils.isNotEmpty(list)) {
            throw new BusinessException("该险种已存在，请勿重复添加");
        }

        insurance.setInsuranceId(IdWorker.getIdStr());
        insurance.setCreateTime(DateUtils.getCurrentDate());
        insurance.setCreateId(SysLoginUtils.getUserId());
        dao.save(insurance);
    }

    /**
     * 删除
     *
     * @param id
     * <AUTHOR>
     * @Date 2025/8/28 16:03
     */
    public void deleteInsurance(String id) {
        if (EmptyUtils.isEmpty(id)) {
            throw new BusinessException("id不能为空");
        }
        dao.remove(id);
        // 更新排序
        Query query = new Query();
        // 构建排序条件
        query.with(Sort.by(Sort.Order.asc("sort")));
        List<Insurance> list = dao.find(query);
        if (EmptyUtils.isNotEmpty(list)) {
            int sort = 1;
            for (int i = 0; i < list.size(); i++) {
                Insurance insurance = list.get(i);
                insurance.setSort(sort);
                insurance.setUpdateTime(DateUtils.getCurrentDate());
                insurance.setUpdateId(SysLoginUtils.getUserId());
                dao.update(insurance);
                sort++;
            }
        }
    }

    /**
     * 修改排序
     *
     * @param resquestVO
     * <AUTHOR>
     * @Date 2025/8/28 16:04
     */
    public void updateSort(InsuranceResquestVO resquestVO) {
        if (EmptyUtils.isEmpty(resquestVO.getList())) {
            throw new BusinessException("险种数据不能为空");
        }
        int sort = 1;
        for (int i = 0; i < resquestVO.getList().size(); i++) {
            Insurance insurance = resquestVO.getList().get(i);
            Insurance old = dao.findById(insurance.getInsuranceId());
            if (EmptyUtils.isNotEmpty(old)) {
                old.setSort(sort);
                old.setUpdateTime(DateUtils.getCurrentDate());
                old.setUpdateId(SysLoginUtils.getUserId());
                dao.update(old);
                sort++;
            }

        }
    }

}

package com.kbao.kbcelms.enterpriseopplock.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLockRecord;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 企业机会锁定记录MongoDB DAO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Repository
public class EnterpriseOppLockRecordDao extends BaseMongoDaoImpl<EnterpriseOppLockRecord, String>{
    
    /**
     * 根据锁定主表ID查询记录列表
     * @param lockId 锁定主表ID
     * @return 记录列表
     */
    public List<EnterpriseOppLockRecord> findByLockId(Integer lockId) {
        Query query = new Query();
        query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("lockId").is(lockId)
                .and("isDeleted").is(0));
        return this.find(query);
    }
    
    /**
     * 根据锁定主表ID查询最新有效记录
     * @param lockId 锁定主表ID
     * @return 最新有效记录
     */
    public EnterpriseOppLockRecord findLatestValidByLockId(Integer lockId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("lockId").is(lockId)
                .and("status").is(1)
                .and("isDeleted").is(0));
        query.with(org.springframework.data.domain.Sort.by(
                org.springframework.data.domain.Sort.Direction.DESC, "createTime"));
        query.limit(1);
        return this.findOne(query);
    }
    
    /**
     * 根据锁定主表ID批量查询最新有效记录
     * @param lockIds 锁定主表ID列表
     * @return 最新有效记录列表
     */
    public List<EnterpriseOppLockRecord> findLatestValidByLockIds(List<Integer> lockIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("lockId").in(lockIds)
                .and("status").is(1)
                .and("isDeleted").is(0));
        query.with(Sort.by(
                Sort.Direction.DESC, "createTime"));
        return this.find(query);
    }
    
    /**
     * 根据锁定人查询记录列表
     * @param lockUser 锁定人
     * @return 记录列表
     */
    public List<EnterpriseOppLockRecord> findByLockUser(String lockUser) {
        Query query = new Query();
        query.addCriteria(Criteria.where("lockUser").is(lockUser)
                .and("isDeleted").is(0));
        query.with(Sort.by(
                Sort.Direction.DESC, "createTime"));
        return this.find(query);
    }
    
    /**
     * 设置记录状态为无效
     * @param lockId 锁定主表ID
     * @param updateId 更新人
     * @return 更新结果
     */
    public long invalidateByLockId(Integer lockId, String updateId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("lockId").is(lockId)
                .and("status").is(1)
                .and("isDeleted").is(0));
        
        Update update = new Update();
        update.set("status", 0);
        update.set("updateId", updateId);
        update.set("updateTime", new java.util.Date());
        return this.update(query, update).getModifiedCount();
    }
    
    /**
     * 逻辑删除记录
     * @param id 主键
     * @param updateId 更新人
     * @return 更新结果
     */
    public long deleteLogicalById(String id, String updateId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("id").is(id));
        
        Update update = new Update();
        update.set("isDeleted", 1);
        update.set("updateId", updateId);
        update.set("updateTime", new Date());
        
        return this.update(query, update).getModifiedCount();
    }
}

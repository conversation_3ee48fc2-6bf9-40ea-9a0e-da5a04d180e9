package com.kbao.kbcelms.enterpriseopplock.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;
import org.springframework.beans.factory.annotation.Autowired;
import com.kbao.kbcelms.enterpriseopplock.dao.EnterpriseOppLockRecordDao;
import com.kbao.kbcelms.enterpriseopplock.service.EnterpriseOppLockService;

import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockRecordQueryDTO;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLockRecord;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 企业机会锁定记录Service（不进行租户隔离）
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class EnterpriseOppLockRecordService  extends BaseMongoServiceImpl<EnterpriseOppLockRecord, String, EnterpriseOppLockRecordDao> {
    
    private static final Logger logger = LoggerFactory.getLogger(EnterpriseOppLockRecordService.class);
    
    @Autowired
    private EnterpriseOppLockRecordDao dao;
    
    @Autowired
    private EnterpriseOppLockService enterpriseOppLockService;
    
    /**
     * 查询企业机会锁定记录列表
     * @param queryDTO 查询条件
     * @return 记录列表
     */
    public Result<List<EnterpriseOppLockRecord>> getList(EnterpriseOppLockRecordQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new EnterpriseOppLockRecordQueryDTO();
            }
            
            // 构建查询条件
            Query query = buildQuery(queryDTO);
            
            // 设置排序（按创建时间倒序）
            query.with(Sort.by(Sort.Direction.DESC, "createTime"));
            
            // 查询所有记录
            List<EnterpriseOppLockRecord> records = this.dao.find(query);
            
            return Result.succeed(records, "查询成功");
        } catch (Exception e) {
            logger.error("查询企业机会锁定记录列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据机会ID查询企业机会锁定记录列表
     * @param opportunityId 机会ID
     * @return 锁定记录列表
     */
    public List<EnterpriseOppLockRecord> getListByOpportunityId(Integer opportunityId) {
        if (opportunityId == null) {
            throw new BusinessException("机会ID不能为空");
        }
        
        // 1. 根据机会ID查询EnterpriseOppLock表，获取锁定记录ID
        EnterpriseOppLock lockRecord = enterpriseOppLockService.getByOpportunityId(opportunityId);
        if (lockRecord == null) {
            // 该机会未锁定，返回空列表
            return new ArrayList<>();
        }
        
        // 2. 根据锁定记录ID查询EnterpriseOppLockRecord列表
        String lockId = lockRecord.getId().toString();
        Query query = new Query();
        query.addCriteria(Criteria.where("lockId").is(lockId));
        query.addCriteria(Criteria.where("isDeleted").is(0));
        
        // 设置排序（按创建时间倒序）
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        
        // 查询所有记录
        return this.dao.find(query);
    }
    
    /**
     * 根据ID查询企业机会锁定记录详情
     * @param id 主键ID
     * @return 详情信息
     */
    public EnterpriseOppLockRecord getById(String id) {
        try {
            if (id == null || id.trim().isEmpty()) {
                return null;
            }

            EnterpriseOppLockRecord entity = this.dao.findById(id);
            if (entity == null) {
                return null;
            }
            return entity;
        } catch (Exception e) {
            logger.error("根据ID查询企业机会锁定记录详情失败", e);
            return null;
        }
    }
    
    /**
     * 新增企业机会锁定记录
     * @param entity 新增实体
     * @return 新增结果
     */
    public String add(EnterpriseOppLockRecord entity) {
        try {
            // 验证文件数量限制
            if (entity.getLockFiles() != null && entity.getLockFiles().size() > 10) {
                throw new BusinessException("锁定文件数量不能超过10个");
            }
            
            // 设置创建信息
            String currentUserId = ElmsContext.getUser().getUserId(); // 临时设置，实际应该从上下文获取
            Date now = new Date();
            entity.setCreateId(currentUserId);
            entity.setCreateTime(now);
            entity.setUpdateId(currentUserId);
            entity.setUpdateTime(now);
            entity.setIsDeleted(0);
            entity.setStatus(1); // 默认状态为有效
            
            // 如果没有设置锁定时间，默认为当前时间
            if (entity.getLockTime() == null) {
                entity.setLockTime(now);
            }
            
            EnterpriseOppLockRecord savedEntity = this.dao.save(entity);
            if (savedEntity != null && savedEntity.getId() != null) {
               return savedEntity.getId();
            } else {
                throw new BusinessException("新增失败");
            }
        } catch (Exception e) {
            logger.error("新增企业机会锁定记录失败", e);
            throw new BusinessException("新增失败：" + e.getMessage());
        }
    }
    

    

    

    
    /**
     * 设置记录状态为无效
     * @param lockId 锁定主表ID
     * @return 操作结果
     */
    public void invalidateByLockId(Integer lockId,String currentUserId) {
        this.dao.invalidateByLockId(lockId, currentUserId);
    }
    

    
    /**
     * 构建查询条件（不进行租户隔离）
     * @param queryDTO 查询DTO
     * @return MongoDB查询对象
     */
    private Query buildQuery(EnterpriseOppLockRecordQueryDTO queryDTO) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        
        // 添加基础过滤条件（移除租户隔离逻辑）
        criteria.and("isDeleted").is(0);
        
        if (queryDTO.getLockId() != null) {
            criteria.and("lockId").is(queryDTO.getLockId());
        }
        
        if (queryDTO.getCreditCode() != null && !queryDTO.getCreditCode().trim().isEmpty()) {
            criteria.and("creditCode").is(queryDTO.getCreditCode());
        }
        
        if (queryDTO.getOpportunityType() != null && !queryDTO.getOpportunityType().trim().isEmpty()) {
            criteria.and("opportunityType").is(queryDTO.getOpportunityType());
        }
        
        if (queryDTO.getLockUser() != null && !queryDTO.getLockUser().trim().isEmpty()) {
            criteria.and("lockUser").regex(queryDTO.getLockUser(), "i");
        }
        
        if (queryDTO.getStatus() != null) {
            criteria.and("status").is(queryDTO.getStatus());
        }
        
        if (queryDTO.getLockTimeStart() != null) {
            criteria.and("lockTime").gte(queryDTO.getLockTimeStart());
        }
        
        if (queryDTO.getLockTimeEnd() != null) {
            criteria.and("lockTime").lte(queryDTO.getLockTimeEnd());
        }
        
        if (queryDTO.getLockEndTimeStart() != null) {
            criteria.and("lockEndTime").gte(queryDTO.getLockEndTimeStart());
        }
        
        if (queryDTO.getLockEndTimeEnd() != null) {
            criteria.and("lockEndTime").lte(queryDTO.getLockEndTimeEnd());
        }
        
        if (queryDTO.getCreateTimeStart() != null) {
            criteria.and("createTime").gte(queryDTO.getCreateTimeStart());
        }
        
        if (queryDTO.getCreateTimeEnd() != null) {
            criteria.and("createTime").lte(queryDTO.getCreateTimeEnd());
        }
        
        query.addCriteria(criteria);
        return query;
    }
    

    

    

    
    /**
     * 根据记录ID更新MongoDB记录
     * @param recordId MongoDB记录ID
     * @param updateEntity 更新的实体
     * @return 更新结果
     */
    public Result<String> updateByRecordId(String recordId, EnterpriseOppLockRecord updateEntity) {
        try {
            if (recordId == null || recordId.trim().isEmpty()) {
                return Result.failed("记录ID不能为空");
            }
            
            // 1. 查询现有记录
            EnterpriseOppLockRecord existingRecord = dao.findById(recordId);
            if (existingRecord == null) {
                return Result.failed("记录不存在");
            }
            
            // 2. 更新字段
            boolean hasUpdates = false;
            
            // 更新锁定截止时间
            if (updateEntity.getLockEndTime() != null) {
                existingRecord.setLockEndTime(updateEntity.getLockEndTime());
                hasUpdates = true;
            }
            
            // 更新文件清单
            if (updateEntity.getLockFiles() != null) {
                existingRecord.setLockFiles(updateEntity.getLockFiles());
                hasUpdates = true;
            }
            
            // 更新备注信息
            if (updateEntity.getRemark() != null) {
                existingRecord.setRemark(updateEntity.getRemark());
                hasUpdates = true;
            }
            
            // 如果有更新，则保存
            if (hasUpdates) {
                existingRecord.setUpdateId("system"); // 临时设置，实际应该从上下文获取
                existingRecord.setUpdateTime(new Date());
                
                EnterpriseOppLockRecord savedRecord = dao.save(existingRecord);
                if (savedRecord == null) {
                    return Result.failed("更新失败");
                }
            }
            
            return Result.succeed("更新成功");
            
        } catch (Exception e) {
            logger.error("更新MongoDB记录失败，recordId: {}", recordId, e);
            return Result.failed("更新失败：" + e.getMessage());
        }
    }
}

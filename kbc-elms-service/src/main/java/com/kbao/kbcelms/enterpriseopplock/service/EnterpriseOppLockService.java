package com.kbao.kbcelms.enterpriseopplock.service;


import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcelms.enterpriseopplock.dao.EnterpriseOppLockMapper;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockEditDTO;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLockRecord;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.service.OpportunityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业机会锁定Service
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class EnterpriseOppLockService extends BaseSQLServiceImpl<EnterpriseOppLock, Integer, EnterpriseOppLockMapper> {
    
    private static final Logger logger = LoggerFactory.getLogger(EnterpriseOppLockService.class);
    
    @Autowired
    private OpportunityService opportunityService;
    
    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;
    
    @Autowired
    private EnterpriseOppLockRecordService enterpriseOppLockRecordService;
    
    /**
     * 根据ID查询企业机会锁定详情
     * @param id 主键ID
     * @return 详情信息
     */
    public EnterpriseOppLock getByOpportunityId(Integer id) {
        if (id == null) {
            throw new BusinessException("ID不能为空");
        }
        
        // 1. 先根据机会ID查询锁定记录
        EnterpriseOppLock entity = this.mapper.selectByOpportunityId(id);
        
        // 2. 如果根据机会ID查不到，则根据机会类型+企业统一编码查询
        if (entity == null) {
            // 查询机会信息，获取机会类型和企业信息
            Opportunity opportunity = opportunityService.selectByPrimaryKey(id);
            if (opportunity == null) {
                return null; // 机会不存在
            }
            
            String opportunityType = opportunity.getOpportunityType();
            if (opportunityType == null || opportunityType.trim().isEmpty()) {
                return null; // 机会类型为空
            }
            
            // 查询关联企业信息
            String creditCode = null;
            if (opportunity.getAgentEnterpriseId() != null) {
                GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(opportunity.getAgentEnterpriseId());
                if (enterprise != null) {
                    creditCode = enterprise.getCreditCode();
                }
            }
            
            if (creditCode == null || creditCode.trim().isEmpty()) {
                return null; // 企业信用代码为空
            }
            
            // 根据机会类型+企业统一编码查询锁定记录
            entity = this.mapper.selectByOpportunityTypeAndCreditCode(opportunityType, creditCode);
            if (entity == null) {
                return null; // 仍然没有找到锁定记录
            }
        }
        
        // 3. 补充MongoDB中的详细信息
        EnterpriseOppLockRecord record = this.enterpriseOppLockRecordService.getById(entity.getCurrentRecordId());
        if (record != null) {
            entity.setLockFiles(record.getLockFiles());
            entity.setRemark(record.getRemark());
        }
        
        return entity;
    }
    
    /**
     * 锁定机会
     * @param lockDTO 锁定参数
     * @return 锁定结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<String> lockOpportunity(EnterpriseOppLock lockDTO) {
        try {
            if (lockDTO.getOpportunityId() == null) {
                return Result.failed("机会ID不能为空");
            }
            
            Integer opportunityId = lockDTO.getOpportunityId();
            String currentUserId = ElmsContext.getUser().getUserId();// 临时设置，实际应该从上下文获取
            Date currentTime = new Date();
            
            // 1. 查询机会信息，获取机会类型和关联企业信息
            Opportunity opportunity = opportunityService.selectByPrimaryKey(opportunityId);
            if (opportunity == null) {
                return Result.failed("机会不存在");
            }
            
            String opportunityType = opportunity.getOpportunityType();
            if (opportunityType == null || opportunityType.trim().isEmpty()) {
                return Result.failed("机会类型不能为空");
            }
            
            // 查询关联企业信息
            GenAgentEnterprise enterprise = null;
            String creditCode = null;
            String enterpriseName = null;
            
            if (opportunity.getAgentEnterpriseId() != null) {
                enterprise = genAgentEnterpriseService.selectByPrimaryKey(opportunity.getAgentEnterpriseId());
                if (enterprise != null) {
                    creditCode = enterprise.getCreditCode();
                    enterpriseName = enterprise.getName();
                }
            }
            
            if (creditCode == null || creditCode.trim().isEmpty()) {
                return Result.failed("机会关联的企业信息不完整，无法进行锁定");
            }
            
            // 2. 检查当前机会是否已被锁定
            EnterpriseOppLock existingLock = this.mapper.selectByOpportunityTypeAndCreditCode(opportunityType, creditCode);
            if (existingLock != null && !isLockExpired(existingLock.getLockEndTime())) {
                return Result.failed("该机会已被锁定，无法进行锁定操作");
            }

            // 4. 创建新的锁定记录
            EnterpriseOppLock newLock = new EnterpriseOppLock();
            newLock.setOpportunityId(opportunityId);
            newLock.setOpportunityType(opportunityType);
            newLock.setName(enterpriseName);
            newLock.setCreditCode(creditCode);
            newLock.setLockUser(currentUserId);
            newLock.setLockTime(currentTime);
            newLock.setLockEndTime(lockDTO.getLockEndTime());
            newLock.setCreateId(currentUserId);
            newLock.setCreateTime(currentTime);
            newLock.setUpdateId(currentUserId);
            newLock.setUpdateTime(currentTime);
            newLock.setIsDeleted(0);
            
            // 保存锁定记录
            Integer lockResult = super.insertSelective(newLock);
            if (lockResult == null || lockResult <= 0) {
                return Result.failed("创建锁定记录失败");
            }
            
            // 5. 更新机会表的锁定状态
            // 将当前机会状态设置为已锁定(1)
            updateOpportunityLockStatus(opportunityId, 1,currentUserId); // 1-已锁定
            
            // 同机会类型+同企业的其他进行中机会设置为被锁定(2)
            updateOtherOpportunitiesLockStatus(opportunityType, creditCode, opportunityId, 2); // 2-被锁定
            
            // 6. 创建锁定记录到MongoDB
            if (lockDTO.getLockFiles() != null && !lockDTO.getLockFiles().isEmpty()) {
                // 创建MongoDB记录
                String recordId = createLockRecord(newLock.getId(), lockDTO, creditCode, opportunityType);
                if (recordId != null) {
                    // 更新主表的current_record_id
                    newLock.setCurrentRecordId(recordId);
                    super.updateByPrimaryKeySelective(newLock);
                }
            }
            
            return Result.succeed(newLock.getId().toString(), "机会锁定成功");
            
        } catch (Exception e) {
            logger.error("锁定机会失败", e);
            return Result.failed("锁定机会失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查锁定记录是否已过期
     * @param lockEndTime 锁定截止时间
     * @return 是否已过期
     */
    private boolean isLockExpired(Date lockEndTime) {
        if (lockEndTime == null) {
            return false; // 没有截止时间的锁定认为未过期
        }
        return new Date().after(lockEndTime);
    }
    
    /**
     * 创建锁定记录到MongoDB
     * @param lockId 锁定主表ID
     * @param lockDTO 锁定参数
     * @param creditCode 企业统一社会信用代码
     * @param opportunityType 机会类型
     * @return 记录ID
     */
    private String createLockRecord(Integer lockId, EnterpriseOppLock lockDTO, String creditCode, String opportunityType) {
        try {
            EnterpriseOppLockRecord record = new EnterpriseOppLockRecord();
            record.setLockId(lockId);
            record.setCreditCode(creditCode);
            record.setOpportunityType(opportunityType);
            record.setLockUser(ElmsContext.getUser().getUserId()); // 临时设置，实际应该从上下文获取
            record.setLockTime(new Date());
            record.setLockEndTime(lockDTO.getLockEndTime());
            record.setStatus(1); // 1-有效
            record.setIsDeleted(0); // 0-未删除
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());
            record.setLockFiles(lockDTO.getLockFiles());
            return enterpriseOppLockRecordService.add(record);
        } catch (Exception e) {
            logger.error("创建锁定记录失败", e);
            return null;
        }
    }
    
    /**
     * 更新机会的锁定状态
     * @param opportunityId 机会ID
     * @param lockStatus 锁定状态
     */
    private void updateOpportunityLockStatus(Integer opportunityId, Integer lockStatus,String userId) {
        try {
            Opportunity opportunity = opportunityService.selectByPrimaryKey(opportunityId);
            if (opportunity != null) {
                opportunity.setLockStatus(lockStatus);
                opportunity.setUpdateTime(new Date());
                opportunity.setUpdateId(userId); // 临时设置，实际应该从上下文获取
                opportunityService.updateByPrimaryKey(opportunity);
                logger.info("更新机会锁定状态成功，机会ID：{}，锁定状态：{}", opportunityId, lockStatus);
            }
        } catch (Exception e) {
            logger.error("更新机会锁定状态失败，机会ID：{}，锁定状态：{}", opportunityId, lockStatus, e);
        }
    }
    
    /**
     * 更新其他机会的锁定状态
     * @param opportunityType 机会类型
     * @param creditCode 企业信用代码
     * @param currentOpportunityId 当前机会ID（排除）
     * @param lockStatus 锁定状态
     */
    private void updateOtherOpportunitiesLockStatus(String opportunityType, String creditCode, Integer currentOpportunityId, Integer lockStatus) {
        try {
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            
            // 1. 根据机会类型和企业信用代码查询其他进行中机会
            List<Opportunity> otherOpportunities = opportunityService.selectActiveOpportunitiesByTypeAndCreditCode(
                opportunityType, creditCode, currentOpportunityId);
            
            if (otherOpportunities == null || otherOpportunities.isEmpty()) {
                logger.info("没有找到需要更新锁定状态的其他机会，机会类型：{}，企业信用代码：{}，排除机会ID：{}", 
                           opportunityType, creditCode, currentOpportunityId);
                return;
            }
            
            // 2. 提取机会ID列表
            List<Integer> opportunityIds = otherOpportunities.stream()
                .map(Opportunity::getId)
                .collect(Collectors.toList());
            
            // 3. 批量更新锁定状态
            int updateCount = opportunityService.batchUpdateLockStatus(opportunityIds, lockStatus, currentUserId);
            
            logger.info("批量更新其他机会锁定状态完成，机会类型：{}，企业信用代码：{}，排除机会ID：{}，目标状态：{}，更新数量：{}", 
                       opportunityType, creditCode, currentOpportunityId, lockStatus, updateCount);
            
            // 5. 记录详细的更新信息（用于调试和审计）
            if (logger.isDebugEnabled()) {
                String updatedOpportunityIds = opportunityIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
                logger.debug("更新的机会ID列表：[{}]", updatedOpportunityIds);
            }
            
        } catch (Exception e) {
            logger.error("更新其他机会锁定状态失败，机会类型：{}，企业信用代码：{}，排除机会ID：{}，目标状态：{}", 
                        opportunityType, creditCode, currentOpportunityId, lockStatus, e);
            // 这里不抛出异常，避免影响主要的锁定流程
        }
    }
    
    /**
     * 解锁机会
     * @param opportunityId 机会ID
     * @return 解锁结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<String> unlockOpportunity(Integer opportunityId) {
        try {
            if (opportunityId == null) {
                return Result.failed("机会ID不能为空");
            }
            
            String currentUserId = ElmsContext.getUser().getUserId(); // 临时设置，实际应该从上下文获取
            
            // 1. 查询机会信息
            Opportunity opportunity = opportunityService.selectByPrimaryKey(opportunityId);
            if (opportunity == null) {
                return Result.failed("机会不存在");
            }
            
            // 2. 查询关联企业信息
            GenAgentEnterprise enterprise = null;
            String creditCode = null;
            
            if (opportunity.getAgentEnterpriseId() != null) {
                enterprise = genAgentEnterpriseService.selectByPrimaryKey(opportunity.getAgentEnterpriseId());
                if (enterprise != null) {
                    creditCode = enterprise.getCreditCode();
                }
            }
            
            if (creditCode == null || creditCode.trim().isEmpty()) {
                return Result.failed("机会关联的企业信息不完整，无法进行解锁");
            }
            
            // 3. 查询锁定记录
            EnterpriseOppLock lockRecord = this.mapper.selectByOpportunityTypeAndCreditCode(opportunity.getOpportunityType(), creditCode);
            if (lockRecord == null) {
                return Result.failed("该机会未被锁定");
            }else if (lockRecord.getOpportunityId().equals(opportunityId)){
                // 4. 删除锁定记录（逻辑删除）
                int deleteResult = this.mapper.deleteLogical(lockRecord.getId(), currentUserId);
                if (deleteResult <= 0) {
                    return Result.failed("删除锁定记录失败");
                }
            }

            // 5. 更新机会锁定状态为已解锁
            updateOpportunityLockStatus(opportunityId, 3,currentUserId); // 3-已解锁
            
            // 6. 将MongoDB中对应记录状态设置为无效
            if (lockRecord.getCurrentRecordId() != null && !lockRecord.getCurrentRecordId().trim().isEmpty()) {
                enterpriseOppLockRecordService.invalidateByLockId(lockRecord.getId(),currentUserId);
            }
            
            logger.info("机会解锁成功，机会ID：{}，锁定记录ID：{}", opportunityId, lockRecord.getId());
            return Result.succeed("机会解锁成功");
            
        } catch (Exception e) {
            logger.error("解锁机会失败", e);
            return Result.failed("解锁机会失败：" + e.getMessage());
        }
    }
    
    /**
     * 编辑锁定信息
     * @param editDTO 编辑请求参数
     * @return 编辑结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<String> editLockInfo(EnterpriseOppLockEditDTO editDTO) {
        try {
            if (editDTO == null || editDTO.getLockId() == null) {
                return Result.failed("锁定记录ID不能为空");
            }
            
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            
            // 1. 查询锁定记录是否存在
            EnterpriseOppLock lockEntity = super.selectByPrimaryKey(editDTO.getLockId());
            if (lockEntity == null) {
                return Result.failed("锁定记录不存在");
            }
            
            // 转换为VO对象以便访问属性
            EnterpriseOppLock lockRecord = new EnterpriseOppLock();
            BeanUtils.copyProperties(lockEntity, lockRecord);
            
            // 2. 检查锁定记录是否过期
            if (lockRecord.getLockEndTime() != null && lockRecord.getLockEndTime().before(new Date())) {
                return Result.failed("该锁定记录已过期，无法编辑");
            }
            
            // 3. 判断需要更新的内容
            boolean needUpdateMySQL = false;
            boolean needUpdateMongoDB = false;
            
            // 检查MySQL更新需求
            if (editDTO.getLockEndTime() != null && !editDTO.getLockEndTime().equals(lockRecord.getLockEndTime())) {
                needUpdateMySQL = true;
            }
            
            // 检查MongoDB更新需求（包括lockEndTime变更）
            if (editDTO.getLockFiles() != null || editDTO.getRemark() != null || needUpdateMySQL) {
                needUpdateMongoDB = true;
            }
            
            // 4. 更新MySQL中的锁定截止时间（如果有变更）
            if (needUpdateMySQL) {
                EnterpriseOppLock updateLock = new EnterpriseOppLock();
                updateLock.setId(editDTO.getLockId());
                updateLock.setLockEndTime(editDTO.getLockEndTime());
                updateLock.setUpdateId(currentUserId);
                updateLock.setUpdateTime(new Date());
                
                int updateResult = super.updateByPrimaryKeySelective(updateLock);
                if (updateResult <= 0) {
                    return Result.failed("更新锁定记录失败");
                }
                logger.info("MySQL锁定记录更新成功，锁定记录ID：{}，新截止时间：{}", 
                           editDTO.getLockId(), editDTO.getLockEndTime());
            }
            
            // 5. 更新MongoDB中的记录（文件清单、备注信息和锁定截止时间）
            if (needUpdateMongoDB) {
                String recordId = lockRecord.getCurrentRecordId();
                if (recordId != null && !recordId.trim().isEmpty()) {
                    // 创建更新用的实体
                    EnterpriseOppLockRecord recordUpdate = new EnterpriseOppLockRecord();
                    recordUpdate.setLockId(editDTO.getLockId());
                    
                    // 设置文件清单
                    if (editDTO.getLockFiles() != null) {
                        List<EnterpriseOppLockRecord.LockFileInfo> mongoFiles = new ArrayList<>();
                        for (EnterpriseOppLockEditDTO.LockFileInfo editFile : editDTO.getLockFiles()) {
                            EnterpriseOppLockRecord.LockFileInfo mongoFile = new EnterpriseOppLockRecord.LockFileInfo();
                            mongoFile.setFileName(editFile.getFileName());
                            mongoFile.setFileUrl(editFile.getFileUrl());
                            mongoFile.setFileSize(editFile.getFileSize());
                            mongoFile.setFileType(editFile.getFileType());
                            mongoFile.setUploadTime(editFile.getUploadTime() != null ? editFile.getUploadTime() : new Date());
                            mongoFiles.add(mongoFile);
                        }
                        recordUpdate.setLockFiles(mongoFiles);
                    }
                    
                    // 设置备注信息
                    if (editDTO.getRemark() != null) {
                        recordUpdate.setRemark(editDTO.getRemark());
                    }
                    
                    // 设置锁定截止时间（如果发生变更，同步更新MongoDB）
                    if (editDTO.getLockEndTime() != null) {
                        recordUpdate.setLockEndTime(editDTO.getLockEndTime());
                    }
                    
                    // 调用MongoDB记录服务的更新方法
                    Result<String> updateRecordResult = enterpriseOppLockRecordService.updateByRecordId(recordId, recordUpdate);
                    if (!"SUCCESS".equals(updateRecordResult.getResp_code())) {
                        logger.warn("更新MongoDB记录失败：{}", updateRecordResult.getResp_msg());
                        return Result.failed("更新锁定记录详情失败：" + updateRecordResult.getResp_msg());
                    }
                    logger.info("MongoDB记录更新成功，记录ID：{}", recordId);
                } else {
                    logger.warn("锁定记录缺少MongoDB记录ID，无法更新详情信息，锁定记录ID：{}", editDTO.getLockId());
                }
            }
            
            // 6. 记录操作日志
            StringBuilder logMessage = new StringBuilder("编辑锁定信息成功，锁定记录ID：").append(editDTO.getLockId());
            if (needUpdateMySQL) {
                logMessage.append("，MySQL已更新");
            }
            if (needUpdateMongoDB) {
                logMessage.append("，MongoDB已更新");
            }
            if (!needUpdateMySQL && !needUpdateMongoDB) {
                logMessage.append("，无实际变更");
            }
            
            logger.info(logMessage.toString());
            return Result.succeed("编辑锁定信息成功");
            
        } catch (Exception e) {
            logger.error("编辑锁定信息失败，锁定记录ID：{}", editDTO != null ? editDTO.getLockId() : "null", e);
            return Result.failed("编辑锁定信息失败：" + e.getMessage());
        }
    }

    public boolean hasLockOpp(String creditCode, String opportunityType) {
        return mapper.hasLockOpp(creditCode, opportunityType) > 0;
    }
}


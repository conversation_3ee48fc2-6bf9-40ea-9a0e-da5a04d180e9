package com.kbao.kbcelms.enterpriseopplock.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业机会锁定Mapper
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface EnterpriseOppLockMapper extends BaseMapper<EnterpriseOppLock, Integer> {
    


    /**
     * 逻辑删除
     * @param id 主键
     * @param updateId 更新人
     * @return 影响行数
     */
    int deleteLogical(@Param("id") Integer id, @Param("updateId") String updateId);
    
    /**
     * 根据机会类型和社会统一信用代码查询锁定记录列表
     * @param opportunityType 机会类型
     * @param creditCode 社会统一信用代码
     * @return 锁定记录列表
     */
    EnterpriseOppLock selectByOpportunityTypeAndCreditCode(@Param("opportunityType") String opportunityType, @Param("creditCode") String creditCode);

    int hasLockOpp(@Param("creditCode") String creditCode, @Param("opportunityType") String opportunityType);

    /**
     * 根据机会ID查询锁定记录
     * @param opportunityId 机会ID
     * @return 锁定记录
     */
    EnterpriseOppLock selectByOpportunityId(@Param("opportunityId") Integer opportunityId);
    
    /**
     * 根据机会ID和社会统一信用代码查询锁定记录
     * @param opportunityId 机会ID
     * @param creditCode 社会统一信用代码
     * @return 锁定记录
     */
    EnterpriseOppLock selectByOpportunityIdAndCreditCode(@Param("opportunityId") Integer opportunityId, @Param("creditCode") String creditCode);
    
}

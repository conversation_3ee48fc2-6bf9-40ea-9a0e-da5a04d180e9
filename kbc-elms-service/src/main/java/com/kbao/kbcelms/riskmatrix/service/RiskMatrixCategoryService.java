package com.kbao.kbcelms.riskmatrix.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixRequest;
import com.kbao.kbcelms.riskmatrix.dao.RiskMatrixCategoryMapper;
import com.kbao.kbcelms.riskmatrix.entity.RiskMatrixCategory;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixCategoryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 风险矩阵类别服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class RiskMatrixCategoryService extends BaseSQLServiceImpl<RiskMatrixCategory, Long, RiskMatrixCategoryMapper> {
    
    @Autowired
    private RiskMatrixLevelService levelService;

    @Autowired
    private CategoryScoreItemService categoryScoreItemService;

    @Autowired
    private ScoreItemService scoreItemService;

    /**
     * 将评分项ID列表转换为逗号分隔的字符串
     *
     * @param scoreItemIds 评分项ID列表
     * @return 逗号分隔的字符串
     */
    private String convertScoreItemIdsToString(List<Long> scoreItemIds) {
        if (CollectionUtils.isEmpty(scoreItemIds)) {
            return null;
        }
        return scoreItemIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    /**
     * 将逗号分隔的字符串转换为评分项ID列表
     *
     * @param scoreIds 逗号分隔的字符串
     * @return 评分项ID列表
     */
    private List<Long> convertStringToScoreItemIds(String scoreIds) {
        if (!StringUtils.hasText(scoreIds)) {
            return new ArrayList<>();
        }
        return Arrays.stream(scoreIds.split(","))
                .filter(StringUtils::hasText)
                .map(String::trim)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 根据矩阵ID查询类别列表
     *
     * @param matrixId 矩阵ID
     * @return 类别列表
     */
    public List<RiskMatrixCategory> getByMatrixId(Long matrixId) {
        return mapper.selectByMatrixId(matrixId);
    }
    
    /**
     * 保存类别配置
     *
     * @param matrixId 矩阵ID
     * @param categories 类别列表
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCategories(Long matrixId, List<RiskMatrixCategory> categories) {
        if (matrixId == null || CollectionUtils.isEmpty(categories)) {
            return false;
        }

        // 先删除原有类别配置（包括档次配置）
        deleteByMatrixId(matrixId);

        // 设置矩阵ID和时间
        LocalDateTime now = LocalDateTime.now();
        for (RiskMatrixCategory category : categories) {
            category.setMatrixId(matrixId);
            category.setCreateTime(now);
            category.setUpdateTime(now);
        }

        // 批量插入新的类别配置
        return mapper.batchInsert(categories) > 0;
    }

    /**
     * 保存风险矩阵类别配置（从请求参数）
     *
     * @param matrixId 矩阵ID
     * @param categoryRequests 类别请求参数列表
     * @param currentUser 当前用户
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCategoriesForMatrix(Long matrixId, List<RiskMatrixRequest.CategoryRequest> categoryRequests, String currentUser) {
        if (matrixId == null || CollectionUtils.isEmpty(categoryRequests)) {
            log.warn("保存类别配置失败：矩阵ID或类别列表为空");
            return false;
        }

        log.info("开始保存风险矩阵类别配置，矩阵ID：{}，类别数量：{}", matrixId, categoryRequests.size());

        try {
            // 先删除原有类别配置（包括档次配置）
            deleteByMatrixId(matrixId);

            // 转换并插入新类别配置
            List<RiskMatrixCategory> categories = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (int i = 0; i < categoryRequests.size(); i++) {
                RiskMatrixRequest.CategoryRequest categoryRequest = categoryRequests.get(i);
                RiskMatrixCategory category = convertRequestToEntity(categoryRequest, matrixId, i + 1, now, currentUser);
                categories.add(category);
            }

            // 批量插入类别配置
            if (!CollectionUtils.isEmpty(categories)) {
                // 逐个插入类别配置以获取生成的ID
                for (int i = 0; i < categories.size(); i++) {
                    RiskMatrixCategory category = categories.get(i);
                    int result = mapper.insertSelective(category);
                    if (result <= 0) {
                        log.error("插入类别配置失败，矩阵ID：{}，类别名称：{}", matrixId, category.getName());
                        return false;
                    }

                    // 保存该类别的档次配置
                    RiskMatrixRequest.CategoryRequest categoryRequest = categoryRequests.get(i);
                    if (!CollectionUtils.isEmpty(categoryRequest.getLevels())) {
                        levelService.saveLevelsFromRequests(category.getId(), categoryRequest.getLevels());
                    }

                    // 关联评分项已在 convertRequestToEntity 方法中处理
                }
            }

            log.info("风险矩阵类别配置保存成功，矩阵ID：{}", matrixId);
            return true;

        } catch (Exception e) {
            log.error("保存风险矩阵类别配置失败，矩阵ID：{}", matrixId, e);
            throw new RuntimeException("保存类别配置失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 根据矩阵ID删除类别配置（包括档次配置）
     *
     * @param matrixId 矩阵ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMatrixId(Long matrixId) {
        if (matrixId == null) {
            return;
        }
        
        // 查询类别信息，用于删除档次配置
        List<RiskMatrixCategory> categories = mapper.selectByMatrixId(matrixId);
        if (!CollectionUtils.isEmpty(categories)) {
            for (RiskMatrixCategory category : categories) {
                // 删除档次配置
                levelService.deleteByCategoryId(category.getId());
            }
        }
        
        // 删除类别配置
        mapper.deleteByMatrixId(matrixId);
    }
    
    /**
     * 批量删除类别配置
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        
        // 删除档次配置
        for (Long id : ids) {
            levelService.deleteByCategoryId(id);
        }
        
        // 删除类别配置
        return mapper.deleteByIds(ids);
    }
    
    /**
     * 更新类别配置
     * 
     * @param category 类别配置
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(RiskMatrixCategory category) {
        if (category == null || category.getId() == null) {
            return false;
        }
        
        category.setUpdateTime(LocalDateTime.now());
        return mapper.updateByPrimaryKeySelective(category) > 0;
    }
    
    /**
     * 新增类别配置
     * 
     * @param category 类别配置
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean insertCategory(RiskMatrixCategory category) {
        if (category == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        category.setCreateTime(now);
        category.setUpdateTime(now);
        
        return mapper.insertSelective(category) > 0;
    }
    
    /**
     * 根据ID删除类别配置（包括档次配置）
     * 
     * @param id 类别ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        if (id == null) {
            return false;
        }
        
        // 删除档次配置
        levelService.deleteByCategoryId(id);
        
        // 删除类别配置
        return mapper.deleteByPrimaryKey(id) > 0;
    }

    /**
     * 将请求参数转换为实体对象
     *
     * @param categoryRequest 类别请求参数
     * @param matrixId 矩阵ID
     * @param sortOrder 排序号
     * @param now 当前时间
     * @param currentUser 当前用户
     * @return 类别实体对象
     */
    private RiskMatrixCategory convertRequestToEntity(RiskMatrixRequest.CategoryRequest categoryRequest,
                                                     Long matrixId, int sortOrder, LocalDateTime now, String currentUser) {
        RiskMatrixCategory category = new RiskMatrixCategory();
        BeanUtils.copyProperties(categoryRequest, category);

        category.setMatrixId(matrixId);
        category.setWeight(categoryRequest.getWeight() != null ?
                BigDecimal.valueOf(categoryRequest.getWeight()) : BigDecimal.ONE);
        category.setCalculationMethod(StringUtils.hasText(categoryRequest.getCalculationMethod()) ?
                categoryRequest.getCalculationMethod() : "sum");
        category.setSortOrder(sortOrder);
        category.setCreateTime(now);
        category.setUpdateTime(now);

        return category;
    }



    /**
     * 保存类别的档次配置（公开方法）
     *
     * @param categoryId 类别ID
     * @param levelRequests 档次请求参数列表
     * @param currentUser 当前用户
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLevelsForCategory(Long categoryId, List<RiskMatrixRequest.LevelRequest> levelRequests, String currentUser) {
        if (categoryId == null || CollectionUtils.isEmpty(levelRequests)) {
            log.warn("保存档次配置失败：类别ID或档次列表为空");
            return false;
        }

        log.info("开始保存类别档次配置，类别ID：{}，档次数量：{}", categoryId, levelRequests.size());

        try {
            boolean success = levelService.saveLevelsFromRequests(categoryId, levelRequests);
            if (success) {
                log.info("类别档次配置保存成功，类别ID：{}", categoryId);
                return true;
            } else {
                log.warn("类别档次配置保存失败，类别ID：{}", categoryId);
                return false;
            }
        } catch (Exception e) {
            log.error("保存类别档次配置失败，类别ID：{}", categoryId, e);
            throw new RuntimeException("保存档次配置失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取风险矩阵类别列表（包含关联评分项信息）
     *
     * @param matrixId 矩阵ID
     * @return 类别列表
     */
    public List<RiskMatrixCategoryVO> getCategoryListByMatrixId(Long matrixId) {
        if (matrixId == null) {
            return new ArrayList<>();
        }

        List<RiskMatrixCategory> categories = mapper.selectByMatrixId(matrixId);
        List<RiskMatrixCategoryVO> result = new ArrayList<>();

        for (RiskMatrixCategory category : categories) {
            RiskMatrixCategoryVO vo = new RiskMatrixCategoryVO();
            BeanUtils.copyProperties(category, vo);

            // 从 scoreIds 字段解析评分项ID列表
            List<Long> scoreItemIds = convertStringToScoreItemIds(category.getScoreIds());
            vo.setScoreItems(scoreItemIds);

            // 加载关联的评分项名称列表（用于前端显示）
            if (!CollectionUtils.isEmpty(scoreItemIds)) {
                List<String> scoreItemNames = getScoreItemNamesByIds(scoreItemIds);
                vo.setScoreItemNames(scoreItemNames);
            }

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取单个类别详情（包含档次配置和关联评分项）
     *
     * @param categoryId 类别ID
     * @return 类别详情
     */
    public RiskMatrixCategoryVO getCategoryDetailById(Long categoryId) {
        if (categoryId == null) {
            return null;
        }

        RiskMatrixCategory category = mapper.selectByPrimaryKey(categoryId);
        if (category == null) {
            return null;
        }

        RiskMatrixCategoryVO vo = new RiskMatrixCategoryVO();
        BeanUtils.copyProperties(category, vo);

        // 加载档次配置
        vo.setLevels(levelService.getLevelsByCategoryId(categoryId));

        // 从 scoreIds 字段解析评分项ID列表
        vo.setScoreItems(convertStringToScoreItemIds(category.getScoreIds()));

        return vo;
    }

    /**
     * 保存单个类别（新增或更新）
     *
     * @param matrixId 矩阵ID
     * @param categoryRequest 类别请求参数
     * @param currentUser 当前用户
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSingleCategory(Long matrixId, RiskMatrixRequest.CategoryRequest categoryRequest, String currentUser) {
        if (matrixId == null || categoryRequest == null) {
            log.warn("保存单个类别失败：矩阵ID或类别请求参数为空");
            return false;
        }

        try {
            LocalDateTime now = LocalDateTime.now();
            RiskMatrixCategory category;

            if (categoryRequest.getId() != null) {
                // 更新现有类别
                category = mapper.selectByPrimaryKey(categoryRequest.getId());
                if (category == null) {
                    log.error("更新类别失败：类别不存在，ID：{}", categoryRequest.getId());
                    return false;
                }

                // 更新基本信息
                category.setName(categoryRequest.getName());
                category.setDescription(categoryRequest.getDescription());
                category.setWeight(categoryRequest.getWeight() != null ?
                        BigDecimal.valueOf(categoryRequest.getWeight()) : BigDecimal.ONE);
                category.setCalculationMethod(StringUtils.hasText(categoryRequest.getCalculationMethod()) ?
                        categoryRequest.getCalculationMethod() : "sum");
                category.setUpdateTime(now);

                int result = mapper.updateByPrimaryKeySelective(category);
                if (result <= 0) {
                    log.error("更新类别失败，ID：{}", categoryRequest.getId());
                    return false;
                }
            } else {
                // 新增类别
                category = convertRequestToEntity(categoryRequest, matrixId, 1, now, currentUser);
                int result = mapper.insertSelective(category);
                if (result <= 0) {
                    log.error("新增类别失败，矩阵ID：{}", matrixId);
                    return false;
                }
            }

            // 保存档次配置
            if (!CollectionUtils.isEmpty(categoryRequest.getLevels())) {
                levelService.saveLevelsFromRequests(category.getId(), categoryRequest.getLevels());
            }

            // 保存关联评分项配置
            categoryScoreItemService.saveCategoryScoreItems(category.getId(), categoryRequest.getScoreItems());

            log.info("单个类别保存成功，类别ID：{}", category.getId());
            return true;

        } catch (Exception e) {
            log.error("保存单个类别失败，矩阵ID：{}", matrixId, e);
            throw new RuntimeException("保存单个类别失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据评分项ID列表获取评分项名称列表
     *
     * @param scoreItemIds 评分项ID列表
     * @return 评分项名称列表
     */
    private List<String> getScoreItemNamesByIds(List<Long> scoreItemIds) {
        if (CollectionUtils.isEmpty(scoreItemIds)) {
            return new ArrayList<>();
        }

        try {
            // 调用评分项服务获取名称
            return scoreItemService.getScoreItemNamesByIds(scoreItemIds);
        } catch (Exception e) {
            log.warn("获取评分项名称失败，scoreItemIds: {}", scoreItemIds, e);
            // 返回默认名称
            return scoreItemIds.stream()
                    .map(id -> "评分项" + id)
                    .collect(Collectors.toList());
        }
    }
}

package com.kbao.kbcelms.questionnaire.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireQueryBean;
import com.kbao.kbcelms.questionnaire.entity.Questionnaire;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷Mapper接口
 */
public interface QuestionnaireMapper extends BaseMapper<Questionnaire, Long> {

    /**
     * 分页查询问卷列表
     */
    List<QuestionnaireVO> selectQuestionnairePage(@Param("query") QuestionnaireQueryBean query);
    /**
     * 根据ID查询问卷详情（包含问题）
     */
    QuestionnaireVO selectQuestionnaire(@Param("id") Long id, @Param("enterpriseType") String enterpriseType);

    /**
     * 查询问卷基本信息
     */
    QuestionnaireVO selectQuestionnaireBase(@Param("id") Long id,
                                            @Param("code") String code,
                                            @Param("enterpriseType") String enterpriseType,
                                            @Param("status")Integer status);



    /**
     * 统计问卷数量
     */
    Long countQuestionnaires(@Param("query") QuestionnaireQueryBean query);

    /**
     * 根据企业类型查询已存在的问卷（排除指定ID的问卷）
     * @param enterpriseTypes 企业类型列表
     * @param excludeId 排除的问卷ID（编辑时使用）
     * @return 匹配的问卷列表
     */
    List<Questionnaire> selectByEnterpriseTypes(@Param("enterpriseTypes") List<String> enterpriseTypes, 
                                               @Param("excludeId") Long excludeId);
}

package com.kbao.kbcelms.questionnaire.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.questionnaire.entity.QuestionnaireAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 问卷答案Mapper接口
 */
public interface QuestionnaireAnswerMapper extends BaseMapper<QuestionnaireAnswer, Long> {

    /**
     * 根据问卷ID和企业ID查询答案列表
     */
    List<QuestionnaireAnswer> selectAnswersByQuestionnaireAndEnterprise(
            @Param("questionnaireId") Long questionnaireId, 
            @Param("enterpriseId") Long enterpriseId);

    /**
     * 根据问卷ID查询所有答案
     */
    List<QuestionnaireAnswer> selectAnswersByEnterprise(@Param("questionnaireId") Long questionnaireId,
                                                        @Param("enterpriseId") Long enterpriseId,
                                                        @Param("enterpriseType") String enterpriseType);


    /**
     * 批量插入或更新答案
     */
    int batchInsertOrUpdate(@Param("answers") List<QuestionnaireAnswer> answers);

    /**
     * 删除企业的问卷答案（逻辑删除）
     */
    int deleteByQuestionnaireAndEnterprise(
            @Param("questionnaireId") Long questionnaireId, 
            @Param("enterpriseId") Long enterpriseId);

    /**
     * 统计问卷答题企业数量
     */
    List<String> getEnterpriseTypesByAnswer(@Param("agentEnterpriseId") Integer agentEnterpriseId);

    /**
     * 根据评分项ID列表和企业ID查询答案
     */
    List<QuestionnaireAnswer> selectAnswersByScoreIdsAndEnterprise(
            @Param("scoreIds") List<Long> scoreIds,
            @Param("enterpriseId") Long enterpriseId,
            @Param("enterpriseType") String enterpriseType);

}

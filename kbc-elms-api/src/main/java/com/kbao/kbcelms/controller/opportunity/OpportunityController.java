package com.kbao.kbcelms.controller.opportunity;

import com.github.pagehelper.PageInfo;import com.kbao.commons.enums.ResultStatusEnum;import com.kbao.commons.web.PageRequest;import com.kbao.commons.web.Result;import com.kbao.kbcbsc.log.annotation.LogAnnotation;import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.formConfig.bean.InsureFieldReqVo;import com.kbao.kbcelms.opportunity.vo.OpportunityConfigResVo;import com.kbao.kbcelms.formConfig.model.FormConfigField;import com.kbao.kbcelms.formConfig.service.FormConfigService;import com.kbao.kbcelms.opportunity.service.OpportunityApiService;import com.kbao.kbcelms.opportunity.vo.OpportunityAddReqVo;import com.kbao.kbcelms.opportunity.vo.OpportunityListResVo;import com.kbao.kbcelms.opportunity.vo.OpportunitySearchReqVo;import com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService;import com.kbao.kbcelms.opportunityteamdivision.vo.ConfirmDivisionVo;import io.swagger.annotations.Api;import io.swagger.annotations.ApiOperation;import org.springframework.beans.factory.annotation.Autowired;import org.springframework.web.bind.annotation.PostMapping;import org.springframework.web.bind.annotation.RequestBody;import org.springframework.web.bind.annotation.RequestMapping;import org.springframework.web.bind.annotation.RestController;import java.util.List;
@RequestMapping("/api/opportunity")
@Api(tags = "机会API")
@RestController
public class OpportunityController {
    @Autowired
    private FormConfigService formConfigService;
    @Autowired
    private OpportunityApiService opportunityApiService;
    @Autowired
    private OpportunityTeamDivisionService opportunityTeamDivisionService;

    @ApiOperation(value = "查询机会列表", notes = "查询机会列表")
    @PostMapping("/list")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "查询", desc = "查询企业客户列表")
    public Result<PageInfo<OpportunityListResVo>> getList(@RequestBody PageRequest<OpportunitySearchReqVo> pageRequest) {
        PageInfo<OpportunityListResVo> list = opportunityApiService.getAgentOpportunityList(pageRequest);
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "查询详情页配置", notes = "查询详情页配置")
    @PostMapping("/config")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "查询", desc = "查询详情页配置")
    public Result<OpportunityConfigResVo> getOpportunityConfigs(@RequestBody OpportunitySearchReqVo reqVo) {
        OpportunityConfigResVo configs = opportunityApiService.getOpportunityConfigs(reqVo.getOpportunityId());
        return Result.succeed(configs, ResultStatusEnum.SUCCESS.getMsg());
    }


    @ApiOperation(value = "查询展示字段", notes = "查询展示字段")
    @PostMapping("/insureFields")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "查询", desc = "查询展示字段")
    public Result<List<FormConfigField>> getInsureFields(@RequestBody InsureFieldReqVo reqVo) {
        List<FormConfigField> fields = formConfigService.getInsureFields(reqVo.getConfigIds());
        return Result.succeed(fields, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "保存", notes = "保存")
    @PostMapping("/add")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "保存", desc = "保存")
    public Result<String> save(@RequestBody OpportunityAddReqVo reqVo) {
        opportunityApiService.add(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "查询明细", notes = "查询明细")
    @PostMapping("/detail")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "保存", desc = "保存")
    public Result<OpportunityAddReqVo> getOpportunityDetail(@RequestBody OpportunitySearchReqVo reqVo) {
        OpportunityAddReqVo opportunity = opportunityApiService.getOpportunityDetail(reqVo.getOpportunityId());
        return Result.succeed(opportunity, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "查询明细", notes = "查询明细")
    @PostMapping("/fullInfo")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "保存", desc = "保存")
    public Result<OpportunityAddReqVo> getOpportunityFullInfo(@RequestBody OpportunitySearchReqVo reqVo) {
        OpportunityAddReqVo opportunity = opportunityApiService.getOpportunityFullInfo(reqVo.getOpportunityId());
        return Result.succeed(opportunity, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "查询明细", notes = "查询明细")
    @PostMapping("/update")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "保存", desc = "保存")
    public Result<OpportunityAddReqVo> updateOpportunity(@RequestBody OpportunityAddReqVo reqVo) {
        opportunityApiService.updateOpportunity(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "提交机会", notes = "提交机会")
    @PostMapping("/submit")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "保存", desc = "保存")
    public Result<OpportunityAddReqVo> submit(@RequestBody OpportunitySearchReqVo reqVo) {
        opportunityApiService.submit(reqVo.getOpportunityId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "删除机会", notes = "删除机会")
    @PostMapping("/delete")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "保存", desc = "保存")
    public Result<OpportunityAddReqVo> delete(@RequestBody OpportunitySearchReqVo reqVo) {
        opportunityApiService.delete(reqVo.getOpportunityId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "确认比例", notes = "确认比例")
    @PostMapping("/confirmRatio")
    @LogAnnotation(module = "机会API", recordRequestParam = true, action = "保存", desc = "保存")
    public Result<OpportunityAddReqVo> confirmRatio(@RequestBody ConfirmDivisionVo reqVo) {
        opportunityTeamDivisionService.confirmRatio(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "获取待确认信息数", notes = "获取待确认信息数")
    @PostMapping("/getConfirmNum")
    public Result<Integer> getNeedConfirmNum() {
        String agentCode = ElmsContext.getUser().getAgentCode();
        Integer confirmNum = opportunityTeamDivisionService.getNeedConfirmNum(agentCode);
        return Result.succeed(confirmNum, "查询成功");
    }
}

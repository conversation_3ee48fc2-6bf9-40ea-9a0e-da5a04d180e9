package com.kbao.kbcelms.controller.enterprise;

import com.kbao.commons.enums.ResultStatusEnum;import com.kbao.commons.web.Result;import com.kbao.kbcbsc.log.annotation.LogAnnotation;import com.kbao.kbcelms.businessProcess.bean.AgentEnterpriseProcessVo;import com.kbao.kbcelms.dataTemplate.service.DataTemplateService;import com.kbao.kbcelms.enterprise.base.bean.EnterpriseSearchVo;import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;import com.kbao.kbcelms.enterprise.base.service.EnterpriseApiService;import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseEditVo;import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;import io.swagger.annotations.Api;import io.swagger.annotations.ApiOperation;import org.springframework.beans.factory.annotation.Autowired;import org.springframework.web.bind.annotation.PostMapping;import org.springframework.web.bind.annotation.RequestBody;import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;import java.util.List;import java.util.Map;
@RestController
@RequestMapping("/api/enterprise")
@Api(tags = "企业信息API")
public class EnterpriseController {
    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;
    @Autowired
    private EnterpriseApiService enterpriseApiService;

    @ApiOperation(value = "搜索企业信息", notes = "搜索企业信息")
    @PostMapping("/search")
    @LogAnnotation(module = "企业信息API", recordRequestParam = true, action = "查询", desc = "搜索企业信息")
    public Result<List<EnterpriseBasicInfo>> searchByName(@RequestBody EnterpriseSearchVo vo) {
        List<EnterpriseBasicInfo> list = enterpriseBasicInfoService.searchByName(vo.getName());
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "企业验真", notes = "企业验真")
    @PostMapping("/verify")
    @LogAnnotation(module = "企业信息API", recordRequestParam = true, action = "查询", desc = "企业验真")
    public Result<Map<String,Object>> verify(@RequestBody EnterpriseSearchVo vo) {
        Map<String,Object> result = enterpriseApiService.verify(vo);
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "查询验真结果", notes = "查询验真结果")
    @PostMapping("/getVerifyData")
    @LogAnnotation(module = "企业信息API", recordRequestParam = true, action = "查询", desc = "查询验真结果")
    public Result<Map<String,Object>> verify(@RequestBody AgentEnterpriseEditVo vo) {
        Map<String,Object> result = enterpriseApiService.getVerifyData(vo.getQueryRecordId());
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }
}

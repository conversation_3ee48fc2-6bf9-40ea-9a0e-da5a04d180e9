package com.kbao.kbcelms.controller.industryrisk;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.riskconfig.service.IndustryRiskConfigService;
import com.kbao.kbcelms.riskconfig.vo.IndustryRiskConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * 行业风险配置控制器
 * <AUTHOR>
 * @date 2025-08-11
 */
@Api(tags = "行业风险配置管理")
@RestController
@RequestMapping("/api/h5/industryRisk")
@Validated
public class IndustryRiskApiController extends BaseController {

    @Autowired
    private IndustryRiskConfigService industryRiskConfigService;

    /**
     * 根据一级行业编码获取配置详情
     */
    @ApiOperation("根据一级行业编码获取配置详情")
    @GetMapping("/industryLevel1Code")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "查询", desc = "获取行业风险配置详情")
    public Result<IndustryRiskConfigVO> getByIndustryLevel1Code(
            @ApiParam("行业编码") @RequestParam @NotBlank String categoryCode) {
        try {
            IndustryRiskConfigVO result = industryRiskConfigService.getByIndustryByCode(categoryCode);
            if (result == null) {
                return Result.failed("配置不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("获取行业风险配置详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
}

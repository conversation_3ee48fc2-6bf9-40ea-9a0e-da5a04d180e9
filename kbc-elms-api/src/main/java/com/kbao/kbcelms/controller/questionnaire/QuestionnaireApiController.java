package com.kbao.kbcelms.controller.questionnaire;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireAnswerBean;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireCodeBean;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireRequest;
import com.kbao.kbcelms.questionnaire.service.QuestionnaireAnswerService;
import com.kbao.kbcelms.questionnaire.service.QuestionnaireService;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireVO;
import com.kbao.tool.util.SysLoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * H5端问卷管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@RestController
@RequestMapping("/api/h5/questionnaire")
@Api(tags = "H5端问卷管理")
public class QuestionnaireApiController extends BaseController {

    @Autowired
    private QuestionnaireService questionnaireService;

    @Autowired
    private QuestionnaireAnswerService questionnaireAnswerService;

    /**
     * 根据企业类型查询对应的问卷详情
     */
    @ApiOperation(value = "根据企业类型查询问卷", notes = "根据当前登录用户的企业类型查询对应的问卷详情")
    @PostMapping("/getByEnterpriseType")
    @LogAnnotation(module = "H5端问卷管理", recordRequestParam = true, action = "查询", desc = "根据企业类型查询问卷")
    public Result<QuestionnaireVO> getByEnterpriseType(@RequestBody @Validated QuestionnaireRequest request) {
        // 查询问卷
        QuestionnaireVO questionnaire = questionnaireService.getQuestionnaireDetail(request.getId(), null,
                request.getEnterpriseType(), 1);

        if (questionnaire == null) {
            return Result.failed("未找到对应企业类型的问卷");
        }

        log.info("根据企业类型查询问卷成功，questionnaireId: {}", questionnaire.getId());
        return Result.succeed(questionnaire, "查询成功");
    }

    /**
     * 根据企业编码查询对应的问卷详情
     */
    @ApiOperation(value = "根据企业类型查询问卷", notes = "根据问卷ID和编码查询")
    @PostMapping("/getByCode")
    @LogAnnotation(module = "H5端问卷管理", recordRequestParam = true, action = "查询", desc = "根据问卷ID和编码查询")
    public Result<QuestionnaireVO> getByCode(@RequestBody @Validated QuestionnaireCodeBean request) {
        // 查询问卷
        QuestionnaireVO questionnaire = questionnaireService.getQuestionnaireDetail(request.getId(), request.getCode(),
                null, 1);

        if (questionnaire == null) {
            return Result.failed("未找到对应企业类型的问卷");
        }

        return Result.succeed(questionnaire, "查询成功");
    }

    @PostMapping("/answer/submit")
    @ApiOperation("提交问卷答案")
    @LogAnnotation(module = "问卷管理", recordRequestParam = true, action = "提交", desc = "提交问卷答案")
    public Result<Boolean> submitQuestionnaireAnswer(
            @ApiParam("问卷答案") @RequestBody @Validated QuestionnaireAnswerBean answerBean
    ) {
        Boolean success = questionnaireAnswerService.submitQuestionnaireAnswer(answerBean);
        return Result.succeed(success, "提交成功");
    }
}

package com.kbao.kbcelms.controller.common;

import com.kbao.commons.enums.ResultStatusEnum;import com.kbao.commons.web.BaseController;import com.kbao.commons.web.Result;import com.kbao.kbcbsc.log.annotation.LogAnnotation;import com.kbao.kbcelms.common.service.CommonDictService;
import com.kbao.kbcelms.common.vo.CascadeDictItem;
import com.kbao.kbcelms.common.vo.DictItem;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseType;
import com.kbao.kbcelms.enterprise.type.service.EnterpriseTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通用字典控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/common/dict")
@Api(tags = "通用字典API")
public class CommonDictController extends BaseController {
    
    @Autowired
    private CommonDictService commonDictService;
    @Autowired
    private EnterpriseTypeService enterpriseTypeService;
    
    /**
     * 获取普通字典
     * @param enumCode 字典编码
     * @return 字典列表
     */
    @ApiOperation(value = "获取普通字典", notes = "根据字典编码获取普通字典数据")
    @PostMapping("/simple")
    @LogAnnotation(module = "通用字典", recordRequestParam = true, action = "查询", desc = "获取普通字典")
    public Result<List<DictItem>> getSimpleDict(
            @ApiParam(value = "字典编码", required = true, example = "employeeScales") 
            @RequestParam String enumCode) {
        try {
            List<DictItem> dictItems = commonDictService.getSimpleDict(enumCode);
            return Result.succeed(dictItems, ResultStatusEnum.SUCCESS.getMsg());
        } catch (IllegalArgumentException e) {
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            return Result.failed("获取字典数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取级联字典
     * @param enumCode 字典编码
     * @return 级联字典列表
     */
    @ApiOperation(value = "获取级联字典", notes = "根据字典编码获取级联字典数据")
    @PostMapping("/cascade")
    @LogAnnotation(module = "通用字典", recordRequestParam = true, action = "查询", desc = "获取级联字典")
    public Result<List<CascadeDictItem>> getCascadeDict(
            @ApiParam(value = "字典编码", required = true, example = "basCode") 
            @RequestParam String enumCode) {
        try {
            List<CascadeDictItem> cascadeDictItems = commonDictService.getCascadeDict(enumCode);
            return Result.succeed(cascadeDictItems, ResultStatusEnum.SUCCESS.getMsg());
        } catch (IllegalArgumentException e) {
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            return Result.failed("获取级联字典数据失败: " + e.getMessage());
        }
    }

    @PostMapping("/enterpriseTypes")
    @LogAnnotation(module = "企业类型", recordRequestParam = true, action = "查询", desc = "获取级联字典")
    public Result<List<EnterpriseType>> getEnterpriseTypes() {
        try {
            List<EnterpriseType> enterpriseTypes = enterpriseTypeService.getEnterpriseTypeDescList();
            return Result.succeed(enterpriseTypes, ResultStatusEnum.SUCCESS.getMsg());
        } catch (IllegalArgumentException e) {
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            return Result.failed("获取级联字典数据失败: " + e.getMessage());
        }
    }
}

package com.kbao.kbcelms.industrylimit.entity;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 行业限制条件实体
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ApiModel(value = "IndustryLimitCondition", description = "行业限制条件")
public class IndustryLimitCondition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private Long ruleId;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String field;

    /**
     * 操作符(eq-等于,ne-不等于,gt-大于,lt-小于,gte-大于等于,lte-小于等于,contains-包含,range-区间)
     */
    @ApiModelProperty(value = "操作符")
    private String operator;

    /**
     * 匹配值
     */
    @ApiModelProperty(value = "匹配值")
    private String value;

    /**
     * 条件描述
     */
    @ApiModelProperty(value = "条件描述")
    private String description;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
}

package com.kbao.kbcelms.industrylimit.vo;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 行业限制条件视图对象
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ApiModel(value = "IndustryLimitConditionVO", description = "行业限制条件视图对象")
public class IndustryLimitConditionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String field;

    /**
     * 操作符
     */
    @ApiModelProperty(value = "操作符")
    private String operator;

    /**
     * 匹配值
     */
    @ApiModelProperty(value = "匹配值")
    private String value;

    /**
     * 条件描述
     */
    @ApiModelProperty(value = "条件描述")
    private String description;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;
}

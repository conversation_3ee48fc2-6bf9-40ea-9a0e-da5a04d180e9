package com.kbao.kbcelms.opportunityprocess.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 启动流程请求VO
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@ApiModel(description = "启动流程请求VO")
public class OpportunityProcessStartRequestVO {
    
    /** 机会ID */
    @ApiModelProperty(value = "机会ID", example = "1", required = true)
    private Integer opportunityId;
    
    /** 公司类型 */
    @ApiModelProperty(value = "公司类型", example = "A", notes = "A、B、C、D", required = true)
    private String companyType;
} 
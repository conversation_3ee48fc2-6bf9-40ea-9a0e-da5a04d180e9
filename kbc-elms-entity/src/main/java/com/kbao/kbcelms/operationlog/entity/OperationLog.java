package com.kbao.kbcelms.operationlog.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import java.util.Date;
import java.util.Map;

/**
 * @ClassName: OperationLog
 * @Description: 操作日志
 * @Author: luobb
 */
@Data
@Document
@ApiModel(description = "操作日志实体类")
public class OperationLog implements Cloneable {
    @Id
    private String id;

    // 操作人ID
    @Indexed
    private String operatorId;

    // 操作人名称
    private String operatorName;

    // 操作时间
    @Indexed
    private Date operationTime;

    // 业务类型(驼峰形式，如:OpportunityTeam, OpportunityLog)
    @Indexed
    private String businessType;

    // 操作类型(如:CREATE, UPDATE, DELETE, CHANGE_MANAGER等)
    @Indexed
    private String operationType;

    // 机会ID
    @Indexed
    private Integer opportunityId;

    // 机会名称
    private String opportunityName;

    // 业务ID(如:团队成员ID, 日志ID等)
    @Indexed
    private String businessId;

    // 变更前内容
    private Map<String, Object> beforeChange;

    // 变更后内容
    private Map<String, Object> afterChange;

    // 变更描述
    private String description;

    // 操作结果：SUCCESS/FAIL
    @Indexed
    private String operationResult;

    // 结果消息，失败时存储异常信息
    private String resultMessage;

    // 变更说明
    private String changeRemark;

    // 租户ID
    @Indexed
    private String tenantId;

    @Override
    public OperationLog clone() {
        try {
            return (OperationLog) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
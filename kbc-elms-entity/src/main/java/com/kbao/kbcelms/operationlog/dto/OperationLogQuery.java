package com.kbao.kbcelms.operationlog.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName: OperationLogQuery
 * @Description: OperationLog查询对象
 * @Author: luobb
 * @Date: 2025/8/4 13:53
 * @Version: 1.0
 */
@Data
@ApiModel(description = "操作日志查询对象")
public class OperationLogQuery {

    private String id;

    // 操作人ID
    private String operatorId;

    // 操作时间 - 起始
    private Date operationStartTime;

    // 操作时间 - 结束
    private Date operationEndTime;

    // 业务类型(驼峰形式，如:OpportunityTeam, OpportunityLog)
    private String businessType;

    // 操作类型(如:CREATE, UPDATE, DELETE, CHANGE_MANAGER等)
    private String operationType;

    // 操作结果：SUCCESS/FAIL
    private String operationResult;

    // 机会ID
    private Integer opportunityId;

    // 变更说明
    private String changeRemark;
}
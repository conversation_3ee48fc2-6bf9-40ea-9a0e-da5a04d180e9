package com.kbao.kbcelms.opportunityteamdivision.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目分工比例
 */
@Data
public class OpportunityTeamDivision {
    /** 编号 */
    private Integer id;
    /** 机会id */
    private Integer opportunityId;
    /** 租户id */
    private String tenantId;
    /** 用户id */
    private String userId;
    /** 项目分工 */
    private String divisionId;
    /** 分工比例 */
    private BigDecimal divisionRatio;
    /** 参与状态 0 待确认，1已确认  5 已拒绝 */
    private Integer status;
    /** 说明 */
    private String remark;
    /** 创建人编号 当前用户ID */
    private String createId;
    /** 创建人姓名 */
    private Date createTime;
    /** 更新人id */
    private String updateId;
    /** 更新时间 0正常 1审核中 2被否决 -1已删除 -2草稿 */
    private Date updateTime;

    /** 确认人 */
    private String confirmId;
    /** 确认时间 */
    private Date confirmTime;
    /** 删除 */
    private Integer isDeleted;

    /**
     * 是否默认分工
     */
    private Integer isDefault;
    /**
     * 分工次数
     */
    private Integer num;

    /**
     * 邮件发送次数
     */
    private Integer times;

} 
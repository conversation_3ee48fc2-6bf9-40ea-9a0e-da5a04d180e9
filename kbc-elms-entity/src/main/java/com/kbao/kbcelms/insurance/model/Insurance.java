package com.kbao.kbcelms.insurance.model;

import java.io.Serializable;
import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/28 15:44
 */
@Data
@Document(collection = "Insurance")
public class Insurance implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private String insuranceId;

    @Indexed
    private String insuranceName;

    private int sort;

    private Date createTime;

    private String createId;

    private Date updateTime;

    private String updateId;

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.riskmatrix.dao.RiskMatrixMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.riskmatrix.entity.RiskMatrix">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="enterprise_types" property="enterpriseTypes" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="category_count" property="categoryCount" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, code, description, category, status, enterprise_types,
        create_time, update_time, create_user, update_user
    </sql>

    <!-- 带类别数量统计的字段列表 -->
    <sql id="Base_Column_List_With_Category_Count">
        rm.id, rm.name, rm.code, rm.description, rm.category, rm.status, rm.enterprise_types,
        rm.create_time, rm.update_time, rm.create_user, rm.update_user,
        COALESCE(cc.category_count, 0) as category_count
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where_Clause">
        <where>
            <if test="name != null and name != ''">
                AND rm.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="category != null and category != ''">
                AND rm.category = #{category}
            </if>
            <if test="status != null">
                AND rm.status = #{status}
            </if>
            <if test="enterpriseType != null and enterpriseType.size() > 0">
                AND (
                <foreach collection="enterpriseType" item="type" separator=" AND ">
                    (rm.enterprise_types IS NOT NULL AND FIND_IN_SET(#{type}, rm.enterprise_types) > 0)
                </foreach>
                OR rm.enterprise_types IS NULL
                )
            </if>
            <if test="createUser != null and createUser != ''">
                AND rm.create_user = #{createUser}
            </if>
        </where>
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List_With_Category_Count"/>
        FROM t_risk_matrix rm
        LEFT JOIN (
            SELECT matrix_id, COUNT(*) as category_count
            FROM t_risk_matrix_category
            GROUP BY matrix_id
        ) cc ON rm.id = cc.matrix_id
        WHERE rm.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据编码查询 -->
    <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List_With_Category_Count"/>
        FROM t_risk_matrix rm
        LEFT JOIN (
            SELECT matrix_id, COUNT(*) as category_count
            FROM t_risk_matrix_category
            GROUP BY matrix_id
        ) cc ON rm.id = cc.matrix_id
        WHERE rm.code = #{code,jdbcType=VARCHAR}
    </select>

    <!-- 分页查询 -->
    <select id="selectByQuery" parameterType="com.kbao.kbcelms.riskmatrix.bean.RiskMatrixQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List_With_Category_Count"/>
        FROM t_risk_matrix rm
        LEFT JOIN (
            SELECT matrix_id, COUNT(*) as category_count
            FROM t_risk_matrix_category
            GROUP BY matrix_id
        ) cc ON rm.id = cc.matrix_id
        <include refid="Query_Where_Clause"/>
        ORDER BY rm.update_time DESC
    </select>

    <!-- 查询总数 -->
    <select id="countByQuery" parameterType="com.kbao.kbcelms.riskmatrix.bean.RiskMatrixQuery" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM t_risk_matrix rm
        <include refid="Query_Where_Clause"/>
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrix" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_risk_matrix (
            name, code, description, category, status, enterprise_types,
            create_time, update_time, create_user, update_user
        ) VALUES (
            #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
            #{category,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{enterpriseTypes,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
            #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrix" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_risk_matrix
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
            <if test="description != null">description,</if>
            <if test="category != null">category,</if>
            <if test="status != null">status,</if>
            <if test="enterpriseTypes != null">enterprise_types,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="code != null">#{code,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="category != null">#{category,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="enterpriseTypes != null">#{enterpriseTypes,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createUser != null">#{createUser,jdbcType=VARCHAR},</if>
            <if test="updateUser != null">#{updateUser,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrix">
        UPDATE t_risk_matrix
        SET name = #{name,jdbcType=VARCHAR},
            code = #{code,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            category = #{category,jdbcType=VARCHAR},
            status = #{status,jdbcType=TINYINT},
            enterprise_types = #{enterpriseTypes,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 风险矩阵详情结果映射（包含嵌套的类别和评分项） -->
    <resultMap id="RiskMatrixDetailResultMap" type="com.kbao.kbcelms.riskmatrix.vo.RiskMatrixDetailVO">
        <id column="matrix_id" property="id" jdbcType="BIGINT"/>
        <result column="matrix_name" property="name" jdbcType="VARCHAR"/>
        <result column="matrix_code" property="code" jdbcType="VARCHAR"/>
        <result column="matrix_description" property="description" jdbcType="VARCHAR"/>
        <result column="matrix_category" property="category" jdbcType="VARCHAR"/>
        <result column="matrix_status" property="status" jdbcType="TINYINT"/>
        <result column="matrix_enterprise_types" property="enterpriseTypes" jdbcType="VARCHAR"/>
        <result column="category_count" property="categoryCount" jdbcType="INTEGER"/>

        <!-- 嵌套的类别集合 -->
        <collection property="categories" ofType="com.kbao.kbcelms.riskmatrix.vo.RiskMatrixDetailVO$CategoryDetailVO">
            <id column="category_id" property="id" jdbcType="BIGINT"/>
            <result column="category_matrix_id" property="matrixId" jdbcType="BIGINT"/>
            <result column="category_name" property="name" jdbcType="VARCHAR"/>
            <result column="category_description" property="description" jdbcType="VARCHAR"/>
            <result column="category_weight" property="weight" jdbcType="DECIMAL"/>
            <result column="category_calculation_method" property="calculationMethod" jdbcType="VARCHAR"/>
            <result column="category_sort_order" property="sortOrder" jdbcType="INTEGER"/>
            <result column="category_level_count" property="levelCount" jdbcType="INTEGER"/>

            <!-- 嵌套的评分项集合 -->
            <collection property="scoreItems" ofType="com.kbao.kbcelms.riskmatrix.vo.RiskMatrixDetailVO$ScoreItemDetailVO">
                <id column="score_item_id" property="id" jdbcType="BIGINT"/>
                <result column="score_item_name" property="name" jdbcType="VARCHAR"/>
                <result column="score_item_code" property="code" jdbcType="VARCHAR"/>
                <result column="score_item_description" property="description" jdbcType="VARCHAR"/>
                <result column="score_item_category" property="category" jdbcType="VARCHAR"/>
                <result column="score_item_weight" property="weight" jdbcType="DECIMAL"/>
                <result column="score_item_max_score" property="maxScore" jdbcType="INTEGER"/>
                <result column="score_item_is_formula" property="isFormula" jdbcType="TINYINT"/>
                <result column="score_item_formula_id" property="formulaId" jdbcType="BIGINT"/>
                <result column="score_item_formula_name" property="formulaName" jdbcType="VARCHAR"/>
                <result column="score_item_coefficient" property="coefficient" jdbcType="DECIMAL"/>
                <result column="score_item_enterprise_types" property="enterpriseTypes" jdbcType="VARCHAR"/>
                <result column="score_item_status" property="status" jdbcType="TINYINT"/>
            </collection>
        </collection>
    </resultMap>

    <!-- 根据风险矩阵企业类型查询完整信息（包含类别和评分项） -->
    <select id="selectByEnterpriseTypes" parameterType="java.lang.String" resultMap="RiskMatrixDetailResultMap">
        SELECT
            rm.id as matrix_id,
            rm.name as matrix_name,
            rm.code as matrix_code,
            rm.description as matrix_description,
            rm.category as matrix_category,
            rm.status as matrix_status,
            rm.enterprise_types as matrix_enterprise_types,

            -- 类别字段
            rmc.id as category_id,
            rmc.matrix_id as category_matrix_id,
            rmc.name as category_name,
            rmc.description as category_description,
            rmc.weight as category_weight,
            rmc.calculation_method as category_calculation_method,
            rmc.sort_order as category_sort_order,

            si.id as score_item_id,
            si.name as score_item_name,
            si.coefficient as score_item_coefficient,
            si.is_formula as score_item_is_formula,
            si.formula_id as score_item_formula_id,
            si.formula_name as score_item_formula_name
        FROM t_risk_matrix rm
         LEFT JOIN t_risk_matrix_category rmc ON rm.id = rmc.matrix_id
         LEFT JOIN t_risk_matrix_level lc ON rmc.id = lc.category_id
         LEFT JOIN t_category_score_item csi ON csi.category_id = rmc.id
         LEFT JOIN t_score_item si ON csi.score_item_id = si.id
        WHERE FIND_IN_SET(#{enterpriseType,jdbcType=VARCHAR}, rm.enterprise_types)
          AND rm.status = 1
        ORDER BY rm.id, rmc.sort_order ASC, si.create_time ASC
    </select>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrix">
        UPDATE t_risk_matrix
        <set>
            <if test="name != null">name = #{name,jdbcType=VARCHAR},</if>
            <if test="code != null">code = #{code,jdbcType=VARCHAR},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            <if test="category != null">category = #{category,jdbcType=VARCHAR},</if>
            <if test="status != null">status = #{status,jdbcType=TINYINT},</if>
            <if test="enterpriseTypes != null">enterprise_types = #{enterpriseTypes,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateUser != null">update_user = #{updateUser,jdbcType=VARCHAR},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM t_risk_matrix
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM t_risk_matrix
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>

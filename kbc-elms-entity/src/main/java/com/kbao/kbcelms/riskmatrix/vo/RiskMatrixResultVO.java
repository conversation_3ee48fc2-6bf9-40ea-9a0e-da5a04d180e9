package com.kbao.kbcelms.riskmatrix.vo;

import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixResultDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 风险矩阵计算结果视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixResultVO {
    
    /**
     * 报告id
     */
    private String id;

    /**
     * 报告url
     */
    private String reportUrl;

    /**
     * 报告生成时间
     */
    private Date reportTime;

    /**
     * 矩阵列表
     */
    private List<RiskMatrixResultDTO> resultDTOS;
    

}

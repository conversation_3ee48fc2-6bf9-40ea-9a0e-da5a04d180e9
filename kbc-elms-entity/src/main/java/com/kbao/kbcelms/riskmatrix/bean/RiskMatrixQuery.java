package com.kbao.kbcelms.riskmatrix.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 风险矩阵查询参数
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixQuery {
    
    /**
     * 矩阵名称（模糊查询）
     */
    private String name;
    
    /**
     * 矩阵分类
     */
    private String category;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 企业类型
     */
    private List<String> enterpriseType;
    
    /**
     * 创建人
     */
    private String createUser;
}

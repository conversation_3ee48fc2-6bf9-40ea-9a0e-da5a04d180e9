package com.kbao.kbcelms.bascode.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "BaseCodeTreeVO", description = "基础代码树形结构VO")
@Data
public class BaseCodeTreeVO  {
    @ApiModelProperty(value = "节点值（code）")
    private String value;
    @ApiModelProperty(value = "节点名称（name）")
    private String label;
    @ApiModelProperty(value = "子节点")
    private List<BaseCodeTreeVO> children = new ArrayList<>();

    public BaseCodeTreeVO(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<BaseCodeTreeVO> getChildren() {
        return children;
    }

    public void setChildren(List<BaseCodeTreeVO> children) {
        this.children = children;
    }
}
package com.kbao.kbcelms.outinf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 租户ID请求VO
 */
@Data
@ApiModel(value = "TenantIdRequestVO", description = "租户ID请求VO")
public class TenantIdRequestVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "租户ID", required = false, example = "tenant001")
    private String tenantId;
} 
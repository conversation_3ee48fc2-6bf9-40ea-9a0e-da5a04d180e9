<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.questionnaire.dao.QuestionnaireQuestionOptionMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestionOption">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="question_id" property="questionId" jdbcType="BIGINT"/>
        <result column="option_text" property="optionText" jdbcType="VARCHAR"/>
        <result column="option_value" property="optionValue" jdbcType="VARCHAR"/>
        <result column="score" property="score" jdbcType="INTEGER"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- VO结果映射 -->
    <resultMap id="VOResultMap" type="com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionOptionVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="question_id" property="questionId" jdbcType="BIGINT"/>
        <result column="option_text" property="optionText" jdbcType="VARCHAR"/>
        <result column="option_value" property="optionValue" jdbcType="VARCHAR"/>
        <result column="score" property="score" jdbcType="INTEGER"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, question_id, option_text, option_value, score, sort_order, 
        create_time, update_time, deleted
    </sql>

    <!-- VO字段列表 -->
    <sql id="VO_Column_List">
        id, question_id, option_text, option_value, score, sort_order
    </sql>

    <!-- 根据问题ID查询选项列表 -->
    <select id="selectOptionsByQuestionId" resultMap="VOResultMap">
        SELECT <include refid="VO_Column_List"/>
        FROM t_questionnaire_question_option
        WHERE question_id = #{questionId} AND deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 批量删除选项（逻辑删除） -->
    <update id="batchDeleteByIds">
        UPDATE t_questionnaire_question_option 
        SET deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 根据问题ID删除所有选项（逻辑删除） -->
    <update id="deleteByQuestionId">
        UPDATE t_questionnaire_question_option 
        SET deleted = 1, update_time = NOW()
        WHERE question_id = #{questionId} AND deleted = 0
    </update>

    <!-- 根据问题ID列表删除所有选项（逻辑删除） -->
    <update id="deleteByQuestionIds">
        UPDATE t_questionnaire_question_option 
        SET deleted = 1, update_time = NOW()
        WHERE question_id IN
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 获取问题的最大排序号 -->
    <select id="getMaxSortOrder" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(sort_order), 0)
        FROM t_questionnaire_question_option
        WHERE question_id = #{questionId} AND deleted = 0
    </select>

    <!-- 基础CRUD操作 -->

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_questionnaire_question_option
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestionOption" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_questionnaire_question_option (
            question_id, option_text, option_value, score, sort_order,
            create_time, update_time, deleted
        ) VALUES (
            #{questionId}, #{optionText}, #{optionValue}, #{score}, #{sortOrder},
            #{createTime}, #{updateTime}, #{deleted}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestionOption" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_questionnaire_question_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionId != null">question_id,</if>
            <if test="optionText != null">option_text,</if>
            <if test="optionValue != null">option_value,</if>
            <if test="score != null">score,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="questionId != null">#{questionId},</if>
            <if test="optionText != null">#{optionText},</if>
            <if test="optionValue != null">#{optionValue},</if>
            <if test="score != null">#{score},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestionOption">
        UPDATE t_questionnaire_question_option SET
            question_id = #{questionId},
            option_text = #{optionText},
            option_value = #{optionValue},
            score = #{score},
            sort_order = #{sortOrder},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestionOption">
        UPDATE t_questionnaire_question_option
        <set>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="optionText != null">option_text = #{optionText},</if>
            <if test="optionValue != null">option_value = #{optionValue},</if>
            <if test="score != null">score = #{score},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据主键删除（逻辑删除） -->
    <update id="deleteByPrimaryKey">
        UPDATE t_questionnaire_question_option SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>

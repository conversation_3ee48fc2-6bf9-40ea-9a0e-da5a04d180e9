package com.kbao.kbcelms.questionnaire.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 问卷题型枚举
 */
@Getter
@AllArgsConstructor
public enum QuestionTypeEnum {
    
    /**
     * 单选题
     */
    SINGLE("single", "单选题"),
    
    /**
     * 多选题
     */
    MULTI("multi", "多选题"),
    
    /**
     * 简答题
     */
    TEXT("text", "简答题"),
    
    /**
     * 评分题
     */
    RATING("rating", "评分题"),
    
    /**
     * 矩阵题
     */
    MATRIX("matrix", "矩阵题");
    
    /**
     * 题型代码
     */
    private final String code;
    
    /**
     * 题型名称
     */
    private final String name;
    
    /**
     * 根据代码获取枚举
     */
    public static QuestionTypeEnum getByCode(String code) {
        for (QuestionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 验证题型代码是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}

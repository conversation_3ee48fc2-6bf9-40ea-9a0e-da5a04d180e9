package com.kbao.kbcelms.questionnaire.bean;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 题目排序参数
 */
@Data
public class QuestionSortBean {

    /**
     * 题目排序列表
     */
    @Valid
    @NotEmpty(message = "题目排序列表不能为空")
    private List<QuestionSortItem> questions;

    /**
     * 题目排序项
     */
    @Data
    public static class QuestionSortItem {

        /**
         * 题目ID
         */
        private Long id;

        /**
         * 排序号
         */
        private Integer sortOrder;
    }
}
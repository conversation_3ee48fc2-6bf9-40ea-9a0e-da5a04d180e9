package com.kbao.kbcelms.questionnaire.bean;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 问卷答题参数
 */
@Data
public class QuestionnaireAnswerBean {

    /**
     * 问卷ID
     */
    @NotNull(message = "问卷ID不能为空")
    private Long questionnaireId;

    /**
     * 问卷编码
     */
    @NotBlank(message = "问卷编码不能为空")
    private String code;

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    private Long enterpriseId;
    /**
     * 企业类型
     */
    @NotNull(message = "企业类型不能为空")
    private String enterpriseType;

    /**
     * 分享人ID
     */
    @NotBlank(message = "分享人ID不能为空")
    private String shareUserId;

    /**
     * 提交人ID
     */
    @NotBlank(message = "提交人ID不能为空")
    private String submitUserId;

    /**
     * 答案列表
     */
    @Valid
    private List<QuestionAnswerBean> answers;

    /**
     * 单个问题答案
     */
    @Data
    public static class QuestionAnswerBean {

        /**
         * 问题ID
         */
        @NotNull(message = "问题ID不能为空")
        private Long questionId;
        /**
         * 问卷题目
         */
        private String questionTitle;

        /**
         * 问题ID
         */
        @NotNull(message = "关联评分项id不能为空")
        private Long scoreId;

        /**
         * 选项id
         */
        private Long optionId;

        /**
         * 答案内容（选择题存选项值，简答题存文本）
         */
        private String answerContent;
        /**
         * 选项值
         */
        private String optionValue;

        /**
         * 得分
         */
        private BigDecimal score;

        /**
         * 序号
         */
        private Integer sortOrder;
    }
}

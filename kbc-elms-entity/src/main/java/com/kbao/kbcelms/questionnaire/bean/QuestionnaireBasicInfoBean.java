package com.kbao.kbcelms.questionnaire.bean;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 问卷基本信息保存参数
 * 专门用于前端基本信息编辑页面，不包含题目数据
 */
@Data
public class QuestionnaireBasicInfoBean {

    /**
     * 问卷ID（编辑时传入）
     */
    private Long id;

    /**
     * 问卷标题
     */
    @NotBlank(message = "问卷标题不能为空")
    private String title;

    /**
     * 问卷描述
     */
    private String description;

    /**
     * 适用企业类型列表
     */
    private List<String> enterpriseTypes;

    /**
     * 状态：0-禁用，1-启用，2-草稿
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}

package com.kbao.kbcelms.questionnaire.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 问卷视图对象
 */
@Data
public class QuestionnaireVO {

    /**
     * 问卷ID
     */
    private Long id;

    /**
     * 问卷编码
     */
    private String code;

    /**
     * 问卷标题
     */
    private String title;

    /**
     * 问卷描述
     */
    private String description;

    /**
     * 适用企业类型（逗号分隔）
     */
    private String enterpriseTypes;

    /**
     * 适用企业类型列表
     */
    private List<String> enterpriseTypeList;

    /**
     * 状态：0-禁用，1-启用，2-草稿
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 问题数量
     */
    private Integer questionCount;

    /**
     * 问题列表
     */
    private List<QuestionnaireQuestionVO> questions;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 获取企业类型列表
     */
    public List<String> getEnterpriseTypeList() {
        if (this.enterpriseTypes == null || this.enterpriseTypes.trim().isEmpty()) {
            return Arrays.asList();
        }
        return Arrays.asList(this.enterpriseTypes.split(","));
    }

    /**
     * 设置企业类型列表
     */
    public void setEnterpriseTypeList(List<String> enterpriseTypeList) {
        this.enterpriseTypeList = enterpriseTypeList;
        if (enterpriseTypeList != null && !enterpriseTypeList.isEmpty()) {
            this.enterpriseTypes = String.join(",", enterpriseTypeList);
        }
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.questionnaire.dao.QuestionnaireQuestionMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestion">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="questionnaire_id" property="questionnaireId" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="score_id" property="scoreId" jdbcType="VARCHAR"/>
        <result column="max_score" property="maxScore" jdbcType="INTEGER"/>
        <result column="required" property="required" jdbcType="TINYINT"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- VO结果映射 -->
    <resultMap id="VOResultMap" type="com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="questionnaire_id" property="questionnaireId" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="type_desc" property="typeDesc" jdbcType="VARCHAR"/>
        <result column="score_id" property="scoreId" jdbcType="VARCHAR"/>
        <result column="max_score" property="maxScore" jdbcType="INTEGER"/>
        <result column="required" property="required" jdbcType="TINYINT"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 详情结果映射（包含选项列表） -->
    <resultMap id="DetailResultMap" type="com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="questionnaire_id" property="questionnaireId" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="type_desc" property="typeDesc" jdbcType="VARCHAR"/>
        <result column="score_id" property="scoreId" jdbcType="TINYINT"/>
        <result column="max_score" property="maxScore" jdbcType="INTEGER"/>
        <result column="required" property="required" jdbcType="TINYINT"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <collection property="options" ofType="com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionOptionVO">
            <id column="o_id" property="id" jdbcType="BIGINT"/>
            <result column="o_question_id" property="questionId" jdbcType="BIGINT"/>
            <result column="o_option_text" property="optionText" jdbcType="VARCHAR"/>
            <result column="o_option_value" property="optionValue" jdbcType="VARCHAR"/>
            <result column="o_score" property="score" jdbcType="INTEGER"/>
            <result column="o_sort_order" property="sortOrder" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, questionnaire_id, title, type, score_id, max_score, required, sort_order,
        create_time, update_time, deleted
    </sql>

    <!-- VO字段列表 -->
    <sql id="VO_Column_List">
        q.id, q.questionnaire_id, q.title, q.type,
        CASE q.type 
            WHEN 'single' THEN '单选题' 
            WHEN 'multi' THEN '多选题' 
            WHEN 'text' THEN '简答题' 
            WHEN 'rating' THEN '评分题' 
            WHEN 'matrix' THEN '矩阵题' 
            ELSE '未知' 
        END as type_desc,
        q.score_id, q.max_score, q.required, q.sort_order
    </sql>

    <!-- 根据问卷ID查询问题列表（包含选项） -->
    <select id="selectQuestionsByQuestionnaireId" resultMap="DetailResultMap">
        SELECT
            q.id, q.questionnaire_id, q.title, q.type,
            CASE q.type
                WHEN 'single' THEN '单选题'
                WHEN 'multi' THEN '多选题'
                WHEN 'text' THEN '简答题'
                WHEN 'rating' THEN '评分题'
                WHEN 'matrix' THEN '矩阵题'
                ELSE '未知'
            END as type_desc,
            q.score_id, q.max_score, q.required, q.sort_order,
            o.id as o_id, o.question_id as o_question_id, o.option_text as o_option_text,
            o.option_value as o_option_value, o.score as o_score, o.sort_order as o_sort_order
        FROM t_questionnaire_question q
        LEFT JOIN t_questionnaire_question_option o ON q.id = o.question_id AND o.deleted = 0
        WHERE q.questionnaire_id = #{questionnaireId} AND q.deleted = 0
        ORDER BY q.sort_order ASC, o.sort_order ASC
    </select>

    <!-- 批量删除问题（逻辑删除） -->
    <update id="batchDeleteByIds">
        UPDATE t_questionnaire_question 
        SET deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 根据问卷ID删除所有问题（逻辑删除） -->
    <update id="deleteByQuestionnaireId">
        UPDATE t_questionnaire_question 
        SET deleted = 1, update_time = NOW()
        WHERE questionnaire_id = #{questionnaireId} AND deleted = 0
    </update>

    <!-- 获取问卷的最大排序号 -->
    <select id="getMaxSortOrder" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(sort_order), 0)
        FROM t_questionnaire_question
        WHERE questionnaire_id = #{questionnaireId} AND deleted = 0
    </select>

    <!-- 基础CRUD操作 -->

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_questionnaire_question
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_questionnaire_question (
            questionnaire_id, title, type, score_id, max_score, required, sort_order,
            create_time, update_time, deleted
        ) VALUES (
            #{questionnaireId}, #{title}, #{type}, #{scoreId}, #{maxScore}, #{required}, #{sortOrder},
            #{createTime}, #{updateTime}, #{deleted}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_questionnaire_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionnaireId != null">questionnaire_id,</if>
            <if test="title != null">title,</if>
            <if test="type != null">type,</if>
            <if test="scoreId != null">score_id,</if>
            <if test="maxScore != null">max_score,</if>
            <if test="required != null">required,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="questionnaireId != null">#{questionnaireId},</if>
            <if test="title != null">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="scoreId != null">#{scoreId},</if>
            <if test="maxScore != null">#{maxScore},</if>
            <if test="required != null">#{required},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestion">
        UPDATE t_questionnaire_question SET
            questionnaire_id = #{questionnaireId},
            title = #{title},
            type = #{type},
            score_id = #{scoreId},
            max_score = #{maxScore},
            required = #{required},
            sort_order = #{sortOrder},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestion">
        UPDATE t_questionnaire_question
        <set>
            <if test="questionnaireId != null">questionnaire_id = #{questionnaireId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="scoreId != null">score_id = #{scoreId},</if>
            <if test="maxScore != null">max_score = #{maxScore},</if>
            <if test="required != null">required = #{required},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据主键删除（逻辑删除） -->
    <update id="deleteByPrimaryKey">
        UPDATE t_questionnaire_question SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>

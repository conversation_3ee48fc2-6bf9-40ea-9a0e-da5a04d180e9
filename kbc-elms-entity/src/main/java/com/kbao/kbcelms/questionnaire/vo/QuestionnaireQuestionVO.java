package com.kbao.kbcelms.questionnaire.vo;

import lombok.Data;

import java.util.List;

/**
 * 问卷问题视图对象
 */
@Data
public class QuestionnaireQuestionVO {

    /**
     * 问题ID
     */
    private Long id;

    /**
     * 问卷ID
     */
    private Long questionnaireId;

    /**
     * 问题标题
     */
    private String title;

    /**
     * 题型代码
     */
    private String type;

    /**
     * 题型描述
     */
    private String typeDesc;

    /**
     * 关联评分项
     */
    private Long scoreId;

    /**
     * 最高分
     */
    private Integer maxScore;

    /**
     * 是否必填：0-非必填，1-必填
     */
    private Integer required;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 选项数量（查询时统计）
     */
    private Integer optionCount;

    /**
     * 选项列表
     */
    private List<QuestionnaireQuestionOptionVO> options;
}

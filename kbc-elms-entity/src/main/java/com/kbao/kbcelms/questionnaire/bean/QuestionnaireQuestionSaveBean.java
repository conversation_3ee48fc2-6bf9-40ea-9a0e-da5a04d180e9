package com.kbao.kbcelms.questionnaire.bean;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 问卷问题保存参数
 */
@Data
public class QuestionnaireQuestionSaveBean {

    /**
     * 问题ID（编辑时传入）
     */
    private Long id;

    /**
     * 问题标题
     */
    @NotBlank(message = "问题标题不能为空")
    private String title;

    /**
     * 题型：single-单选，multi-多选，text-简答，rating-评分，matrix-矩阵
     */
    @NotBlank(message = "题型不能为空")
    private String type;

    /**
     * 关联评分项
     */
    private Long scoreId;

    /**
     * 最高分
     */
    private Integer maxScore;

    /**
     * 是否必填：0-非必填，1-必填
     */
    @NotNull(message = "是否必填不能为空")
    private Integer required;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 选项列表
     */
    @Valid
    private List<QuestionnaireQuestionOptionSaveBean> options;
}

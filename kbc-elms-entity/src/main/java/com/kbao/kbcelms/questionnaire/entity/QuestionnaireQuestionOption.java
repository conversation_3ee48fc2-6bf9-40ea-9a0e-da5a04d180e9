package com.kbao.kbcelms.questionnaire.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 问卷问题选项实体类
 */
@Data
public class QuestionnaireQuestionOption implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 选项ID
     */
    private Long id;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 选项文本
     */
    private String optionText;

    /**
     * 选项值
     */
    private String optionValue;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;
}

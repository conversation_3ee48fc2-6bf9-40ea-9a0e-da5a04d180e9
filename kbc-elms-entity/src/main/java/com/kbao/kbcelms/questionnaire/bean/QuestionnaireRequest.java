package com.kbao.kbcelms.questionnaire.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 根据企业类型查询问卷请求参数
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@ApiModel(value = "QuestionnaireByEnterpriseTypeRequest", description = "根据企业类型查询问卷请求参数")
public class QuestionnaireRequest {

    @ApiModelProperty(value = "问卷ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "企业类型编码", example = "A")
    private String enterpriseType;
}

package com.kbao.kbcelms.questionnaire.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 问卷答案实体类
 */
@Data
public class QuestionnaireAnswer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 答案ID
     */
    private Long id;

    /**
     * 问卷ID
     */
    private Long questionnaireId;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 题目
     */
    private String questionTitle;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 评分项ID
     */
    private Long scoreId;
    /**
     * 选项ID
     */
    private Long optionId;

    /**
     * 答案内容（选择题存选项值，简答题存文本）
     */
    private String answerContent;
    /**
     * 选项值
     */
    private String optionValue;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 分享人ID
     */
    private String shareUserId;

    /**
     * 提交人ID
     */
    private String submitUnionId;

    /**
     * 答题时间
     */
    private LocalDateTime answerTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;
}

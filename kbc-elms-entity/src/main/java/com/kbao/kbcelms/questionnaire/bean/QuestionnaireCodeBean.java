package com.kbao.kbcelms.questionnaire.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class QuestionnaireCodeBean {
    /**
     * 微信分享获取问卷专用，只传id容易被篡改。
     */

    @ApiModelProperty(value = "问卷ID", example = "1")
    @NotNull(message = "问卷编码不能为空！")
    private Long id;

    @ApiModelProperty(value = "问卷编码", example = "hde1289121kxcosdk")
    @NotBlank(message = "问卷编码不能为空！")
    private String code;
}

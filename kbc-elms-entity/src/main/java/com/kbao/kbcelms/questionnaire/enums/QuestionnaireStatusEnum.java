package com.kbao.kbcelms.questionnaire.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 问卷状态枚举
 */
@Getter
@AllArgsConstructor
public enum QuestionnaireStatusEnum {
    
    /**
     * 禁用
     */
    DISABLED(0, "禁用"),
    
    /**
     * 启用
     */
    ENABLED(1, "启用"),
    
    /**
     * 草稿
     */
    DRAFT(2, "草稿");
    
    /**
     * 状态值
     */
    private final Integer value;
    
    /**
     * 状态描述
     */
    private final String desc;
    
    /**
     * 根据值获取枚举
     */
    public static QuestionnaireStatusEnum getByValue(Integer value) {
        for (QuestionnaireStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 验证状态值是否有效
     */
    public static boolean isValidValue(Integer value) {
        return getByValue(value) != null;
    }
}

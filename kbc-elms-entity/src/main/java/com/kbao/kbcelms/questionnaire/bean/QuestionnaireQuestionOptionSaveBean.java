package com.kbao.kbcelms.questionnaire.bean;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 问卷问题选项保存参数
 */
@Data
public class QuestionnaireQuestionOptionSaveBean {

    /**
     * 选项ID（编辑时传入）
     */
    private Long id;

    private String questionId;

    /**
     * 选项文本
     */
    @NotBlank(message = "选项文本不能为空")
    private String optionText;

    /**
     * 选项值
     */
    @NotBlank(message = "选项值不能为空")
    private String optionValue;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 排序
     */
    private Integer sortOrder;
}

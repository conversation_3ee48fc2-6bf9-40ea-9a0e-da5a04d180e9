<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.auth.dao.AuthMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.auth.entity.Auth">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="authCode" column="auth_code" jdbcType="VARCHAR" />
        <result property="authName" column="auth_name" jdbcType="VARCHAR" />
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR" />
        <result property="authDesc" column="auth_desc" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
        <result property="sort" column="sort" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, auth_code, auth_name, parent_code, auth_desc, create_id, create_time, update_id, update_time, is_deleted, sort
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.auth_code,
        t.auth_name,
        t.parent_code,
        t.auth_desc,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted,
        t.sort
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="authCode != null">
                and t.auth_code = #{authCode,jdbcType=VARCHAR}
            </if>
            <if test="authName != null">
                and t.auth_name = #{authName,jdbcType=VARCHAR}
            </if>
            <if test="parentCode != null">
                and t.parent_code = #{parentCode,jdbcType=VARCHAR}
            </if>
            <if test="authDesc != null">
                and t.auth_desc = #{authDesc,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="sort != null">
                and t.sort = #{sort,jdbcType=INTEGER}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_auth where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_auth t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_auth t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.auth.entity.Auth" useGeneratedKeys="true" keyProperty="id">
        insert into t_auth (
            auth_code, auth_name, parent_code, auth_desc, create_id, create_time, update_id, update_time, is_deleted, sort
        ) values (
            #{authCode}, #{authName}, #{parentCode}, #{authDesc}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0, #{sort}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.auth.entity.Auth" useGeneratedKeys="true" keyProperty="id">
        insert into t_auth
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="authCode != null">auth_code,</if>
            <if test="authName != null">auth_name,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="authDesc != null">auth_desc,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            is_deleted,
            <if test="sort != null">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="authCode != null">#{authCode},</if>
            <if test="authName != null">#{authName},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="authDesc != null">#{authDesc},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            0,
            <if test="sort != null">#{sort},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.auth.entity.Auth">
        update t_auth set
            auth_code = #{authCode},
            auth_name = #{authName},
            parent_code = #{parentCode},
            auth_desc = #{authDesc},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted},
            sort = #{sort}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.auth.entity.Auth">
        update t_auth
        <set>
            <if test="authCode != null">auth_code = #{authCode},</if>
            <if test="authName != null">auth_name = #{authName},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="authDesc != null">auth_desc = #{authDesc},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="sort != null">sort = #{sort},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from t_auth where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_auth (
            auth_code, auth_name, parent_code, auth_desc, create_id, create_time, update_id, update_time, is_deleted, sort
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.authCode}, #{item.authName}, #{item.parentCode}, #{item.authDesc}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0, #{item.sort}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        delete from t_auth where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findByAuthCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Alias_Column_List"/>
        from t_auth t
        where t.auth_code = #{authCode} and t.is_deleted = 0
    </select>

    <select id="findParentAuth" resultMap="BaseResultMap">
        select
        <include refid="Alias_Column_List"/>
        from t_auth t
        where t.parent_code is null
    </select>

</mapper>
package com.kbao.kbcelms.formConfig.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import com.kbao.kbcelms.formConfig.model.FormConfigField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @Description 表单配置实体
 * @Date 2025-08-15
 */
@Data
public class FormConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 配置名称
     */
    private String configName;
    
    /**
     * 配置编码
     */
    private String configCode;

    /**
     * 分类信息：1-企业信息，2-员福配置，3-企业补充信息
     */
    private String type;

    /**
     * 状态：0-禁用，1-启用
     */
    private String status;

    /**
     * 描述
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 创建人
     */
    private String createId;
    
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 更新人
     */
    private String updateId;
    
    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDeleted;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 关联字段列表
     */
    private List<FormConfigField> fields;
}

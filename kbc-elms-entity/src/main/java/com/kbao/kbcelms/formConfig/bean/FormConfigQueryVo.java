package com.kbao.kbcelms.formConfig.bean;

import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 表单配置查询VO
 * @Date 2025-08-15
 */
@Data
public class FormConfigQueryVo implements Serializable {
    
    /**
     * 配置名称
     */
    private String configName;
    
    /**
     * 配置编码
     */
    private String configCode;

    /**
     * 分类信息：1-企业信息，2-员福配置，3-企业补充信息
     */
    private String type;

    /**
     * 状态：0-禁用，1-启用
     */
    private String status;
}

package com.kbao.kbcelms.formula.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 公式变量DTO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "FormulaVariableDTO", description = "公式变量DTO")
public class FormulaVariableDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 公式ID
     */
    @ApiModelProperty(value = "公式ID")
    private Long formulaId;

    /**
     * 变量名称
     */
    @ApiModelProperty(value = "变量名称", required = true)
    @NotBlank(message = "变量名称不能为空")
    @Size(max = 50, message = "变量名称长度不能超过50个字符")
    private String name;

    /**
     * 变量类型：number-数值，variable-变量，constant-常数
     */
    @ApiModelProperty(value = "变量类型：number-数值，variable-变量，constant-常数", required = true)
    @NotBlank(message = "变量类型不能为空")
    private String type;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private BigDecimal defaultValue;

    /**
     * 最小值
     */
    @ApiModelProperty(value = "最小值")
    private BigDecimal minValue;

    /**
     * 最大值
     */
    @ApiModelProperty(value = "最大值")
    private BigDecimal maxValue;

    /**
     * 变量描述
     */
    @ApiModelProperty(value = "变量描述")
    @Size(max = 200, message = "变量描述长度不能超过200个字符")
    private String description;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Size(max = 20, message = "单位长度不能超过20个字符")
    private String unit;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;
}

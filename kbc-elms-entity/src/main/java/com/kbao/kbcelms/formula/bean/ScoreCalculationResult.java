package com.kbao.kbcelms.formula.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评分项计算结果
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreCalculationResult {

    /**
     * 是否计算成功
     */
    private boolean success;

    /**
     * 评分项ID
     */
    private Long scoreItemId;

    /**
     * 评分项名称
     */
    private String scoreItemName;

    /**
     * 基础分数（问卷答案分数）
     */
    private BigDecimal baseScore;

    /**
     * 公式计算后分数
     */
    private BigDecimal formulaScore;

    /**
     * 最终分数（应用系数后）
     */
    private BigDecimal finalScore;

    /**
     * 应用的系数
     */
    private BigDecimal coefficient;

    /**
     * 使用的公式ID
     */
    private Long formulaId;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

    /**
     * 计算时间
     */
    private LocalDateTime calculationTime;

    /**
     * 创建成功结果
     */
    public static ScoreCalculationResult success(Long scoreItemId, 
                                               String scoreItemName,
                                               BigDecimal baseScore,
                                               BigDecimal formulaScore,
                                               BigDecimal finalScore,
                                               BigDecimal coefficient,
                                               Long formulaId) {
        ScoreCalculationResult result = new ScoreCalculationResult();
        result.setSuccess(true);
        result.setScoreItemId(scoreItemId);
        result.setScoreItemName(scoreItemName);
        result.setBaseScore(baseScore);
        result.setFormulaScore(formulaScore);
        result.setFinalScore(finalScore);
        result.setCoefficient(coefficient);
        result.setFormulaId(formulaId);
        result.setCalculationTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建失败结果
     */
    public static ScoreCalculationResult failed(String errorMessage, String scoreItemName) {
        ScoreCalculationResult result = new ScoreCalculationResult();
        result.setSuccess(false);
        result.setScoreItemName(scoreItemName);
        result.setErrorMessage(errorMessage);
        result.setCalculationTime(LocalDateTime.now());
        return result;
    }

    /**
     * 获取有效的最终分数（失败时返回0）
     */
    public BigDecimal getValidFinalScore() {
        return success && finalScore != null ? finalScore : BigDecimal.ZERO;
    }

    /**
     * 是否使用了公式计算
     */
    public boolean isFormulaUsed() {
        return formulaId != null && formulaScore != null && 
               baseScore != null && !formulaScore.equals(baseScore);
    }

    /**
     * 是否使用了系数
     */
    public boolean isCoefficientUsed() {
        return coefficient != null && coefficient.compareTo(BigDecimal.ONE) != 0;
    }

    /**
     * 获取计算过程描述
     */
    public String getCalculationDescription() {
        if (!success) {
            return "计算失败: " + errorMessage;
        }

        StringBuilder desc = new StringBuilder();
        desc.append("基础分数: ").append(baseScore);
        
        if (isFormulaUsed()) {
            desc.append(" -> 公式计算: ").append(formulaScore);
        }
        
        if (isCoefficientUsed()) {
            desc.append(" -> 系数(").append(coefficient).append("): ").append(finalScore);
        }
        
        return desc.toString();
    }
}

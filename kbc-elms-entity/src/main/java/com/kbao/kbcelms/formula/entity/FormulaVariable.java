package com.kbao.kbcelms.formula.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 公式变量实体类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "FormulaVariable", description = "公式变量实体")
public class FormulaVariable {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 公式ID
     */
    @ApiModelProperty(value = "公式ID")
    private Long formulaId;

    /**
     * 变量名称
     */
    @ApiModelProperty(value = "变量名称")
    private String name;

    /**
     * 变量类型：number-数值，variable-变量，constant-常数
     */
    @ApiModelProperty(value = "变量类型：number-数值，variable-变量，constant-常数")
    private String type;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private BigDecimal defaultValue;

    /**
     * 最小值
     */
    @ApiModelProperty(value = "最小值")
    private BigDecimal minValue;

    /**
     * 最大值
     */
    @ApiModelProperty(value = "最大值")
    private BigDecimal maxValue;

    /**
     * 变量描述
     */
    @ApiModelProperty(value = "变量描述")
    private String description;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标识：0-未删除，1-已删除
     */
    @ApiModelProperty(value = "删除标识：0-未删除，1-已删除")
    private Integer deleted;
}

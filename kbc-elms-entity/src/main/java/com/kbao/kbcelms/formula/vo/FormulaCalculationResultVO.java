package com.kbao.kbcelms.formula.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 公式计算结果VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "FormulaCalculationResultVO", description = "公式计算结果VO")
public class FormulaCalculationResultVO {

    /**
     * 公式ID
     */
    @ApiModelProperty(value = "公式ID")
    private Long formulaId;

    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称")
    private String formulaName;

    /**
     * 公式内容
     */
    @ApiModelProperty(value = "公式内容")
    private String formula;

    /**
     * 公式计算
     */
    @ApiModelProperty(value = "公式计算")
    private String formulaProcess;

    /**
     * 输入变量
     */
    @ApiModelProperty(value = "输入变量")
    private Map<String, BigDecimal> inputVariables;

    /**
     * 计算结果
     */
    @ApiModelProperty(value = "计算结果")
    private BigDecimal result;

    /**
     * 结果单位
     */
    @ApiModelProperty(value = "结果单位")
    private String unit;

    /**
     * 计算时间
     */
    @ApiModelProperty(value = "计算时间")
    private LocalDateTime calculationTime;

    /**
     * 执行时间（毫秒）
     */
    @ApiModelProperty(value = "执行时间（毫秒）")
    private Integer executionTimeMs;

    /**
     * 计算状态：1-成功，0-失败
     */
    @ApiModelProperty(value = "计算状态：1-成功，0-失败")
    private Integer status;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
}

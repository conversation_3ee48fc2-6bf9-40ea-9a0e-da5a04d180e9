package com.kbao.kbcelms.formula.vo;

import com.kbao.kbcelms.constant.vo.ConstantConfigVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 公式VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "FormulaVO", description = "公式VO")
public class FormulaVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称")
    private String name;

    /**
     * 公式描述
     */
    @ApiModelProperty(value = "公式描述")
    private String description;

    /**
     * 公式内容
     */
    @ApiModelProperty(value = "公式内容")
    private String formula;

    /**
     * 分类：1-战略文化与治理框架，2-组织责任与指标体系，3-风险评估与流程执行，4-沟通系统与技术支持
     */
    @ApiModelProperty(value = "分类")
    private Integer category;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 企业类型：A-大型企业，B-中型企业，C-小型企业
     */
    @ApiModelProperty(value = "企业类型：A-大型企业，B-中型企业，C-小型企业")
    private String enterpriseType;

    /**
     * 企业类型名称
     */
    @ApiModelProperty(value = "企业类型名称")
    private String enterpriseTypeName;

    /**
     * 企业类型列表
     */
    @ApiModelProperty(value = "企业类型列表")
    private List<String> enterpriseTypeList;

    /**
     * 状态：0-禁用，1-启用
     */
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;

    /**
     * 状态描述
     */
    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String version;

    /**
     * 使用次数
     */
    @ApiModelProperty(value = "使用次数")
    private Integer usageCount;

    /**
     * 最后测试时间
     */
    @ApiModelProperty(value = "最后测试时间")
    private LocalDateTime lastTestTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 公式变量列表
     */
    @ApiModelProperty(value = "公式变量列表")
    private List<FormulaVariableVO> variables;

    /**
     * 常量列表
     */
    @ApiModelProperty(value = "常量列表")
    private List<ConstantConfigVO> configVOs;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.formula.dao.FormulaCalculationLogMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.formula.entity.FormulaCalculationLog">
        <id column="id" property="id"/>
        <result column="formula_id" property="formulaId"/>
        <result column="formula_name" property="formulaName"/>
        <result column="input_variables" property="inputVariables"/>
        <result column="formula_process" property="formulaProcess"/>
        <result column="calculation_result" property="calculationResult"/>
        <result column="calculation_time" property="calculationTime"/>
        <result column="execution_time_ms" property="executionTimeMs"/>
        <result column="status" property="status"/>
        <result column="error_message" property="errorMessage"/>
        <result column="user_id" property="userId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, formula_id, formula_name, input_variables, calculation_result, calculation_time,
        execution_time_ms, status, error_message, user_id, create_time
    </sql>

    <!-- 根据公式ID查询计算记录 -->
    <select id="selectByFormulaId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_formula_calculation_log
        WHERE formula_id = #{formulaId}
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据用户ID查询计算记录 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_formula_calculation_log
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计公式使用次数 -->
    <select id="countByFormulaId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_formula_calculation_log
        WHERE formula_id = #{formulaId} AND status = 1
    </select>

    <!-- 清理过期记录 -->
    <delete id="deleteExpiredLogs">
        DELETE FROM t_formula_calculation_log
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

    <!-- 统计所有计算记录数量 -->
    <select id="countAll" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_formula_calculation_log
    </select>

    <!-- 插入计算记录 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.formula.entity.FormulaCalculationLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_formula_calculation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formulaId != null">formula_id,</if>
            <if test="formulaName != null">formula_name,</if>
            <if test="inputVariables != null">input_variables,</if>
            <if test="formulaProcess != null">formula_process,</if>
            <if test="calculationResult != null">calculation_result,</if>
            <if test="calculationTime != null">calculation_time,</if>
            <if test="executionTimeMs != null">execution_time_ms,</if>
            <if test="status != null">status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="formulaId != null">#{formulaId},</if>
            <if test="formulaName != null">#{formulaName},</if>
            <if test="inputVariables != null">#{inputVariables},</if>
            <if test="formulaProcess != null">#{formulaProcess},</if>
            <if test="calculationResult != null">#{calculationResult},</if>
            <if test="calculationTime != null">#{calculationTime},</if>
            <if test="executionTimeMs != null">#{executionTimeMs},</if>
            <if test="status != null">#{status},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_formula_calculation_log
        WHERE id = #{id}
    </select>

    <!-- 更新计算记录 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.formula.entity.FormulaCalculationLog">
        UPDATE t_formula_calculation_log
        <set>
            <if test="formulaId != null">formula_id = #{formulaId},</if>
            <if test="formulaName != null">formula_name = #{formulaName},</if>
            <if test="inputVariables != null">input_variables = #{inputVariables},</if>
            <if test="calculationResult != null">calculation_result = #{calculationResult},</if>
            <if test="calculationTime != null">calculation_time = #{calculationTime},</if>
            <if test="executionTimeMs != null">execution_time_ms = #{executionTimeMs},</if>
            <if test="status != null">status = #{status},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除计算记录 -->
    <delete id="deleteByPrimaryKey">
        DELETE FROM t_formula_calculation_log WHERE id = #{id}
    </delete>

</mapper>

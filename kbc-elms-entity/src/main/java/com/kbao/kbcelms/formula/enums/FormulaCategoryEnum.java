package com.kbao.kbcelms.formula.enums;

/**
 * 公式分类枚举
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public enum FormulaCategoryEnum {

    /**
     * 战略文化与治理框架
     */
    STRATEGY_CULTURE_GOVERNANCE(1, "战略文化与治理框架", "战略层面的风险管理公式"),

    /**
     * 组织责任与指标体系
     */
    ORGANIZATION_RESPONSIBILITY_INDICATOR(2, "组织责任与指标体系", "组织层面的风险管理公式"),

    /**
     * 风险评估与流程执行
     */
    RISK_ASSESSMENT_PROCESS_EXECUTION(3, "风险评估与流程执行", "执行层面的风险管理公式"),

    /**
     * 沟通系统与技术支持
     */
    COMMUNICATION_SYSTEM_TECHNICAL_SUPPORT(4, "沟通系统与技术支持", "技术层面的风险管理公式");

    private final Integer code;
    private final String name;
    private final String description;

    FormulaCategoryEnum(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static FormulaCategoryEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FormulaCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        FormulaCategoryEnum category = getByCode(code);
        return category != null ? category.getName() : null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescriptionByCode(Integer code) {
        FormulaCategoryEnum category = getByCode(code);
        return category != null ? category.getDescription() : null;
    }
}

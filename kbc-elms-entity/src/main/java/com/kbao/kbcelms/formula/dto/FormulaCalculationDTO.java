package com.kbao.kbcelms.formula.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 公式计算DTO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "FormulaCalculationDTO", description = "公式计算DTO")
public class FormulaCalculationDTO {

    /**
     * 公式ID
     */
    @ApiModelProperty(value = "公式ID", required = true)
    @NotNull(message = "公式ID不能为空")
    private Long formulaId;

    /**
     * 输入变量
     */
    @ApiModelProperty(value = "输入变量")
    private Map<String, BigDecimal> variables;

    /**
     * 评分项得分
     */
    private BigDecimal score;
}

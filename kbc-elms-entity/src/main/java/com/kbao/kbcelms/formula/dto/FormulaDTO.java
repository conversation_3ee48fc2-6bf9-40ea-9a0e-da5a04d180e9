package com.kbao.kbcelms.formula.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 公式DTO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "FormulaDTO", description = "公式DTO")
public class FormulaDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称", required = true)
    @NotBlank(message = "公式名称不能为空")
    @Size(max = 100, message = "公式名称长度不能超过100个字符")
    private String name;

    /**
     * 公式描述
     */
    @ApiModelProperty(value = "公式描述")
    @Size(max = 500, message = "公式描述长度不能超过500个字符")
    private String description;

    /**
     * 公式内容
     */
    @ApiModelProperty(value = "公式内容", required = true)
    @NotBlank(message = "公式内容不能为空")
    private String formula;

    /**
     * 企业类型：A-大型企业，B-中型企业，C-小型企业
     */
    @ApiModelProperty(value = "企业类型：A-大型企业，B-中型企业，C-小型企业")
    private String enterpriseType;

    /**
     * 状态：0-禁用，1-启用
     */
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @Size(max = 20, message = "版本号长度不能超过20个字符")
    private String version;

    /**
     * 公式变量列表
     */
    @ApiModelProperty(value = "公式变量列表")
    @Valid
    private List<FormulaVariableDTO> variables;
}

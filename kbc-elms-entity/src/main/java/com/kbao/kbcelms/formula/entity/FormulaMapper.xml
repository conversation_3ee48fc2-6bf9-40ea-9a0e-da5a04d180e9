<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.formula.dao.FormulaMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.formula.entity.Formula">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="formula" property="formula"/>
        <result column="category" property="category"/>
        <result column="enterprise_type" property="enterpriseType"/>
        <result column="status" property="status"/>
        <result column="version" property="version"/>
        <result column="usage_count" property="usageCount"/>
        <result column="last_test_time" property="lastTestTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- VO结果映射 -->
    <resultMap id="VOResultMap" type="com.kbao.kbcelms.formula.vo.FormulaVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="formula" property="formula"/>
        <result column="category" property="category"/>
        <result column="enterprise_type" property="enterpriseType"/>
        <result column="status" property="status"/>
        <result column="version" property="version"/>
        <result column="usage_count" property="usageCount"/>
        <result column="last_test_time" property="lastTestTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, name, description, formula, category, enterprise_type, status, version,
        usage_count, last_test_time, create_time, update_time, create_user, update_user, deleted
    </sql>

    <!-- 根据名称查询公式 -->
    <select id="selectByName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_formula
        WHERE name = #{name} AND deleted = 0
    </select>

    <!-- 检查名称是否存在 -->
    <select id="checkNameExists" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_formula
        WHERE name = #{name} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 分页查询公式列表 -->
    <select id="selectFormulaList" resultMap="VOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_formula
        WHERE deleted = 0
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="category != null">
            AND category = #{category}
        </if>
        <if test="enterpriseType != null and enterpriseType.size() > 0">
            AND (
                <foreach collection="enterpriseType" item="type" separator=" AND ">
                    (enterprise_type IS NOT NULL AND FIND_IN_SET(#{type}, enterprise_type) > 0)
                </foreach>
                OR enterprise_type IS NULL
            )
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="version != null and version != ''">
            AND version = #{version}
        </if>
        ORDER BY update_time DESC
    </select>

    <!-- 根据ID查询公式详情 -->
    <select id="selectFormulaDetail" resultMap="VOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_formula
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 更新使用次数 -->
    <update id="updateUsageCount">
        UPDATE t_formula SET
            usage_count = usage_count + 1,
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 更新最后测试时间 -->
    <update id="updateLastTestTime">
        UPDATE t_formula SET
            last_test_time = NOW(),
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>





    <!-- 插入公式 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.formula.entity.Formula" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_formula
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="formula != null">formula,</if>
            <if test="category != null">category,</if>
            <if test="enterpriseType != null">enterprise_type,</if>
            <if test="status != null">status,</if>
            <if test="version != null">version,</if>
            <if test="usageCount != null">usage_count,</if>
            <if test="lastTestTime != null">last_test_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            deleted
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="formula != null">#{formula},</if>
            <if test="category != null">#{category},</if>
            <if test="enterpriseType != null">#{enterpriseType},</if>
            <if test="status != null">#{status},</if>
            <if test="version != null">#{version},</if>
            <if test="usageCount != null">#{usageCount},</if>
            <if test="lastTestTime != null">#{lastTestTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            0
        </trim>
    </insert>

    <!-- 更新公式 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.formula.entity.Formula">
        UPDATE t_formula
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="formula != null">formula = #{formula},</if>
            <if test="category != null">category = #{category},</if>
            <if test="enterpriseType != null">enterprise_type = #{enterpriseType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="version != null">version = #{version},</if>
            <if test="usageCount != null">usage_count = #{usageCount},</if>
            <if test="lastTestTime != null">last_test_time = #{lastTestTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 删除公式（逻辑删除） -->
    <update id="deleteByPrimaryKey">
        UPDATE t_formula SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_formula
        WHERE id = #{id} AND deleted = 0
    </select>

</mapper>

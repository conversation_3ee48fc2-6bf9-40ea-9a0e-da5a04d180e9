package com.kbao.kbcelms.formula.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 公式查询DTO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "FormulaQueryDTO", description = "公式查询DTO")
public class FormulaQueryDTO {

    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称")
    private String name;

    /**
     * 分类：1-战略文化与治理框架，2-组织责任与指标体系，3-风险评估与流程执行，4-沟通系统与技术支持
     */
    @ApiModelProperty(value = "分类")
    private Integer category;

    /**
     * 企业类型：A-大型企业，B-中型企业，C-小型企业（支持多选）
     */
    @ApiModelProperty(value = "企业类型：A-大型企业，B-中型企业，C-小型企业（支持多选）")
    private List<String> enterpriseType;

    /**
     * 状态：0-禁用，1-启用
     */
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String version;

    @Override
    public String toString() {
        return "FormulaQueryDTO{" +
                "name='" + name + '\'' +
                ", category=" + category +
                ", enterpriseType=" + enterpriseType +
                ", status=" + status +
                ", version='" + version + '\'' +
                '}';
    }
}

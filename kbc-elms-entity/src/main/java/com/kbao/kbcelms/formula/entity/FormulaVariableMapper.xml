<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.formula.dao.FormulaVariableMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.formula.entity.FormulaVariable">
        <id column="id" property="id"/>
        <result column="formula_id" property="formulaId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="default_value" property="defaultValue"/>
        <result column="min_value" property="minValue"/>
        <result column="max_value" property="maxValue"/>
        <result column="description" property="description"/>
        <result column="unit" property="unit"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- VO结果映射 -->
    <resultMap id="VOResultMap" type="com.kbao.kbcelms.formula.vo.FormulaVariableVO">
        <id column="id" property="id"/>
        <result column="formula_id" property="formulaId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="default_value" property="defaultValue"/>
        <result column="min_value" property="minValue"/>
        <result column="max_value" property="maxValue"/>
        <result column="description" property="description"/>
        <result column="unit" property="unit"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, formula_id, name, type, default_value, min_value, max_value, 
        description, unit, sort_order, create_time, update_time, deleted
    </sql>

    <!-- 根据公式ID查询变量列表 -->
    <select id="selectByFormulaId" resultMap="VOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_formula_variable
        WHERE formula_id = #{formulaId} AND deleted = 0
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据公式ID删除变量 -->
    <update id="deleteByFormulaId">
        UPDATE t_formula_variable SET deleted = 1, update_time = NOW()
        WHERE formula_id = #{formulaId}
    </update>

    <!-- 批量插入变量 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_formula_variable (
            formula_id, name, type, default_value, min_value, max_value,
            description, unit, sort_order, deleted
        ) VALUES
        <foreach collection="variables" item="item" separator=",">
            (
                #{item.formulaId}, #{item.name}, #{item.type}, #{item.defaultValue},
                #{item.minValue}, #{item.maxValue}, #{item.description}, #{item.unit},
                #{item.sortOrder}, 0
            )
        </foreach>
    </insert>

    <!-- 检查变量名称是否存在 -->
    <select id="checkNameExists" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_formula_variable
        WHERE formula_id = #{formulaId} AND name = #{name} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 插入变量 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.formula.entity.FormulaVariable" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_formula_variable
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formulaId != null">formula_id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="defaultValue != null">default_value,</if>
            <if test="minValue != null">min_value,</if>
            <if test="maxValue != null">max_value,</if>
            <if test="description != null">description,</if>
            <if test="unit != null">unit,</if>
            <if test="sortOrder != null">sort_order,</if>
            deleted
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="formulaId != null">#{formulaId},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="defaultValue != null">#{defaultValue},</if>
            <if test="minValue != null">#{minValue},</if>
            <if test="maxValue != null">#{maxValue},</if>
            <if test="description != null">#{description},</if>
            <if test="unit != null">#{unit},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            0
        </trim>
    </insert>

    <!-- 更新变量 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.formula.entity.FormulaVariable">
        UPDATE t_formula_variable
        <set>
            <if test="formulaId != null">formula_id = #{formulaId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="defaultValue != null">default_value = #{defaultValue},</if>
            <if test="minValue != null">min_value = #{minValue},</if>
            <if test="maxValue != null">max_value = #{maxValue},</if>
            <if test="description != null">description = #{description},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 删除变量（逻辑删除） -->
    <update id="deleteByPrimaryKey">
        UPDATE t_formula_variable SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_formula_variable
        WHERE id = #{id} AND deleted = 0
    </select>

</mapper>

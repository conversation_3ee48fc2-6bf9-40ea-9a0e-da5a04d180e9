package com.kbao.kbcelms.opportunity.dto;

import com.kbao.commons.web.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 机会待办任务查询请求示例
 * 展示如何使用新的查询参数进行分页查询
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "机会待办任务查询请求示例")
public class OpportunityTodoQueryExample {
    
    @ApiModelProperty(value = "分页参数", example = "{\"pageNum\":1,\"pageSize\":10}")
    private PageRequest<OpportunityTodoQueryDTO> pageRequest;
    
    /**
     * 创建查询顾问姓名的示例
     */
    public static OpportunityTodoQueryDTO createAgentNameQuery(String agentName) {
        OpportunityTodoQueryDTO query = new OpportunityTodoQueryDTO();
        query.setAgentName(agentName);
        return query;
    }
    
    /**
     * 创建查询机会名称的示例
     */
    public static OpportunityTodoQueryDTO createOpportunityNameQuery(String opportunityName) {
        OpportunityTodoQueryDTO query = new OpportunityTodoQueryDTO();
        query.setOpportunityName(opportunityName);
        return query;
    }
    
    /**
     * 创建查询企业名称的示例
     */
    public static OpportunityTodoQueryDTO createEnterpriseNameQuery(String enterpriseName) {
        OpportunityTodoQueryDTO query = new OpportunityTodoQueryDTO();
        query.setEnterpriseName(enterpriseName);
        return query;
    }
    
    /**
     * 创建复合查询的示例
     */
    public static OpportunityTodoQueryDTO createComplexQuery(String agentName, String legalCode, Integer status) {
        OpportunityTodoQueryDTO query = new OpportunityTodoQueryDTO();
        query.setAgentName(agentName);
        query.setLegalCode(legalCode);
        query.setStatus(status);
        return query;
    }
    
    /**
     * 创建包含机会类型和关闭原因类型的复合查询示例
     */
    public static OpportunityTodoQueryDTO createComplexQueryWithTypeAndCloseReason(String agentName, String legalCode, Integer status, String opportunityType, Integer closeReasonType) {
        OpportunityTodoQueryDTO query = new OpportunityTodoQueryDTO();
        query.setAgentName(agentName);
        query.setLegalCode(legalCode);
        query.setStatus(status);
        query.setOpportunityType(opportunityType);
        query.setCloseReasonType(closeReasonType);
        return query;
    }
}

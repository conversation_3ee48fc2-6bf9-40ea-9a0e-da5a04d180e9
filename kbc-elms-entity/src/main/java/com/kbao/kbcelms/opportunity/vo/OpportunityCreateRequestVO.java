package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增机会请求VO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "新增机会请求VO")
public class OpportunityCreateRequestVO {
    
    @NotBlank(message = "顾问工号不能为空")
    @ApiModelProperty(value = "顾问工号", required = true, example = "AG001")
    private String agentCode;
    
        @NotBlank(message = "机会名称不能为空")
    @ApiModelProperty(value = "机会名称", required = true, example = "某公司员工福利保险项目")
    private String opportunityName;

    @ApiModelProperty(value = "机会类型", example = "1", notes = "1-员服，2-综合，自动根据企业类型确定")
    private String opportunityType;
    
    @ApiModelProperty(value = "关联行业编码", example = "INS001")
    private String industryCode;
    
    @ApiModelProperty(value = "保费预算", example = "1000000")
    private String premiumBudget;
    
    @ApiModelProperty(value = "投保人员规模", example = "500")
    private Integer insureNum;
    
    @ApiModelProperty(value = "是否有历史保单", example = "0", notes = "0-否，1-是")
    private String hasHistoryPolicy;
    
    @ApiModelProperty(value = "企业对接人", example = "李经理")
    private String contacter;
    
    @ApiModelProperty(value = "企业对接人职务", example = "人事经理")
    private String contacterPosition;
    
    @ApiModelProperty(value = "企业对接人电话", example = "13800138000")
    private String contacterPhone;
    
    @ApiModelProperty(value = "备注", example = "客户需求描述")
    private String remark;
}

package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/25 16:08
 */
@Data
public class OpportunityInquiryEnterpriseRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业名称", example = "某保险经纪有限公司")
    private String name;

    @ApiModelProperty(value = "社会信用代码", example = "91110000123456789X")
    private String creditCode;

    @ApiModelProperty(value = "行业分类-大类", example = "J66")
    private String categoryFirstCode;

    @ApiModelProperty(value = "行业分类-小类", example = "J66")
    private String categorySecondCode;

    /** 保单到期日期 */
    @ApiModelProperty(value = "保单到期日期", example = "2025-12-31 23:59:59")
    private Date policyExpireTime;
}

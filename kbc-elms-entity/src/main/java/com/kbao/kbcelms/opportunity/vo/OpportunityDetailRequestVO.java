package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 机会详情查询请求VO
 */
@Data
@ApiModel(value = "OpportunityDetailRequestVO", description = "机会详情查询请求VO")
public class OpportunityDetailRequestVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "机会ID", required = true, example = "123")
    @NotNull(message = "机会ID不能为空")
    private Integer opportunityId;
} 
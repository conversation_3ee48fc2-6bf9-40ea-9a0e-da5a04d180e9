package com.kbao.kbcelms.opportunity.vo;

import com.kbao.kbcelms.opportunity.entity.Opportunity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/8/11 9:49
 */
@Data
public class OpportunityJoinResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("项目经理id-对应云服登录账号")
    private String projectManagerId;

    @ApiModelProperty("项目经理名")
    private String projectManagerName;

    @ApiModelProperty("项目归属机构名称")
    private String projectOrgName;
}

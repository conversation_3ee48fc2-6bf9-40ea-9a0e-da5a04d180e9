package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 新增机会响应VO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "新增机会响应VO")
public class OpportunityCreateResponseVO {
    
    @ApiModelProperty(value = "机会ID", example = "1001")
    private Integer opportunityId;
    
    @ApiModelProperty(value = "机会编码", example = "OPP202501150001")
    private String bizCode;
    
    @ApiModelProperty(value = "机会名称", example = "某公司员工福利保险项目")
    private String opportunityName;
    
    @ApiModelProperty(value = "顾问工号", example = "AG001")
    private String agentCode;
    
    @ApiModelProperty(value = "顾问姓名", example = "张三")
    private String agentName;
    
    @ApiModelProperty(value = "机会状态", example = "1", notes = "0-待提交，1-已提交，2-锁定，3-中止，4-终止")
    private Integer status;
    
    @ApiModelProperty(value = "关联顾问企业ID", example = "4")
    private Integer agentEnterpriseId;
    
    @ApiModelProperty(value = "创建时间", example = "2025-01-15 16:30:00")
    private Date createTime;
    
    @ApiModelProperty(value = "区域中心编码", example = "AREA001")
    private String areaCenterCode;
    
    @ApiModelProperty(value = "区域中心名称", example = "华北区域中心")
    private String areaCenterName;
    
    @ApiModelProperty(value = "法人公司编码", example = "LEGAL001")
    private String legalCode;
    
    @ApiModelProperty(value = "法人公司名称", example = "某保险公司")
    private String legalName;
    
    @ApiModelProperty(value = "分公司编码", example = "BRANCH001")
    private String companyCode;
    
    @ApiModelProperty(value = "分公司名称", example = "北京分公司")
    private String companyName;
    
    @ApiModelProperty(value = "交易服务中心编码", example = "TRADE001")
    private String tradingCenterCode;
    
    @ApiModelProperty(value = "交易服务中心名称", example = "北京交易服务中心")
    private String tradingCenterName;
    
    @ApiModelProperty(value = "销售服务中心编码", example = "SALES001")
    private String salesCenterCode;
    
    @ApiModelProperty(value = "销售服务中心名称", example = "北京销售服务中心")
    private String salesCenterName;
}

package com.kbao.kbcelms.opportunity.model;

import cn.hutool.json.JSONObject;import com.kbao.commons.annotation.Excel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 表单配置字段实体
 * @Date 2025-08-15
 */
@Data
@Document(collection = "OpportunityInsureInfo")
public class OpportunityInsureInfo implements Serializable {
    
    /**
     * MongoDB主键
     */
    @Id
    private String id;
    
    /**
     * 配置ID
     */
    @Indexed
    private Integer opportunityId;

    private JSONObject content;

    private Date createTime;
}

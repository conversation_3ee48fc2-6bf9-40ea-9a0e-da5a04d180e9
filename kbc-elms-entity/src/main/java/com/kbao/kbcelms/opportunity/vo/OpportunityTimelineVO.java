package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 机会时间线VO
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
@ApiModel(value = "OpportunityTimelineVO", description = "机会时间线VO")
public class OpportunityTimelineVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "时间线ID")
    private String timelineId;

    @ApiModelProperty(value = "类型：PROGRESS-流程进度，LOG-操作日志")
    private String type;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    /** 关闭原因 */
    @ApiModelProperty(value = "关闭原因")
    private Integer reasonType;
    /** 原因描述 */
    @ApiModelProperty(value = "原因描述")
    private String reasonDesc;

    @ApiModelProperty(value = "操作人ID")
    private String operatorId;

    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    @ApiModelProperty(value = "目标人ID")
    private String targetId;

    @ApiModelProperty(value = "目标人姓名")
    private String targetName;

    @ApiModelProperty(value = "时间")
    private Date timelineTime;

    @ApiModelProperty(value = "状态：1-已完成，0-进行中，-1-待执行")
    private String status;

    @ApiModelProperty(value = "活动节点ID（流程进度专用）")
    private String activityId;

    @ApiModelProperty(value = "活动节点名称（流程进度专用）")
    private String activityName;

    @ApiModelProperty(value = "开始时间（流程进度专用）")
    private Date startTime;

    @ApiModelProperty(value = "结束时间（流程进度专用）")
    private Date endTime;

    @ApiModelProperty(value = "流程节点自定义属性（流程进度专用）")
    private Map<String, Object> customProperties;

    @ApiModelProperty(value = "节点类型（流程进度专用）")
    private String nodeType;

    @ApiModelProperty(value = "节点编码（流程进度专用）")
    private String nodeCode;

    @ApiModelProperty(value = "节点描述（流程进度专用）")
    private String nodeDescription;

    @ApiModelProperty(value = "处理人角色（流程进度专用）")
    private String assigneeRole;

    @ApiModelProperty(value = "处理人组织（流程进度专用）")
    private String assigneeOrg;

    @ApiModelProperty(value = "机会完成人")
    private String assignee;
}
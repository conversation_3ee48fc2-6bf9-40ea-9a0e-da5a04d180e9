package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 员工线索统计VO
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@ApiModel(value = "EmployeeLeadVO", description = "员工线索统计")
public class EmployeeLeadVO {

    /**
     * 顾问工号
     */
    @ApiModelProperty(value = "顾问工号")
    private String agentCode;

    /**
     * 顾问姓名
     */
    @ApiModelProperty(value = "顾问姓名")
    private String agentName;

    /**
     * 法人公司名称（机构）
     */
    @ApiModelProperty(value = "法人公司名称")
    private String legalName;

    /**
     * 销售服务中心名称（营业部）
     */
    @ApiModelProperty(value = "销售服务中心名称")
    private String tradingCenterName;

    /**
     * 企业客户数量
     */
    @ApiModelProperty(value = "企业客户数量")
    private Integer agentEnterpriseNum;

    /**
     * 已验真企业数量
     */
    @ApiModelProperty(value = "已验真企业数量")
    private Integer verifiedEnterpriseNum;

    /**
     * 机会数量
     */
    @ApiModelProperty(value = "机会数量")
    private Integer opportunityNum;

    /**
     * 员福机会数量
     */
    @ApiModelProperty(value = "员福机会数量")
    private Integer emplayeeOpportunityNum;

    /**
     * 综合保障机会数量
     */
    @ApiModelProperty(value = "综合保障机会数量")
    private Integer generalOpportunityNum;
}

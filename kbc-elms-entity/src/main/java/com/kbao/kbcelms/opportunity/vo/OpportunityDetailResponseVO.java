package com.kbao.kbcelms.opportunity.vo;

import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 机会详情查询响应VO
 */
@Data
@ApiModel(value = "OpportunityDetailResponseVO", description = "机会详情查询响应VO")
public class OpportunityDetailResponseVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "机会基本信息")
    private Opportunity opportunity;
    
    @ApiModelProperty(value = "机会详细信息")
    private OpportunityDetail opportunityDetail;
    
    @ApiModelProperty(value = "企业信息")
    private GenAgentEnterprise enterprise;
} 
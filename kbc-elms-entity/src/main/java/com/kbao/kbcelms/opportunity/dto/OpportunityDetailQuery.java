package com.kbao.kbcelms.opportunity.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 机会详情查询参数类
 * 用于封装selectOpportunityDetails方法的查询条件
 */
@Data
@ApiModel(description = "机会详情查询参数")
public class OpportunityDetailQuery {
    /** 租户ID（必传） */
    private String tenantId;

    /** 代理人姓名（模糊匹配t_opportunity.agent_name） */
    private String agentName;

    /** 公司编码（精确匹配t_opportunity.company_code） */
    private String companyCode;

    /** 机会名称（模糊匹配t_opportunity.opportunity_name） */
    private String opportunityName;

    /** 行业编码（精确匹配t_opportunity.industry_code） */
    private String industryCode;

    /** 机会类型（精确匹配t_opportunity.opportunity_type） */
    private String opportunityType;

    /** 机会ID（匹配t_opportunity.id） */
    private String opportunityId;

    /**
     * 机会编码
     */
    private String bizCode;

    /** 机会ID列表（批量匹配t_opportunity.id，IN查询） */
    private List<String> opportunityIds;

    /** 企业名称（模糊匹配t_gen_agent_enterprise.name） */
    private String enterpriseName;

    /** 社会信用代码（精确匹配t_gen_agent_enterprise.credit_code） */
    private String creditCode;

    /** 企业类型（精确匹配t_gen_agent_enterprise.dt_type） */
    private String dtType;

    /** 提交时间-开始（匹配t_opportunity_detail.submit_time >= 该值） */
    private Date submitBeginTime;

    /** 提交时间-结束（匹配t_opportunity_detail.submit_time <= 该值） */
    private Date submitEndTime;

    /** 组队时间-开始（匹配t_opportunity_detail.team_time >= 该值） */
    private Date teamBeginTime;

    /** 组队时间-结束（匹配t_opportunity_detail.team_time <= 该值） */
    private Date teamEndTime;

    /** 日志时间-开始（匹配t_opportunity_detail.log_time >= 该值） */
    private Date logBeginTime;

    /** 日志时间-结束（匹配t_opportunity_detail.log_time <= 该值） */
    private Date logEndTime;

    /** 总结时间-开始（匹配t_opportunity_detail.summary_time >= 该值） */
    private Date summaryBeginTime;

    /** * 总结时间-结束（匹配t_opportunity_detail.summary_time <= 该值） */
    private Date summaryEndTime;

    /** 机会状态列表（批量匹配t_opportunity.status，IN查询） */
    private List<Integer> status;

    /**
     * 机会是否已验证：0-否；1-是。
     */
    private String isVerified;

    /**
     * 机会状态
     */
    private Integer opportunityStatus;

    /**
     * 机会步骤
     */
    private String processStep;

    /** 用户昵称（模糊匹配t_user.nick_name） */
    private String name;


    /**
     * 机构编码
     */
    private String legalCode;
}
package com.kbao.kbcelms.opportunity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;import lombok.Data;import java.util.Date;
@Data
public class OpportunityListResVo {
    /**
     * 机会ID
     */
    private Integer opportunityId;

    /**
     * 机会名称
     */
    private String opportunityName;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 机会类型
     */
    private String opportunityType;

    /**
     * 状态
     */
    private Integer status;


    private String insureTypes;

    /**
     * 提交时间
     */
    private Date submitTime;

    private String hasInsureInfo;

    /**
     * 企业名称
     */
    private String name;

    private String isNeedConfirm;

    private String lockStatus;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lockEndTime;
}

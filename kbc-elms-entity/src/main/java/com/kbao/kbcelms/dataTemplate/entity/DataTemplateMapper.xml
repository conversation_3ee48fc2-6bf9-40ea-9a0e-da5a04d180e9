<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcelms.dataTemplate.entity.DataTemplate">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="templateName" jdbcType="VARCHAR"  column="template_name" />  
        <result property="bizCode" jdbcType="VARCHAR"  column="biz_code" />  
        <result property="type" jdbcType="VARCHAR"  column="type" />  
        <result property="status" jdbcType="CHAR"  column="status" />  
        <result property="remark" jdbcType="VARCHAR"  column="remark" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="isDeleted" jdbcType="TINYINT"  column="is_deleted" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		template_name,  
		biz_code,  
		type,  
		status,  
		remark,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		is_deleted,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.template_name, 
		t.biz_code, 
		t.type, 
		t.status, 
		t.remark, 
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.is_deleted, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
		is_deleted = 0
	    <if test="templateName != null and templateName != ''">
	   		and t.template_name = #{templateName,jdbcType=VARCHAR}
	    </if>
	    <if test="bizCode != null and bizCode != ''">
	   		and t.biz_code = #{bizCode,jdbcType=VARCHAR}
	    </if>
	    <if test="type != null and type != ''">
	   		and t.type = #{type,jdbcType=VARCHAR}
	    </if>
	    <if test="status != null and status != ''">
	   		and t.status = #{status,jdbcType=CHAR}
	    </if>
	    <if test="remark != null and remark != ''">
	   		and t.remark = #{remark,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_data_template t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_data_template
		where  id = #{id,jdbcType=INTEGER}
	</select>

	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_data_template set is_deleted = 1,
		                       update_time = now(),
		                       update_id = #{updateId,jdbcType=VARCHAR}
		                       where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcelms.dataTemplate.entity.DataTemplate" keyProperty="id" useGeneratedKeys="true">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_data_template(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{templateName,jdbcType=VARCHAR}, 
                 
                #{bizCode,jdbcType=VARCHAR}, 
                 
                #{type,jdbcType=VARCHAR}, 
                 
                #{status,jdbcType=CHAR}, 
                 
                #{remark,jdbcType=VARCHAR}, 
                 
                now(),
                 
                #{createId,jdbcType=VARCHAR},

				now(),

				#{createId,jdbcType=VARCHAR},

				'0',
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.dataTemplate.entity.DataTemplate">
		update t_data_template
		<set>
	        <if test="templateName != null ">  
	        	template_name = #{templateName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="bizCode != null ">  
	        	biz_code = #{bizCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="type != null ">  
	        	type = #{type,jdbcType=VARCHAR},  
	        </if>  
	        <if test="status != null ">  
	        	status = #{status,jdbcType=CHAR},  
	        </if>  
	        <if test="remark != null ">  
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>
			update_id = #{updateId,jdbcType=VARCHAR},
			update_time = now()
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
	<select id="isExistTemplateCode" resultType="int">
		select count(1)
		from t_data_template
		where is_deleted = 0 and biz_code = #{bizCode,jdbcType=VARCHAR}
		<if test="id != null">
			and id != #{id,jdbcType=INTEGER}
		</if>
	</select>

	<!--自动建表相关sql-->
	<select id="isExistTable" resultType="int" useCache="false">
		select count(1) from information_schema.tables
		where table_name = #{tableName,jdbcType=VARCHAR}
	</select>

	<select id="isExistData" resultType="int">
		select count(1) from ${tableName}
	</select>

	<update id="ddlDropTable">
		drop table if exists ${tableName}
	</update>

	<update id="ddlCreateTable">
		create table ${tableName} (
		id INT primary key auto_increment not null comment '主键',
		<foreach collection="fieldList" item="item">
			${item.fieldCode} ${item.fieldTypeStr} comment #{item.fieldName},
		</foreach>
        create_id         VARCHAR(50) comment '创建人 当前用户ID',
        create_time       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_id         VARCHAR(50) comment '更新人 默认为当前时间',
        update_time       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        is_deleted        TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除',
		tenant_id VARCHAR(10) not null default 'T0001' comment '租户ID'
		<foreach collection="indexFields" item="field" open="," separator=",">
			index idx_field_${field}(${field})
		</foreach>
		) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment #{tableComment}
	</update>

	<update id="ddlChangeTable">
		alter table ${tableName}
		    <if test="addFields != null and addFields.size() > 0">
				<foreach collection="addFields" item="field">
					add column ${field.fieldCode} ${field.fieldTypeStr} comment #{field.fieldName},
				</foreach>
			</if>
			<if test="addIndexFields != null and addIndexFields.size() > 0">
				<foreach collection="addIndexFields" item="field">
					add index idx_field_${field}(${field})
				</foreach>
			</if>
			<if test="delIndexFields != null and delIndexFields.size() > 0">
				<foreach collection="delIndexFields" item="field">
					drop index idx_field_${field}
				</foreach>
			</if>
	</update>

	<select id="getDataList" resultType="map">
		select * from ${tableName}
		<where>
			<if test="searchParamList != null">
				<foreach collection="searchParamList" item="param">
					<if test='param.fieldName != null and param.fieldName != ""'>
						and ${param.fieldName} ${param.operator} #{param.value}
					</if>
				</foreach>
			</if>
		</where>
	</select>

	<select id="selectByBizCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from t_data_template
        where is_deleted = 0 and biz_code = #{bizCode,jdbcType=VARCHAR}
    </select>
</mapper>

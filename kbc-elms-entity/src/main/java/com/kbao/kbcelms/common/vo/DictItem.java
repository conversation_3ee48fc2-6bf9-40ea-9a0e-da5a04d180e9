package com.kbao.kbcelms.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 普通字典项
 * <AUTHOR>
 */
@ApiModel(value = "DictItem", description = "普通字典项")
public class DictItem implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "字典编码")
    private String code;
    
    @ApiModelProperty(value = "字典值")
    private String value;
    
    public DictItem() {}
    
    public DictItem(String code, String value) {
        this.code = code;
        this.value = value;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    @Override
    public String toString() {
        return "DictItem{" +
                "code='" + code + '\'' +
                ", value='" + value + '\'' +
                '}';
    }
}

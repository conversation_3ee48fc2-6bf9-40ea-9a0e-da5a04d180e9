package com.kbao.kbcelms.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 级联字典项
 * <AUTHOR>
 */
@ApiModel(value = "CascadeDictItem", description = "级联字典项")
public class CascadeDictItem implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "字典编码")
    private String code;
    
    @ApiModelProperty(value = "字典值")
    private String value;
    
    @ApiModelProperty(value = "子级字典")
    private List<CascadeDictItem> children;
    
    public CascadeDictItem() {}
    
    public CascadeDictItem(String code, String value) {
        this.code = code;
        this.value = value;
    }
    
    public CascadeDictItem(String code, String value, List<CascadeDictItem> children) {
        this.code = code;
        this.value = value;
        this.children = children;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public List<CascadeDictItem> getChildren() {
        return children;
    }
    
    public void setChildren(List<CascadeDictItem> children) {
        this.children = children;
    }
    
    @Override
    public String toString() {
        return "CascadeDictItem{" +
                "code='" + code + '\'' +
                ", value='" + value + '\'' +
                ", children=" + children +
                '}';
    }
}

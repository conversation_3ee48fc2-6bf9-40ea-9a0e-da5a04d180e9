package com.kbao.kbcelms.enterprise.type.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 企业规模映射VO
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@ApiModel(value = "EnterpriseScaleMapsVO", description = "企业规模映射VO")
public class EnterpriseScaleMapsVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "人员规模映射 (code -> value)")
    private Map<String, String> employeeScales;
    
    @ApiModelProperty(value = "营收规模映射 (code -> value)")
    private Map<String, String> revenueScales;
    
    public EnterpriseScaleMapsVO() {}
    
    public EnterpriseScaleMapsVO(Map<String, String> employeeScales, Map<String, String> revenueScales) {
        this.employeeScales = employeeScales;
        this.revenueScales = revenueScales;
    }
}

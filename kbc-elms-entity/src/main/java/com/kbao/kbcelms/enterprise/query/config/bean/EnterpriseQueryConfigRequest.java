package com.kbao.kbcelms.enterprise.query.config.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 企业查询配置请求参数
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ApiModel(value = "EnterpriseQueryConfigRequest", description = "企业查询配置请求对象")
public class EnterpriseQueryConfigRequest {

    @ApiModelProperty(value = "配置类型：GENERAL(通用配置) / PERSONAL(个人配置)", required = true)
    @NotBlank(message = "配置类型不能为空")
    private String configType;

    @ApiModelProperty(value = "顾问工号（个人配置时必填）")
    private String agentCode;

    @ApiModelProperty(value = "顾问姓名（个人配置时必填）")
    private String agentName;

    @ApiModelProperty(value = "每日查询次数限制", required = true)
    @NotNull(message = "每日查询次数限制不能为空")
    @Min(value = 1, message = "每日查询次数限制不能小于1")
    private Integer dailyLimit;

    @ApiModelProperty(value = "每月查询次数限制", required = true)
    @NotNull(message = "每月查询次数限制不能为空")
    @Min(value = 1, message = "每月查询次数限制不能小于1")
    private Integer monthlyLimit;
}

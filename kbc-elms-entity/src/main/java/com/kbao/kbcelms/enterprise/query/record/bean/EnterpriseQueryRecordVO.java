package com.kbao.kbcelms.enterprise.query.record.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 企业查询记录返回VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ApiModel(value = "EnterpriseQueryRecordVO", description = "请求实体")
public class EnterpriseQueryRecordVO {

    @ApiModelProperty(value = "查询人姓名")
    private String agentName;

    @ApiModelProperty(value = "查询人工号")
    private String agentCode;

    @ApiModelProperty(value = "实际企业名称")
    private String enterpriseName;
}

package com.kbao.kbcelms.enterprise.type.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 企业规模枚举VO
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@ApiModel(value = "EnterpriseScaleEnumVO", description = "企业规模枚举VO")
public class EnterpriseScaleEnumVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "人员规模枚举列表")
    private List<ScaleEnumItem> employeeScales;

    @ApiModelProperty(value = "营收规模枚举列表")
    private List<ScaleEnumItem> revenueScales;

    public EnterpriseScaleEnumVO() {}

    public EnterpriseScaleEnumVO(List<ScaleEnumItem> employeeScales, List<ScaleEnumItem> revenueScales) {
        this.employeeScales = employeeScales;
        this.revenueScales = revenueScales;
    }

    /**
     * 获取人员规模映射
     * @return 人员规模映射 (code -> value)
     */
    public Map<String, String> getEmployeeScalesMap() {
        if (employeeScales == null) {
            return java.util.Collections.emptyMap();
        }
        return employeeScales.stream().collect(Collectors.toMap(ScaleEnumItem::getCode, ScaleEnumItem::getValue));
    }

    /**
     * 获取营收规模映射
     * @return 营收规模映射 (code -> value)
     */
    public Map<String, String> getRevenueScalesMap() {
        if (revenueScales == null) {
            return java.util.Collections.emptyMap();
        }
        return revenueScales.stream().collect(Collectors.toMap(ScaleEnumItem::getCode, ScaleEnumItem::getValue));
    }
}

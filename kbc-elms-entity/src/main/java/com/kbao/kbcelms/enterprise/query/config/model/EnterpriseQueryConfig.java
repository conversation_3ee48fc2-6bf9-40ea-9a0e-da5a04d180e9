package com.kbao.kbcelms.enterprise.query.config.model;

import io.swagger.annotations.ApiModelProperty;import org.springframework.data.annotation.Id;import org.springframework.data.mongodb.core.index.Indexed;import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import lombok.Data;
import lombok.EqualsAndHashCode;import java.io.Serializable;

/**
 * 企业查询配置MongoDB实体
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Document(collection = "enterprise_query_config")
public class EnterpriseQueryConfig implements Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * MongoDB主键
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 配置类型：GENERAL(通用配置) / PERSONAL(个人配置)
     */
    private String configType;

    /**
     * 顾问工号（个人配置时有值，通用配置时为null）
     */
    @Indexed
    private String agentCode;

    /**
     * 顾问姓名（个人配置时有值，通用配置时为null）
     */
    private String agentName;

    /**
     * 每日查询次数限制
     */
    private Integer dailyLimit;

    /**
     * 每月查询次数限制
     */
    private Integer monthlyLimit;

    /**
     * 创建时间
     */
    private java.util.Date createTime;

    /**
     * 创建人
     */
    private String createId;

    /**
     * 修改时间
     */
    private java.util.Date updateTime;

    /**
     * 修改人
     */
    private String updateId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 配置类型常量
     */
    public static class ConfigType {
        /** 通用配置 */
        public static final String GENERAL = "GENERAL";
        /** 个人配置 */
        public static final String PERSONAL = "PERSONAL";
    }
}

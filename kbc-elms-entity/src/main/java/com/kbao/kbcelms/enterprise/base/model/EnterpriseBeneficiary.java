package com.kbao.kbcelms.enterprise.base.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 最终受益人信息
 * 对应天眼查API：945-最终受益人
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@Document(collection = "enterprise_beneficiary")
@ApiModel(value = "EnterpriseBeneficiary", description = "最终受益人信息")
public class EnterpriseBeneficiary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * MongoDB主键
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 统一社会信用代码
     */
    @Indexed
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /**
     * 关联公司数
     */
    @ApiModelProperty(value = "关联公司数")
    private Integer toco;

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Integer total;

    /**
     * 法人列表（两层数组结构）
     * 第一层：股权路径组
     * 第二层：每个路径组内的具体信息（type、title、value等）
     */
    @ApiModelProperty(value = "法人列表（两层数组结构）")
    private List<List<ChainInfo>> chainList;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * logo
     */
    @ApiModelProperty(value = "logo")
    private String logo;

    /**
     * 人id
     */
    @ApiModelProperty(value = "人id")
    private Long beneficiaryId;

    /**
     * 类型（human/company）
     */
    @ApiModelProperty(value = "类型（human/company）")
    private String type;

    /**
     * 占比
     */
    @ApiModelProperty(value = "占比")
    private String percent;

    /**
     * 无用字段
     */
    @ApiModelProperty(value = "无用字段")
    private String hcgid;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long cid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 租户ID
     */
    @Indexed
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 法人链信息内部类
     */
    @Data
    public static class ChainInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 投资类型（1-直接，2-间接）
         */
        @ApiModelProperty(value = "投资类型（1-直接，2-间接）")
        private Integer investType;

        /**
         * 人id
         */
        @ApiModelProperty(value = "人id")
        private Long id;

        /**
         * 类型（human/company/title/percent）
         */
        @ApiModelProperty(value = "类型（human/company/title/percent）")
        private String type;

        /**
         * type说明
         */
        @ApiModelProperty(value = "type说明")
        private String title;

        /**
         * 法人名称或公司名称
         */
        @ApiModelProperty(value = "法人名称或公司名称")
        private String value;

        /**
         * 公司id
         */
        @ApiModelProperty(value = "公司id")
        private Long cid;

        /**
         * 信息
         */
        @ApiModelProperty(value = "信息")
        private String info;
    }
}

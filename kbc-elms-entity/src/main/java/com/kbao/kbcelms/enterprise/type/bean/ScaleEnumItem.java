package com.kbao.kbcelms.enterprise.type.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 规模枚举项
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@ApiModel(value = "ScaleEnumItem", description = "规模枚举项")
public class ScaleEnumItem implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "企业类型编码")
    private String code;
    
    @ApiModelProperty(value = "规模范围文本")
    private String value;
    
    public ScaleEnumItem() {}
    
    public ScaleEnumItem(String code, String value) {
        this.code = code;
        this.value = value;
    }
}

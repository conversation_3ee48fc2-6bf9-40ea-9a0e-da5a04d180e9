package com.kbao.kbcelms.enterprise.query.config.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 企业查询配置返回VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ApiModel(value = "EnterpriseQueryConfigVO", description = "企业查询配置返回对象")
public class EnterpriseQueryConfigVO {

    @ApiModelProperty(value = "配置ID")
    private String id;

    @ApiModelProperty(value = "配置类型：GENERAL(通用配置) / PERSONAL(个人配置)")
    private String configType;

    @ApiModelProperty(value = "顾问工号")
    private String agentCode;

    @ApiModelProperty(value = "顾问姓名")
    private String agentName;

    @ApiModelProperty(value = "每日查询次数限制")
    private Integer dailyLimit;

    @ApiModelProperty(value = "每月查询次数限制")
    private Integer monthlyLimit;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}

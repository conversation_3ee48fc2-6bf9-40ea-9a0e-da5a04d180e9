package com.kbao.kbcelms.enterprise.type.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业类型枚举VO
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@ApiModel(value = "EnterpriseTypeEnumVO", description = "企业类型枚举VO")
public class EnterpriseTypeEnumVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "企业类型名称")
    private String name;
    
    @ApiModelProperty(value = "企业类型编码")
    private String code;
    
    public EnterpriseTypeEnumVO() {}
    
    public EnterpriseTypeEnumVO(String name, String code) {
        this.name = name;
        this.code = code;
    }
}

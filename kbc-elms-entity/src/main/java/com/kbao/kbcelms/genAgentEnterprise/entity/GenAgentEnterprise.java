package com.kbao.kbcelms.genAgentEnterprise.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Description 顾问企业表实体
 * @Date 2025-07-31
 */
@Data
@ApiModel(description = "顾问企业实体")
public class GenAgentEnterprise implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应表中id
     * 主键
     */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    /**
     * 对应表中agent_code
     * 顾问编码
     */
    @ApiModelProperty(value = "顾问编码", example = "AG001")
    private String agentCode;

    private String agentName;

    /**
     * 对应表中legal_code
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码", example = "LEGAL001")
    private String legalCode;

    private String legalName;

    /**
     * 对应表中trading_center_code
     * 交易中心编码
     */
    @ApiModelProperty(value = "交易中心编码", example = "TC001")
    private String tradingCenterCode;

    private String tradingCenterName;

    /**
     * 对应表中name
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称", example = "某保险经纪有限公司")
    private String name;

    /**
     * 对应表中credit_code
     * 社会信用代码
     */
    @ApiModelProperty(value = "社会信用代码", example = "91110000123456789X")
    private String creditCode;

    /**
     * 对应表中dt_type
     * 企业类型：A-央企，B-上市公司，C-大型企业，D-中小企业
     */
    @ApiModelProperty(value = "企业类型", example = "C", notes = "A-央企，B-上市公司，C-大型企业，D-中小企业")
    private String dtType;

    /**
     * 对应表中enterprise_scale
     * 企业规模
     */
    @ApiModelProperty(value = "企业规模", example = "大型")
    private String enterpriseScale;

    /**
     * 对应表中staff_scale
     * 人员规模
     */
    @ApiModelProperty(value = "人员规模", example = "存儲編碼")
    private String staffScale;

    /**
     * 对应表中city
     * 所在城市
     */
    @ApiModelProperty(value = "所在城市", example = "北京市")
    private String city;

    /**
     * 对应表中district_code
     * 行政区划代码
     */
    @ApiModelProperty(value = "行政区划代码", example = "110000")
    private String districtCode;

    /**
     * 对应表中annual_income
     * 年收入规模
     */
    @ApiModelProperty(value = "年收入规模", example = "存儲編碼")
    private String annualIncome;

    /**
     * 对应表中category_code
     * 行业分类代码
     */
    @ApiModelProperty(value = "行业分类代码", example = "J66")
    private String categoryCode;

    /**
     * 对应表中category_name
     * 行业分类名称
     */
    @ApiModelProperty(value = "行业分类名称", example = "保险业")
    private String categoryName;

    /**
     * 对应表中enterprise_contacter
     * 企业联系人
     */
    @ApiModelProperty(value = "企业联系人", example = "张经理")
    private String enterpriseContacter;

    /**
     * 对应表中contacter_phone
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话", example = "13800138000")
    private String contacterPhone;

    /**
     * 对应表中remark
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息", example = "重点客户，需要重点关注")
    private String remark;

    /**
     * 对应表中is_verified
     * 是否验真：0-未验真，1-已验真
     */
    @ApiModelProperty(value = "是否验真", example = "1", notes = "0-未验真，1-已验真")
    private String isVerified;

    /**
     * 对应表中verify_time
     * 验真时间
     */
    @ApiModelProperty(value = "验真时间", example = "2025-01-15 16:30:00")
    private Date verifyTime;

    private String queryRecordId;

    /**
     * 对应表中create_time
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2025-01-15 16:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 对应表中create_id
     * 创建人
     */
    @ApiModelProperty(value = "创建人ID", example = "USER001")
    private String createId;

    /**
     * 对应表中update_time
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2025-01-15 16:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 对应表中update_id
     * 更新人
     */
    @ApiModelProperty(value = "更新人ID", example = "USER001")
    private String updateId;

    /**
     * 对应表中is_deleted
     * 是否删除 0-未删除 1-已删除
     */
    @ApiModelProperty(value = "是否删除", example = "0", notes = "0-未删除，1-已删除")
    private Integer isDeleted;

    /**
     * 对应表中tenant_id
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", example = "TENANT001")
    private String tenantId;

    @ApiModelProperty(value = "是否验真超过一年", example = "0")
    private String isVerifyOverOneYear;

    private String minCategoryName;
}

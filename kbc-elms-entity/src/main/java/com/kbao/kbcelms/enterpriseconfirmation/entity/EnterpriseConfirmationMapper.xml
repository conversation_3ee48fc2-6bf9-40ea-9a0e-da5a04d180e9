<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.enterpriseconfirmation.dao.EnterpriseConfirmationMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.enterpriseconfirmation.entity.EnterpriseConfirmation">
        <id property="id" jdbcType="INTEGER" column="id"/>
        <result property="legalCode" jdbcType="VARCHAR" column="legal_code"/>
        <result property="legalName" jdbcType="VARCHAR" column="legal_name"/>
        <result property="creditCode" jdbcType="VARCHAR" column="credit_code"/>
        <result property="enterpriseName" jdbcType="VARCHAR" column="enterprise_name"/>
        <result property="districtCode" jdbcType="VARCHAR" column="district_code"/>
        <result property="city" jdbcType="VARCHAR" column="city"/>
        <result property="staffScale" jdbcType="VARCHAR" column="staff_scale"/>
        <result property="annualIncome" jdbcType="VARCHAR" column="annual_income"/>
        <result property="isDuplicate" jdbcType="TINYINT" column="is_duplicate"/>
        <result property="legalEnterpriseNum" jdbcType="INTEGER" column="legal_enterprise_num"/>
        <result property="isProcessed" jdbcType="TINYINT" column="is_processed"/>
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
        <result property="tenantId" jdbcType="VARCHAR" column="tenant_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, legal_code, legal_name, credit_code, enterprise_name, district_code, city,
        staff_scale, annual_income, is_duplicate, legal_enterprise_num, is_processed, create_time, tenant_id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_enterprise_confirmation
        where id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from t_enterprise_confirmation
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.kbao.kbcelms.enterpriseconfirmation.entity.EnterpriseConfirmation" useGeneratedKeys="true" keyProperty="id">
        insert into t_enterprise_confirmation (
            legal_code, legal_name, credit_code, enterprise_name, district_code, city,
            staff_scale, annual_income, is_duplicate, legal_enterprise_num, is_processed, create_time, tenant_id
        ) values (
            #{legalCode}, #{legalName}, #{creditCode}, #{enterpriseName}, #{districtCode}, #{city},
            #{staffScale}, #{annualIncome}, #{isDuplicate}, #{legalEnterpriseNum}, #{isProcessed}, #{createTime}, #{tenantId}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.enterpriseconfirmation.entity.EnterpriseConfirmation">
        update t_enterprise_confirmation
        set legal_code = #{legalCode},
            legal_name = #{legalName},
            credit_code = #{creditCode},
            enterprise_name = #{enterpriseName},
            district_code = #{districtCode},
            city = #{city},
            staff_scale = #{staffScale},
            annual_income = #{annualIncome},
            is_duplicate = #{isDuplicate},
            legal_enterprise_num = #{legalEnterpriseNum},
            is_processed = #{isProcessed},
            create_time = #{createTime},
            tenant_id = #{tenantId}
        where id = #{id}
    </update>

    <!-- 分页查询企业确权列表（仅查询重复记录） -->
    <select id="getConfirmationList" parameterType="com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationSearchVo" 
            resultType="com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationListVo">
        SELECT 
            id,
            legal_code AS legalCode,
            legal_name AS legalName,
            enterprise_name AS enterpriseName,
            credit_code AS creditCode,
            city,
            staff_scale AS staffScale,
            annual_income AS annualIncome,
            is_processed AS processStatus,
            create_time AS createTime
        FROM t_enterprise_confirmation
        WHERE is_duplicate = 1
        <if test="orgList != null and orgList.size > 0">
            AND legal_code IN
            <foreach collection="orgList" item="orgCode" open="(" separator="," close=")">
                #{orgCode}
            </foreach>
        </if>
        <if test="enterpriseName != null and enterpriseName != ''">
            AND enterprise_name LIKE CONCAT('%', #{enterpriseName}, '%')
        </if>
        <if test="processStatus != null">
            AND is_processed = #{processStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据统一信用代码查询记录 -->
    <select id="getByCreditCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM t_enterprise_confirmation
        WHERE credit_code = #{creditCode}
    </select>

    <!-- 获取重复企业创建人信息列表 -->
    <select id="getDuplicateCreators" parameterType="java.lang.String"
            resultType="com.kbao.kbcelms.enterpriseconfirmation.bean.DuplicateEnterpriseCreatorVo">
        SELECT
            age.create_id AS creatorName,
            '待补充' AS legalName,
            age.create_time AS createTime,
            0 AS opportunityCount
        FROM t_gen_agent_enterprise age
        WHERE age.credit_code = #{creditCode}
        ORDER BY age.create_time DESC
    </select>

    <!-- 批量更新重复标记 -->
    <update id="batchUpdateDuplicateFlag">
        UPDATE t_enterprise_confirmation
        SET is_duplicate = #{isDuplicate},
            is_processed = #{isProcessed}
        WHERE credit_code = #{creditCode} and legal_enterprise_num > 0
    </update>

    <update id="resetDuplicateFlag">
        UPDATE t_enterprise_confirmation t1,
            (select sum(legal_enterprise_num) enterpriseNum from t_enterprise_confirmation where credit_code = #{creditCode}) t2
        SET t1.is_duplicate = 0
        WHERE t1.credit_code = #{creditCode}
          and (t1.legal_enterprise_num = 0 or t2.enterpriseNum <![CDATA[ < ]]> 2)
          and not exists(select 1 from t_confirmation_process_record where legal_code = t1.legal_code and credit_code = t1.credit_code)
    </update>

    <!-- 检查同机构是否已存在确权记录 -->
    <select id="getDuplicateNum" resultType="com.kbao.kbcelms.enterpriseconfirmation.bean.DuplicateInfoVo">
        SELECT sum(legal_enterprise_num) enterpriseNum, count(if(legal_code = #{legalCode}, 1, null)) duplicateLegalNum,
            sum(if(legal_code = #{legalCode}, legal_enterprise_num, 0)) legalEnterpriseNum
        FROM t_enterprise_confirmation
        WHERE credit_code = #{creditCode}
    </select>

    <update id="reduceLegalEnterpriseNum">
        update t_enterprise_confirmation set legal_enterprise_num = if(legal_enterprise_num - 1 > 0, legal_enterprise_num - 1, 0)
        where credit_code = #{creditCode} and legal_code = #{legalCode}
    </update>

    <update id="addLegalEnterpriseNum">
        update t_enterprise_confirmation set legal_enterprise_num = legal_enterprise_num + 1
        where credit_code = #{creditCode} and legal_code = #{legalCode}
    </update>
</mapper>

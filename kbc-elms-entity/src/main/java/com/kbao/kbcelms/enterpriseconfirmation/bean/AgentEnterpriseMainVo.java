package com.kbao.kbcelms.enterpriseconfirmation.bean;

import io.swagger.annotations.ApiModelProperty;import lombok.Data;import java.util.Date;
@Data
public class AgentEnterpriseMainVo {
    private String creator;
    private Date createTime;
    private String areaCenterName;
    private String tradingCenterName;
    private String bpGroupName;
    private String name;
    private String creditCode;

    private String dtType;
    private String enterpriseScale;
    private String staffScale;
    private String city;
    private String districtCode;
    private String annualIncome;
    private String categoryCode;
    private String categoryName;
    private String enterpriseContacter;
    private String contacterPhone;
    private String remark;
    private String isVerified;
    private Integer isDeleted;
}

package com.kbao.kbcelms.enterpriseconfirmation.bean;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.enterpriseconfirmation.entity.ConfirmationProcessRecord;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 企业确权详情VO
 * @Date 2025-08-20
 */
@Data
@ApiModel(description = "企业确权详情VO")
public class EnterpriseConfirmationDetailVo {

    private Integer isProcessed;
    /**
     * 企业基本信息
     */
    @ApiModelProperty(value = "企业基本信息")
    private EnterpriseBaseInfoVo enterpriseBasicInfo;

    /**
     * 创建企业时的录入信息
     */
    @ApiModelProperty(value = "创建企业时的录入信息")
    private AgentEnterpriseMainVo genAgentEnterprise;

    /**
     * 重复企业创建人信息列表
     */
    @ApiModelProperty(value = "重复企业创建人信息列表")
    private List<AgentEnterpriseOtherVo> duplicateCreators;

    /**
     * 重复企业处理记录列表
     */
    @ApiModelProperty(value = "重复企业处理记录列表")
    private List<ConfirmationProcessRecord> processRecords;
}

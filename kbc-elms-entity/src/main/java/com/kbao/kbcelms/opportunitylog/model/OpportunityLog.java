package com.kbao.kbcelms.opportunitylog.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

/** 
 * 机会日志表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document
public class OpportunityLog
{
  /**
   * 主键
   */
  @Id
  protected String id;
  /**
   * 机会id
   */
  @Indexed
  protected String opportunityId;
  /**
   * 沟通事项
   */
  protected String logDesc;
  /**
   * 记录人名称
   */
  protected String recorderName;
  /**
   * 沟通日期
   */
  @Indexed
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  protected Date meetingDate;
  /**
   * 会议内容
   */
  protected String meetingContent;
  /**
   * 会议图集
   */
  protected List<String> meetingImageUrl;
  /**
   * 客户需求
   */
  protected String customerDemand;
  /**
   * 需求图片
   */
  protected List<String> demandImageUrl;
  /**
   * 创建日期
   */
  protected Date createTime;
  /**
   * 创建人
   */
  protected String creatorId;
  /**
   * 更新人
   */
  protected String updaterId;
  /**
   * 更新时间
   */
  protected Date updateTime;
  /**
   * 租户Id
   */
  @Indexed
  protected String tenantId;
  
  public void setLogDesc(String logDesc) {
    this.logDesc = logDesc != null ? logDesc.trim() : null;
  }

  public void setRecorderName(String recorderName) {
    this.recorderName = recorderName != null ? recorderName.trim() : null;
  }

  public void setMeetingContent(String meetingContent) {
    this.meetingContent = meetingContent != null ? meetingContent.trim() : null;
  }

  public void setCustomerDemand(String customerDemand) {
    this.customerDemand = customerDemand != null ? customerDemand.trim() : null;
  }
}
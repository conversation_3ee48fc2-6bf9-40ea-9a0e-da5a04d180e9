package com.kbao.kbcelms.riskconfig.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 风险配置模板
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@Document(collection = "risk_config_templates")
@ApiModel(value = "RiskConfigTemplate", description = "风险配置模板")
public class RiskConfigTemplate implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    @NotBlank(message = "模板名称不能为空")
    @Indexed
    @ApiModelProperty(value = "模板名称", required = true)
    private String templateName;

    @NotBlank(message = "模板类型不能为空")
    @ApiModelProperty(value = "模板类型（matrix-风险矩阵，tools-防控工具，complete-完整配置）", required = true)
    private String templateType;

    @ApiModelProperty(value = "模板描述")
    private String description;

    @NotNull(message = "配置数据不能为空")
    @ApiModelProperty(value = "模板配置数据", required = true)
    private Map<String, Object> configData;

    @ApiModelProperty(value = "适用行业列表")
    private List<String> applicableIndustries;

    @ApiModelProperty(value = "是否公开模板")
    private Boolean isPublic = false;

    @ApiModelProperty(value = "模板状态（active-启用，inactive-禁用）")
    private String status = "active";

    @Indexed
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
}

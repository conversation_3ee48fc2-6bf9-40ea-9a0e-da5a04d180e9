package com.kbao.kbcelms.riskconfig.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 复制模板请求对象
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@ApiModel(value = "CopyTemplateRequest", description = "复制模板请求对象")
public class CopyTemplateRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "模板ID不能为空")
    @ApiModelProperty(value = "模板ID", required = true)
    private String templateId;

    @NotBlank(message = "目标行业编码不能为空")
    @ApiModelProperty(value = "目标行业编码", required = true)
    private String targetIndustryCode;

    @NotBlank(message = "目标行业名称不能为空")
    @ApiModelProperty(value = "目标行业名称", required = true)
    private String targetIndustryName;

    @ApiModelProperty(value = "目标行业层级")
    private Integer targetIndustryLevel = 1;

    @ApiModelProperty(value = "风险矩阵说明")
    private String matrixDesc;

    @ApiModelProperty(value = "整体风险等级")
    private String riskLevel = "medium";

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
}

package com.kbao.kbcelms.riskconfig.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 动态列定义
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@ApiModel(value = "DynamicColumnDef", description = "动态列定义")
public class DynamicColumnDef implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "列标识不能为空")
    @ApiModelProperty(value = "列标识（字段名）", required = true)
    private String key;

    @NotBlank(message = "列名称不能为空")
    @ApiModelProperty(value = "列显示名称", required = true)
    private String label;

    @ApiModelProperty(value = "字段类型（input、select、number、textarea、date、switch）")
    private String type = "input";

    @ApiModelProperty(value = "列宽度（像素）")
    private Integer width = 120;

    @ApiModelProperty(value = "列排序（数字越小越靠前）")
    private Integer order = 0;

    @ApiModelProperty(value = "是否必填")
    private Boolean required = false;

    @ApiModelProperty(value = "是否默认列（默认列不可删除）")
    private Boolean isDefault = false;

    @ApiModelProperty(value = "输入占位符")
    private String placeholder;

    @ApiModelProperty(value = "最大长度限制")
    private Integer maxLength;

    @ApiModelProperty(value = "最小值（数字类型）")
    private Double minValue;

    @ApiModelProperty(value = "最大值（数字类型）")
    private Double maxValue;

    @ApiModelProperty(value = "选项配置（select类型的选项列表）")
    private List<ColumnOption> options;

    @ApiModelProperty(value = "验证规则配置")
    private Map<String, Object> validation;

    @ApiModelProperty(value = "扩展配置（其他字段类型的特殊配置）")
    private Map<String, Object> extraConfig;
}

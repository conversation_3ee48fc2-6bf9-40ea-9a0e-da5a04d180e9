package com.kbao.kbcelms.riskconfig.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 风险矩阵配置
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@ApiModel(value = "RiskMatrixConfig", description = "风险矩阵配置")
public class RiskMatrixConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @Valid
    @ApiModelProperty(value = "列定义配置")
    private List<DynamicColumnDef> columnDefs;

    @ApiModelProperty(value = "行数据")
    private List<Map<String, Object>> rowData;
}

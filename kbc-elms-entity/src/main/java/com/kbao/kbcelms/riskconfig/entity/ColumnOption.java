package com.kbao.kbcelms.riskconfig.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 列选项配置
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@ApiModel(value = "ColumnOption", description = "列选项配置")
public class ColumnOption implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "选项标签不能为空")
    @ApiModelProperty(value = "选项显示标签", required = true)
    private String label;

    @NotBlank(message = "选项值不能为空")
    @ApiModelProperty(value = "选项值", required = true)
    private String value;

    @ApiModelProperty(value = "是否禁用")
    private Boolean disabled = false;

    @ApiModelProperty(value = "选项描述")
    private String description;

    @ApiModelProperty(value = "CSS样式类")
    private String cssClass;

    @ApiModelProperty(value = "排序")
    private Integer order = 0;
}

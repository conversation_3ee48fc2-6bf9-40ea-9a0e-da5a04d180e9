package com.kbao.kbcelms.riskconfig.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 行业风险配置实体
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@Document(collection = "industry_risk_configs")
@ApiModel(value = "IndustryRiskConfig", description = "行业风险配置")
public class IndustryRiskConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "一级行业编码")
    private String industryLevel1Code;

    @ApiModelProperty(value = "一级行业名称")
    private String industryLevel1Name;

    @ApiModelProperty(value = "二级行业编码")
    private String industryLevel2Code;

    @ApiModelProperty(value = "二级行业名称")
    private String industryLevel2Name;

    @ApiModelProperty(value = "风险矩阵说明描述")
    private String matrixDesc;

//    @ApiModelProperty(value = "整体风险等级（low-低、medium-中、high-高、critical-极高）")
//    private String riskLevel;

    @NotBlank(message = "配置版本不能为空")
    @ApiModelProperty(value = "配置版本号", required = true)
    private String configVersion;

    @ApiModelProperty(value = "状态（active-启用，inactive-禁用）")
    private String status = "active";

    @Indexed
    @ApiModelProperty(value = "租户ID（多租户支持）")
    private String tenantId;

    @Valid
    @ApiModelProperty(value = "风险矩阵配置")
    private RiskMatrixConfig matrixConfig;

    @ApiModelProperty(value = "富文本详细说明内容")
    private String richTextContent;

    @ApiModelProperty(value = "员福报告URL")
    private String url;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;

    @ApiModelProperty(value = "删除标记（false-未删除，true-已删除）")
    private Boolean deleted = false;
}

package com.kbao.kbcelms.riskconfig.constants;

/**
 * 风险配置常量
 * <AUTHOR>
 * @date 2025-08-11
 */
public class RiskConfigConstants {

    /**
     * 风险等级
     */
    public static class RiskLevel {
        /** 低风险 */
        public static final String LOW = "low";
        /** 中风险 */
        public static final String MEDIUM = "medium";
        /** 高风险 */
        public static final String HIGH = "high";
        /** 极高风险 */
        public static final String CRITICAL = "critical";
    }

    /**
     * 状态
     */
    public static class Status {
        /** 启用 */
        public static final String ACTIVE = "active";
        /** 禁用 */
        public static final String INACTIVE = "inactive";
    }

    /**
     * 字段类型
     */
    public static class FieldType {
        /** 文本输入 */
        public static final String INPUT = "input";
        /** 下拉选择 */
        public static final String SELECT = "select";
        /** 数字输入 */
        public static final String NUMBER = "number";
        /** 文本域 */
        public static final String TEXTAREA = "textarea";
        /** 日期选择 */
        public static final String DATE = "date";
        /** 开关 */
        public static final String SWITCH = "switch";
    }

    /**
     * 变更类型
     */
    public static class ChangeType {
        /** 创建 */
        public static final String CREATE = "CREATE";
        /** 更新 */
        public static final String UPDATE = "UPDATE";
        /** 删除 */
        public static final String DELETE = "DELETE";
    }

    /**
     * 模板类型
     */
    public static class TemplateType {
        /** 风险矩阵 */
        public static final String MATRIX = "matrix";
        /** 防控工具 */
        public static final String TOOLS = "tools";
        /** 完整配置 */
        public static final String COMPLETE = "complete";
    }

    /**
     * 行业层级
     */
    public static class IndustryLevel {
        /** 一级行业 */
        public static final int LEVEL_1 = 1;
        /** 二级行业 */
        public static final int LEVEL_2 = 2;
        /** 三级行业 */
        public static final int LEVEL_3 = 3;
    }

    /**
     * 防控效果
     */
    public static class ControlEffect {
        /** 高 */
        public static final String HIGH = "high";
        /** 中 */
        public static final String MEDIUM = "medium";
        /** 低 */
        public static final String LOW = "low";
    }

    /**
     * 实施成本
     */
    public static class ImplementationCost {
        /** 高 */
        public static final String HIGH = "high";
        /** 中 */
        public static final String MEDIUM = "medium";
        /** 低 */
        public static final String LOW = "low";
    }

    /**
     * 默认配置
     */
    public static class DefaultConfig {
        /** 默认租户ID */
        public static final String DEFAULT_TENANT_ID = "default";
        /** 默认配置版本 */
        public static final String DEFAULT_VERSION = "1.0";
        /** 默认列宽度 */
        public static final int DEFAULT_COLUMN_WIDTH = 120;
    }

    /**
     * 集合名称
     */
    public static class CollectionName {
        /** 行业风险配置集合 */
        public static final String INDUSTRY_RISK_CONFIGS = "industry_risk_configs";
        /** 风险配置历史集合 */
        public static final String RISK_CONFIG_HISTORIES = "risk_config_histories";
        /** 风险配置模板集合 */
        public static final String RISK_CONFIG_TEMPLATES = "risk_config_templates";
    }

    /**
     * 索引名称
     */
    public static class IndexName {
        /** 行业编码租户唯一索引 */
        public static final String UK_INDUSTRY_CODE_TENANT = "uk_industry_code_tenant";
        /** 文本搜索索引 */
        public static final String TEXT_SEARCH_INDEX = "text_search_index";
    }
}

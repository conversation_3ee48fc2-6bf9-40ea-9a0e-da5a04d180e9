package com.kbao.kbcelms.riskconfig.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 风险配置变更历史
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@Document(collection = "risk_config_histories")
@ApiModel(value = "RiskConfigHistory", description = "风险配置变更历史")
public class RiskConfigHistory implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    @NotBlank(message = "配置ID不能为空")
    @Indexed
    @ApiModelProperty(value = "关联的行业风险配置ID", required = true)
    private String configId;

    @NotBlank(message = "版本号不能为空")
    @ApiModelProperty(value = "版本号", required = true)
    private String version;

    @NotBlank(message = "变更类型不能为空")
    @ApiModelProperty(value = "变更类型（CREATE-创建，UPDATE-更新，DELETE-删除）", required = true)
    private String changeType;

    @NotNull(message = "变更内容不能为空")
    @ApiModelProperty(value = "变更内容（完整的配置数据快照）", required = true)
    private Map<String, Object> changeContent;

    @ApiModelProperty(value = "变更摘要说明")
    private String changeSummary;

    @ApiModelProperty(value = "操作人")
    private String createdBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "操作时间")
    private Date createdTime;
}

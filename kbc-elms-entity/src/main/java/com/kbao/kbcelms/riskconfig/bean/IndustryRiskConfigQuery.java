package com.kbao.kbcelms.riskconfig.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 行业风险配置查询条件
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@ApiModel(value = "IndustryRiskConfigQuery", description = "行业风险配置查询条件")
public class IndustryRiskConfigQuery implements Serializable {
    
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "一级行业编码")
    private String industryLevel1Code;

    @ApiModelProperty(value = "一级行业名称")
    private String industryLevel1Name;

    @ApiModelProperty(value = "二级行业编码")
    private String industryLevel2Code;

    @ApiModelProperty(value = "二级行业名称")
    private String industryLevel2Name;

    @ApiModelProperty(value = "风险等级")
    private String riskLevel;

    @ApiModelProperty(value = "风险等级列表")
    private List<String> riskLevels;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "关键词搜索（支持行业名称、矩阵描述、风险类型等）")
    private String keyword;

    @ApiModelProperty(value = "创建开始时间")
    private Date createdTimeStart;

    @ApiModelProperty(value = "创建结束时间")
    private Date createdTimeEnd;

    @ApiModelProperty(value = "更新开始时间")
    private Date updatedTimeStart;

    @ApiModelProperty(value = "更新结束时间")
    private Date updatedTimeEnd;

    @ApiModelProperty(value = "是否包含已删除数据")
    private Boolean includeDeleted = false;

    @ApiModelProperty(value = "排序字段")
    private String sortField = "createdTime";

    @ApiModelProperty(value = "排序方向（asc-升序，desc-降序）")
    private String sortDirection = "desc";
}

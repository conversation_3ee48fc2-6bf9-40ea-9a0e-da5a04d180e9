package com.kbao.kbcelms.riskconfig.bean;

import com.kbao.kbcelms.riskconfig.entity.RiskMatrixConfig;
import com.kbao.kbcelms.riskconfig.entity.RiskToolsConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 行业风险配置请求对象
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@ApiModel(value = "IndustryRiskConfigRequest", description = "行业风险配置请求对象")
public class IndustryRiskConfigRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID（更新时必填）")
    private String id;

    @ApiModelProperty(value = "一级行业编码")
    private String industryLevel1Code;

    @ApiModelProperty(value = "一级行业名称")
    private String industryLevel1Name;

    @ApiModelProperty(value = "二级行业编码")
    private String industryLevel2Code;

    @ApiModelProperty(value = "二级行业名称")
    private String industryLevel2Name;

    @ApiModelProperty(value = "风险矩阵说明描述")
    private String matrixDesc;

//    @ApiModelProperty(value = "整体风险等级")
//    private String riskLevel;

    @ApiModelProperty(value = "状态")
    private String status = "active";

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @Valid
    @ApiModelProperty(value = "风险矩阵配置")
    private RiskMatrixConfig matrixConfig;

//    @Valid
//    @ApiModelProperty(value = "防控工具配置")
//    private RiskToolsConfig toolsConfig;

    @Size(max = 10000, message = "富文本详细说明内容不能超过10000个字符")
    @ApiModelProperty(value = "富文本详细说明内容")
    private String richTextContent;

    @ApiModelProperty(value = "变更摘要（用于历史记录）")
    private String changeSummary;
}

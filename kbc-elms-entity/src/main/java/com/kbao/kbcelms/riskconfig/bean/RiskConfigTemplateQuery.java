package com.kbao.kbcelms.riskconfig.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 风险配置模板查询条件
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@ApiModel(value = "RiskConfigTemplateQuery", description = "风险配置模板查询条件")
public class RiskConfigTemplateQuery implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板名称（模糊查询）")
    private String templateName;

    @ApiModelProperty(value = "模板类型")
    private String templateType;

    @ApiModelProperty(value = "模板类型列表")
    private List<String> templateTypes;

    @ApiModelProperty(value = "适用行业编码")
    private String applicableIndustry;

    @ApiModelProperty(value = "是否公开模板")
    private Boolean isPublic;

    @ApiModelProperty(value = "模板状态")
    private String status;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "关键词搜索（支持模板名称、描述等）")
    private String keyword;

    @ApiModelProperty(value = "排序字段")
    private String sortField = "createdTime";

    @ApiModelProperty(value = "排序方向（asc-升序，desc-降序）")
    private String sortDirection = "desc";
}

package com.kbao.kbcelms.riskconfig.util;

import com.kbao.kbcelms.riskconfig.bean.IndustryRiskConfigRequest;
import com.kbao.kbcelms.riskconfig.constants.RiskConfigConstants;
import com.kbao.kbcelms.riskconfig.entity.IndustryRiskConfig;
import com.kbao.kbcelms.riskconfig.entity.RiskConfigHistory;
import com.kbao.kbcelms.riskconfig.vo.IndustryRiskConfigVO;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 风险配置数据转换工具类
 * <AUTHOR>
 * @date 2025-08-11
 */
public class RiskConfigConverter {

    /**
     * 请求对象转实体对象
     */
    public static IndustryRiskConfig requestToEntity(IndustryRiskConfigRequest request) {
        if (request == null) {
            return null;
        }
        
        IndustryRiskConfig entity = new IndustryRiskConfig();
        BeanUtils.copyProperties(request, entity);
        
        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus(RiskConfigConstants.Status.ACTIVE);
        }
        if (entity.getTenantId() == null) {
            entity.setTenantId(RiskConfigConstants.DefaultConfig.DEFAULT_TENANT_ID);
        }
        if (entity.getDeleted() == null) {
            entity.setDeleted(false);
        }
        
        return entity;
    }

    /**
     * 实体对象转VO对象
     */
    public static IndustryRiskConfigVO entityToVO(IndustryRiskConfig entity) {
        if (entity == null) {
            return null;
        }
        
        IndustryRiskConfigVO vo = new IndustryRiskConfigVO();
        BeanUtils.copyProperties(entity, vo);
        
        // 计算统计信息
//        vo.setMatrixStats(calculateMatrixStats(entity));
//        vo.setToolsStats(calculateToolsStats(entity));
        
        return vo;
    }

    /**
     * 实体对象列表转VO对象列表
     */
    public static List<IndustryRiskConfigVO> entityListToVOList(List<IndustryRiskConfig> entityList) {
        if (entityList == null) {
            return null;
        }
        
        return entityList.stream()
                .map(RiskConfigConverter::entityToVO)
                .collect(Collectors.toList());
    }

    /**
     * 计算风险矩阵统计信息
     */
    public static IndustryRiskConfigVO.MatrixStatsVO calculateMatrixStats(IndustryRiskConfig entity) {
        IndustryRiskConfigVO.MatrixStatsVO stats = new IndustryRiskConfigVO.MatrixStatsVO();
        
        if (entity.getMatrixConfig() == null) {
            stats.setTotalRows(0);
            stats.setTotalColumns(0);
            stats.setHighRiskCount(0);
            stats.setMediumRiskCount(0);
            stats.setLowRiskCount(0);
            stats.setCriticalRiskCount(0);
            return stats;
        }
        
        // 计算行数和列数
        stats.setTotalColumns(entity.getMatrixConfig().getColumnDefs() != null ? 
                entity.getMatrixConfig().getColumnDefs().size() : 0);
        stats.setTotalRows(entity.getMatrixConfig().getRowData() != null ? 
                entity.getMatrixConfig().getRowData().size() : 0);
        
        // 计算各风险等级数量
        int highRiskCount = 0;
        int mediumRiskCount = 0;
        int lowRiskCount = 0;
        int criticalRiskCount = 0;
        
        if (entity.getMatrixConfig().getRowData() != null) {
            for (Map<String, Object> row : entity.getMatrixConfig().getRowData()) {
                String level = (String) row.get("level");
                if (level != null) {
                    switch (level) {
                        case RiskConfigConstants.RiskLevel.HIGH:
                            highRiskCount++;
                            break;
                        case RiskConfigConstants.RiskLevel.MEDIUM:
                            mediumRiskCount++;
                            break;
                        case RiskConfigConstants.RiskLevel.LOW:
                            lowRiskCount++;
                            break;
                        case RiskConfigConstants.RiskLevel.CRITICAL:
                            criticalRiskCount++;
                            break;
                    }
                }
            }
        }
        
        stats.setHighRiskCount(highRiskCount);
        stats.setMediumRiskCount(mediumRiskCount);
        stats.setLowRiskCount(lowRiskCount);
        stats.setCriticalRiskCount(criticalRiskCount);
        
        return stats;
    }

    /**
     * 计算防控工具统计信息
     */
//    public static IndustryRiskConfigVO.ToolsStatsVO calculateToolsStats(IndustryRiskConfig entity) {
//        IndustryRiskConfigVO.ToolsStatsVO stats = new IndustryRiskConfigVO.ToolsStatsVO();
//
//        if (entity.getToolsConfig() == null) {
//            stats.setTotalRows(0);
//            stats.setTotalColumns(0);
//            stats.setHighEffectCount(0);
//            stats.setMediumEffectCount(0);
//            stats.setLowEffectCount(0);
//            return stats;
//        }
//
//        // 计算行数和列数
//        stats.setTotalColumns(entity.getToolsConfig().getColumnDefs() != null ?
//                entity.getToolsConfig().getColumnDefs().size() : 0);
//        stats.setTotalRows(entity.getToolsConfig().getRowData() != null ?
//                entity.getToolsConfig().getRowData().size() : 0);
//
//        // 计算各效果等级数量
//        int highEffectCount = 0;
//        int mediumEffectCount = 0;
//        int lowEffectCount = 0;
//
//        if (entity.getToolsConfig().getRowData() != null) {
//            for (Map<String, Object> row : entity.getToolsConfig().getRowData()) {
//                String effect = (String) row.get("effect");
//                if (effect != null) {
//                    switch (effect) {
//                        case RiskConfigConstants.ControlEffect.HIGH:
//                            highEffectCount++;
//                            break;
//                        case RiskConfigConstants.ControlEffect.MEDIUM:
//                            mediumEffectCount++;
//                            break;
//                        case RiskConfigConstants.ControlEffect.LOW:
//                            lowEffectCount++;
//                            break;
//                    }
//                }
//            }
//        }
//
//        stats.setHighEffectCount(highEffectCount);
//        stats.setMediumEffectCount(mediumEffectCount);
//        stats.setLowEffectCount(lowEffectCount);
//
//        return stats;
//    }

    /**
     * 创建配置变更历史记录
     */
    public static RiskConfigHistory createHistory(String configId, String version, String changeType, 
                                                  Map<String, Object> changeContent, String changeSummary, 
                                                  String userId) {
        RiskConfigHistory history = new RiskConfigHistory();
        history.setConfigId(configId);
        history.setVersion(version);
        history.setChangeType(changeType);
        history.setChangeContent(changeContent);
        history.setChangeSummary(changeSummary);
        history.setCreatedBy(userId);
        history.setCreatedTime(new Date());
        return history;
    }

    /**
     * 生成新版本号
     */
    public static String generateNewVersion(String currentVersion) {
        if (currentVersion == null || currentVersion.isEmpty()) {
            return RiskConfigConstants.DefaultConfig.DEFAULT_VERSION;
        }
        
        String[] parts = currentVersion.split("\\.");
        if (parts.length >= 2) {
            try {
                int majorVersion = Integer.parseInt(parts[0]);
                int minorVersion = Integer.parseInt(parts[1]);
                return majorVersion + "." + (minorVersion + 1);
            } catch (NumberFormatException e) {
                return RiskConfigConstants.DefaultConfig.DEFAULT_VERSION;
            }
        }
        
        return RiskConfigConstants.DefaultConfig.DEFAULT_VERSION;
    }

    /**
     * 创建变更内容快照
     */
    public static Map<String, Object> createChangeSnapshot(IndustryRiskConfig oldConfig, 
                                                           IndustryRiskConfig newConfig) {
        Map<String, Object> snapshot = new HashMap<>();
        
        if (oldConfig != null) {
            snapshot.put("oldVersion", oldConfig.getConfigVersion());
//            snapshot.put("oldRiskLevel", oldConfig.getRiskLevel());
            snapshot.put("oldMatrixCount", oldConfig.getMatrixConfig() != null &&
                    oldConfig.getMatrixConfig().getRowData() != null ?
                    oldConfig.getMatrixConfig().getRowData().size() : 0);
//            snapshot.put("oldToolsCount", oldConfig.getToolsConfig() != null &&
//                    oldConfig.getToolsConfig().getRowData() != null ?
//                    oldConfig.getToolsConfig().getRowData().size() : 0);
            snapshot.put("oldRichTextLength", oldConfig.getRichTextContent() != null ?
                    oldConfig.getRichTextContent().length() : 0);
        }
        
        if (newConfig != null) {
            snapshot.put("newVersion", newConfig.getConfigVersion());
//            snapshot.put("newRiskLevel", newConfig.getRiskLevel());
            snapshot.put("newMatrixCount", newConfig.getMatrixConfig() != null &&
                    newConfig.getMatrixConfig().getRowData() != null ?
                    newConfig.getMatrixConfig().getRowData().size() : 0);
//            snapshot.put("newToolsCount", newConfig.getToolsConfig() != null &&
//                    newConfig.getToolsConfig().getRowData() != null ?
//                    newConfig.getToolsConfig().getRowData().size() : 0);
            snapshot.put("newRichTextLength", newConfig.getRichTextContent() != null ?
                    newConfig.getRichTextContent().length() : 0);
        }
        
        snapshot.put("changeTime", new Date());
        
        return snapshot;
    }
}

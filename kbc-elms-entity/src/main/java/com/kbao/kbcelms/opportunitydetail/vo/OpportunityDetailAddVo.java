package com.kbao.kbcelms.opportunitydetail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;import java.util.Date;import java.util.List;

/**
 * 机会明细表
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
public class OpportunityDetailAddVo {
    /** 投保人员规模 */
    @ApiModelProperty(value = "投保人员规模", example = "500")
    private Integer insureNum;
    
    /** 是否有历史保单：0-否，1-是 */
    @ApiModelProperty(value = "是否有历史保单", example = "1", notes = "0-否，1-是")
    private String hasHistoryPolicy;
    
    /** 保单到期日期 */
    @ApiModelProperty(value = "保单到期日期", example = "2025-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date policyExpireTime;
    
    /** 是否需要投标：0-否，1-是 */
    @ApiModelProperty(value = "是否需要投标", example = "1", notes = "0-否，1-是")
    private Integer isBid;
    
    /** 投标结果 0-失败 1-成功 */
    @ApiModelProperty(value = "投标结果", example = "1", notes = "0-失败，1-成功")
    private Integer bidResult;
    
    /** 投标开始时间 */
    @ApiModelProperty(value = "投标开始时间", example = "2025-01-15 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date bidStartDate;
    
    /** 投标结束时间 */
    @ApiModelProperty(value = "投标结束时间", example = "2025-02-15 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date bidEndDate;
    
    /** 保费预算 */
    @ApiModelProperty(value = "保费预算", example = "1000000")
    private String premiumBudget;
    
    /** 企业对接人 */
    @ApiModelProperty(value = "企业对接人", example = "李经理")
    private String contacter;
    
    /** 企业对接人职务 */
    @ApiModelProperty(value = "企业对接人职务", example = "人力资源总监")
    private String contacterPost;

    /** 是否添加健康服务产品 */
    @ApiModelProperty(value = "是否添加健康服务产品", example = "1", notes = "0-否，1-是")
    private String addHealthService;

    /** 是否添加救援服务产品 */
    @ApiModelProperty(value = "是否添加救援服务产品", example = "1", notes = "0-否，1-是")
    private String addRescueService;

    /** 备注 */
    @ApiModelProperty(value = "备注", example = "项目特殊要求说明")
    private String remark;

    private List<String> insureTypes;

    @ApiModelProperty(value = "险种类型备注", example = "特殊险种说明")
    private String insuranceTypeRemark;
} 
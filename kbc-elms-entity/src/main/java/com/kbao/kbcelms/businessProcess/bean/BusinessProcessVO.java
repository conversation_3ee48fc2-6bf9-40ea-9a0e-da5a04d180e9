package com.kbao.kbcelms.businessProcess.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 业务流程配置返回VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ApiModel(value = "BusinessProcessVO", description = "业务流程配置返回对象")
public class BusinessProcessVO {

    @ApiModelProperty(value = "业务编码", required = true)
    private String businessCode;

    @ApiModelProperty(value = "业务名称", required = true)
    private String businessName;

    @ApiModelProperty(value = "流程配置列表", notes = "启用的流程编码，按执行顺序排列")
    private List<String> processConfigs;
}

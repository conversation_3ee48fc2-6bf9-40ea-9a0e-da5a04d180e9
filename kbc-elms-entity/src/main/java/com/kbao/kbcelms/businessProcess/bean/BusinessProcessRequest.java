package com.kbao.kbcelms.businessProcess.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 业务流程配置请求参数
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ApiModel(value = "BusinessProcessRequest", description = "业务流程配置请求对象")
public class BusinessProcessRequest {

    @ApiModelProperty(value = "业务编码", required = true)
    @NotBlank(message = "业务编码不能为空")
    private String businessCode;

    @ApiModelProperty(value = "业务名称", required = true)
    @NotBlank(message = "业务名称不能为空")
    private String businessName;

    @ApiModelProperty(value = "流程配置列表", notes = "启用的流程编码，按执行顺序排列")
    @NotNull(message = "流程配置列表不能为null")
    private List<String> processConfigs;
}

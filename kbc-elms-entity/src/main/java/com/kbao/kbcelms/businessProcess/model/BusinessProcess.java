package com.kbao.kbcelms.businessProcess.model;

import io.swagger.annotations.ApiModel;import io.swagger.annotations.ApiModelProperty;import org.springframework.data.annotation.Id;import org.springframework.data.mongodb.core.index.Indexed;import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;import java.util.Date;import java.util.List;

/**
 * 业务流程配置MongoDB实体
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Document(collection = "business_process")
@ApiModel(value = "BusinessProcess", description = "业务流程")
public class BusinessProcess  implements Serializable {

    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 业务编码
     */
    @Indexed
    private String businessCode;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 流程配置列表（有序）
     * 只存储启用的流程编码，按执行顺序排列
     */
    private List<String> processConfigs;

    private String createId;

    private Date createTime;

    private String tenantId;

    /**
     * 流程编码常量
     */
    public static class ProcessCode {
        /** 企业KYC查询 */
        public static final String KYC_QUERY = "KYC_QUERY";
        /** 企业合规审批 */
        public static final String COMPLIANCE_APPROVAL = "COMPLIANCE_APPROVAL";
        /** 企业问卷收集 */
        public static final String QUESTIONNAIRE_COLLECTION = "QUESTIONNAIRE_COLLECTION";
        /** 企业风险管理方案 */
        public static final String RISK_MANAGEMENT = "RISK_MANAGEMENT";
    }
}

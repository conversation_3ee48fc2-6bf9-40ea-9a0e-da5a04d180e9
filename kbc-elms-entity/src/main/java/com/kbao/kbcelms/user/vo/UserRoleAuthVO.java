package com.kbao.kbcelms.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:14
 */
@Data
public class UserRoleAuthVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户ID,云服用户ID")
    private String userId;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("权限位编码集合,逗号隔开")
    private String roleAuthStr;

    @ApiModelProperty("权限位编码集合")
    private List<String> roleAuths;

    @ApiModelProperty("用户所属角色集合")
    private List<UserRoleVO> userRoles;

}

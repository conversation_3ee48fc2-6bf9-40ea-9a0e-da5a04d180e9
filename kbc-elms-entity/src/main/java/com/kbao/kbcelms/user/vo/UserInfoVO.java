package com.kbao.kbcelms.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/7 13:55
 */
@Data
public class UserInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("权限位编码集合")
    private List<String> roleAuths;

    // ===================以下字段为关联租户相关数据====================

    @ApiModelProperty("关联租户类型： org 机构 dept部门 out 外部")
    private String relationType;

    @ApiModelProperty("用户归属机构编码")
    private String orgCode;

    @ApiModelProperty("用户归属机构完整机构编码")
    private String orgCodePath;

    @ApiModelProperty("用户归属机构名称")
    private String orgName;

    @ApiModelProperty("用户归属机构完整机构名称")
    private String orgNamePath;

    // ===================以下字段为用户机构权限相关数据====================

    @ApiModelProperty("机构类型 全国机构 org ，总公司 dept")
    private String orgType;

    @ApiModelProperty("用户权限机构编码集合")
    private List<String> orgCodes;

    @ApiModelProperty("用户权限机构名称集合")
    private List<String> orgNames;

    // ===================以下字段为用户角色相关数据====================

    @ApiModelProperty("用户所属角色集合")
    private List<UserRoleVO> userRoles;

}

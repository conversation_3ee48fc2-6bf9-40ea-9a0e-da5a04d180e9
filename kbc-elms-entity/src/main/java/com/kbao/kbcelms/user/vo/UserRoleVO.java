package com.kbao.kbcelms.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:15
 */
@Data
public class UserRoleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "用户角色管理表ID")
    private Integer userRoleId;

    private Integer roleId;

    private String roleName;

    private String roleType;

    private List<RoleAuthVO> roleAuths;

}

package com.kbao.kbcelms.enterpriseopplock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 企业机会锁定编辑请求DTO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(value = "企业机会锁定编辑请求DTO", description = "用于编辑锁定信息")
public class EnterpriseOppLockEditDTO {
    
    @NotNull(message = "锁定记录ID不能为空")
    @ApiModelProperty(value = "锁定记录ID", required = true, example = "1")
    private Integer lockId;
    
    @ApiModelProperty(value = "锁定截止时间", example = "2025-12-31 23:59:59")
    private Date lockEndTime;
    
    @ApiModelProperty(value = "锁定文件列表（最多10个）")
    private List<LockFileInfo> lockFiles;
    
    @ApiModelProperty(value = "备注信息", example = "这是锁定备注信息")
    private String remark;
    
    /**
     * 锁定文件信息内部类
     */
    @Data
    @ApiModel(value = "锁定文件信息", description = "锁定文件详细信息")
    public static class LockFileInfo {
        
        @ApiModelProperty(value = "文件名称", example = "合同文件.pdf")
        private String fileName;
        
        @ApiModelProperty(value = "文件地址", example = "http://example.com/files/contract.pdf")
        private String fileUrl;
        
        @ApiModelProperty(value = "文件大小（字节）", example = "1024000")
        private Long fileSize;
        
        @ApiModelProperty(value = "文件类型", example = "pdf")
        private String fileType;
        
        @ApiModelProperty(value = "上传时间", example = "2025-01-15 10:30:00")
        private Date uploadTime;
    }
}


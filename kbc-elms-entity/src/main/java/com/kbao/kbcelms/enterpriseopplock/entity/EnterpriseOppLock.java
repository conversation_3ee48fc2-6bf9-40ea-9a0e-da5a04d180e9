package com.kbao.kbcelms.enterpriseopplock.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 企业机会锁定表
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class EnterpriseOppLock {
    
    /** 主键 */
    @ApiModelProperty(value = "主键")
    private Integer id;
    
    /** 机会id */
    @ApiModelProperty(value = "机会id")
    private Integer opportunityId;
    
    /** 机会类型: 1-员服，2-综合 */
    @ApiModelProperty(value = "机会类型: 1-员服，2-综合")
    private String opportunityType;
    
    /** 企业名称 */
    @ApiModelProperty(value = "企业名称")
    private String name;
    
    /** 社会统一信用代码 */
    @ApiModelProperty(value = "社会统一信用代码")
    private String creditCode;
    
    /** 机会锁定人 */
    @ApiModelProperty(value = "机会锁定人")
    private String lockUser;
    
    /** 机会锁定时间 */
    @ApiModelProperty(value = "机会锁定时间")
    private Date lockTime;

    /** 锁定截止时间 */
    @ApiModelProperty(value = "锁定截止时间")
    private Date lockEndTime;

    /** 当前最新有效锁定记录ID */
    @ApiModelProperty(value = "当前最新有效锁定记录ID")
    private String currentRecordId;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createId;
    
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /** 更新人 */
    @ApiModelProperty(value = "更新人")
    private String updateId;
    
    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 锁定文件列表 */
    @ApiModelProperty(value = "锁定文件列表")
    private List<EnterpriseOppLockRecord.LockFileInfo> lockFiles;

}

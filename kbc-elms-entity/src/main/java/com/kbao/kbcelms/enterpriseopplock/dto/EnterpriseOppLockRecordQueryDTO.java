package com.kbao.kbcelms.enterpriseopplock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 企业机会锁定记录查询DTO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "企业机会锁定记录查询DTO")
public class EnterpriseOppLockRecordQueryDTO {
    
    /** 企业机会锁定主表ID */
    @ApiModelProperty(value = "企业机会锁定主表ID", example = "1001")
    private Integer lockId;
    
    /** 企业统一社会信用代码 */
    @ApiModelProperty(value = "企业统一社会信用代码", example = "91110000123456789X")
    private String creditCode;
    
    /** 机会类型 */
    @ApiModelProperty(value = "机会类型: 1-员服，2-综合", example = "1")
    private String opportunityType;
    
    /** 锁定人 */
    @ApiModelProperty(value = "锁定人", example = "张三")
    private String lockUser;
    
    /** 记录状态 */
    @ApiModelProperty(value = "记录状态: 0-无效，1-有效", example = "1")
    private Integer status;
    
    /** 锁定时间开始 */
    @ApiModelProperty(value = "锁定时间开始", example = "2025-01-15 00:00:00")
    private Date lockTimeStart;
    
    /** 锁定时间结束 */
    @ApiModelProperty(value = "锁定时间结束", example = "2025-01-15 23:59:59")
    private Date lockTimeEnd;
    
    /** 锁定截止时间开始 */
    @ApiModelProperty(value = "锁定截止时间开始", example = "2025-01-15 00:00:00")
    private Date lockEndTimeStart;
    
    /** 锁定截止时间结束 */
    @ApiModelProperty(value = "锁定截止时间结束", example = "2025-01-15 23:59:59")
    private Date lockEndTimeEnd;
    
    /** 创建时间开始 */
    @ApiModelProperty(value = "创建时间开始", example = "2025-01-15 00:00:00")
    private Date createTimeStart;
    
    /** 创建时间结束 */
    @ApiModelProperty(value = "创建时间结束", example = "2025-01-15 23:59:59")
    private Date createTimeEnd;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.enterpriseopplock.dao.EnterpriseOppLockMapper">
    
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="opportunityId" column="opportunity_id" jdbcType="INTEGER" />
        <result property="opportunityType" column="opportunity_type" jdbcType="CHAR" />
        <result property="name" column="name" jdbcType="VARCHAR" />
        <result property="creditCode" column="credit_code" jdbcType="VARCHAR" />
        <result property="lockUser" column="lock_user" jdbcType="VARCHAR" />
        <result property="lockTime" column="lock_time" jdbcType="TIMESTAMP" />
        <result property="lockEndTime" column="lock_end_time" jdbcType="TIMESTAMP" />
        <result property="currentRecordId" column="current_record_id" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="TINYINT" />
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, opportunity_id, opportunity_type, name, credit_code, lock_user, lock_time, lock_end_time, current_record_id, create_id, create_time,
        update_id, update_time, is_deleted
    </sql>

    <!-- 别名列 -->
    <sql id="Alias_Column_List">
        t.id,
        t.opportunity_id,
        t.opportunity_type,
        t.name,
        t.credit_code,
        t.lock_user,
        t.lock_time,
        t.lock_end_time,
        t.current_record_id,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted
    </sql>
    
    <!-- 基础查询条件 -->
    <sql id="Base_Condition">
        <where>
            <if test="id != null">
                and t.id = #{id,jdbcType=INTEGER}
            </if>
            <if test="opportunityId != null">
                and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
            </if>
            <if test="opportunityType != null and opportunityType != ''">
                and t.opportunity_type = #{opportunityType,jdbcType=CHAR}
            </if>
            <if test="name != null">
                and t.name = #{name,jdbcType=VARCHAR}
            </if>
            <if test="creditCode != null">
                and t.credit_code = #{creditCode,jdbcType=VARCHAR}
            </if>
            <if test="lockUser != null">
                and t.lock_user = #{lockUser,jdbcType=VARCHAR}
            </if>
            <if test="lockTime != null">
                and t.lock_time = #{lockTime,jdbcType=TIMESTAMP}
            </if>
            <if test="lockEndTime != null">
                and t.lock_end_time = #{lockEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isDeleted != null">
                and t.is_deleted = #{isDeleted,jdbcType=TINYINT}
            </if>
        </where>
    </sql>
    
    <!-- BaseMapper 需要的基础查询方法 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_enterprise_opp_lock where id = #{id}
    </select>
    
    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_enterprise_opp_lock t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_enterprise_opp_lock t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock" useGeneratedKeys="true" keyProperty="id">
        insert into t_enterprise_opp_lock (
            opportunity_id, opportunity_type, name, credit_code, lock_user, lock_time, lock_end_time, current_record_id, create_id, create_time, update_id, update_time, is_deleted
        ) values (
            #{opportunityId}, #{opportunityType}, #{name}, #{creditCode}, #{lockUser}, #{lockTime}, #{lockEndTime}, #{currentRecordId}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, #{isDeleted}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock" useGeneratedKeys="true" keyProperty="id">
        insert into t_enterprise_opp_lock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">opportunity_id,</if>
            <if test="opportunityType != null">opportunity_type,</if>
            <if test="name != null">name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="lockUser != null">lock_user,</if>
            <if test="lockTime != null">lock_time,</if>
            <if test="lockEndTime != null">lock_end_time,</if>
            <if test="currentRecordId != null">current_record_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDeleted != null">is_deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">#{opportunityId},</if>
            <if test="opportunityType != null">#{opportunityType},</if>
            <if test="name != null">#{name},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="lockUser != null">#{lockUser},</if>
            <if test="lockTime != null">#{lockTime},</if>
            <if test="lockEndTime != null">#{lockEndTime},</if>
            <if test="currentRecordId != null">#{currentRecordId},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock">
        update t_enterprise_opp_lock set
            opportunity_id = #{opportunityId},
            opportunity_type = #{opportunityType},
            name = #{name},
            credit_code = #{creditCode},
            lock_user = #{lockUser},
            lock_time = #{lockTime},
            lock_end_time = #{lockEndTime},
            current_record_id = #{currentRecordId},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock">
        update t_enterprise_opp_lock
        <set>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="opportunityType != null">opportunity_type = #{opportunityType},</if>
            <if test="name != null">name = #{name},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="lockUser != null">lock_user = #{lockUser},</if>
            <if test="lockTime != null">lock_time = #{lockTime},</if>
            <if test="lockEndTime != null">lock_end_time = #{lockEndTime},</if>
            <if test="currentRecordId != null">current_record_id = #{currentRecordId},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from t_enterprise_opp_lock where id = #{id}
    </delete>
    
    <!-- 逻辑删除 -->
    <update id="deleteLogical">
        update t_enterprise_opp_lock 
        set is_deleted = 1, update_id = #{updateId}, update_time = now()
        where id = #{id,jdbcType=INTEGER}
    </update>
    
    <!-- 根据机会类型和社会统一信用代码查询锁定记录列表 -->
    <select id="selectByOpportunityTypeAndCreditCode" resultMap="BaseResultMap">
        select <include refid="Alias_Column_List" />
        from t_enterprise_opp_lock t
        where t.is_deleted = 0
          and t.opportunity_type = #{opportunityType,jdbcType=CHAR}
          and t.credit_code = #{creditCode,jdbcType=VARCHAR}
        limit 1
    </select>
    
    <!-- 根据机会ID查询锁定记录 -->
    <select id="selectByOpportunityId" resultMap="BaseResultMap">
        select <include refid="Alias_Column_List" />
        from t_enterprise_opp_lock t
        where t.is_deleted = 0
          and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
        order by t.create_time desc
        limit 1
    </select>
    
    <!-- 根据机会ID和社会统一信用代码查询锁定记录 -->
    <select id="selectByOpportunityIdAndCreditCode" resultMap="BaseResultMap">
        select <include refid="Alias_Column_List" />
        from t_enterprise_opp_lock t
        where t.is_deleted = 0
          and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
          and t.credit_code = #{creditCode,jdbcType=VARCHAR}
        order by t.create_time desc
        limit 1
    </select>

    <select id="hasLockOpp" resultType="int">
        select count(1) from t_enterprise_opp_lock
        where credit_code = #{creditCode} and opportunity_type = #{opportunityType}
        and lock_time <![CDATA[ <= ]]> now() and lock_end_time >= now()
        and is_deleted = 0
    </select>
</mapper>

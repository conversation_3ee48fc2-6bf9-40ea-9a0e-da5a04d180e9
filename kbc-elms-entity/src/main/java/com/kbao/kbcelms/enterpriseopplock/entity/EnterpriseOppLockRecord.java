package com.kbao.kbcelms.enterpriseopplock.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * 企业机会锁定记录文档
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Document
public class EnterpriseOppLockRecord {
    
    /** MongoDB文档ID */
    @Id
    @ApiModelProperty(value = "MongoDB文档ID")
    private String id;

    /** 企业机会锁定主表ID */
    @ApiModelProperty(value = "企业机会锁定主表ID")
    private Integer lockId;

    /** 企业统一社会信用代码 */
    @ApiModelProperty(value = "企业统一社会信用代码")
    private String creditCode;
    
    /** 机会类型: 1-员服，2-综合 */
    @ApiModelProperty(value = "机会类型: 1-员服，2-综合")
    private String opportunityType;
    
    /** 锁定人 */
    @ApiModelProperty(value = "锁定人")
    private String lockUser;
    
    /** 锁定时间 */
    @ApiModelProperty(value = "锁定时间")
    private Date lockTime;
    
    /** 锁定截止时间 */
    @ApiModelProperty(value = "锁定截止时间")
    private Date lockEndTime;
    
    /** 锁定文件记录（文件数组，最多10条） */
    @ApiModelProperty(value = "锁定文件记录（文件数组，最多10条）")
    private List<LockFileInfo> lockFiles;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;
    
    /** 记录状态: 0-无效，1-有效 */
    @ApiModelProperty(value = "记录状态: 0-无效，1-有效")
    private Integer status;
    
    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createId;
    
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /** 更新人 */
    @ApiModelProperty(value = "更新人")
    private String updateId;
    
    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /** 是否删除: 0-未删除，1-已删除 */
    @ApiModelProperty(value = "是否删除: 0-未删除，1-已删除")
    private Integer isDeleted;
    
    /**
     * 锁定文件信息内部类
     */
    @Data
    public static class LockFileInfo {
        
        /** 文件名称 */
        @ApiModelProperty(value = "文件名称")
        private String fileName;
        
        /** 文件地址 */
        @ApiModelProperty(value = "文件地址")
        private String fileUrl;
        
        /** 文件大小（字节） */
        @ApiModelProperty(value = "文件大小（字节）")
        private Long fileSize;
        
        /** 文件类型 */
        @ApiModelProperty(value = "文件类型")
        private String fileType;
        
        /** 上传时间 */
        @ApiModelProperty(value = "上传时间")
        private Date uploadTime;
    }
}

<template>
  <div class="user-list">

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"></SearchForm>

    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover>
      <el-table-column align="center" prop="nickName" label="姓名"></el-table-column>
      <el-table-column align="center" prop="phone" label="手机号"></el-table-column>
      <el-table-column align="center" prop="email" label="企业邮箱"></el-table-column>
      <el-table-column align="center" prop="agentCode" label="工号"></el-table-column>
      <el-table-column align="center" prop="ehrUserCode" label="ehr账号"></el-table-column>
      <el-table-column align="center" prop="organNamePath" label="人员归属"></el-table-column>
      <el-table-column align="center" prop="industryNames" label="擅长行业"></el-table-column>
      <el-table-column align="center" prop="insuranceNames" label="擅长险种"></el-table-column>
      <el-table-column align="center" label="状态">
        <template slot-scope="scope">
          {{ scope.row.status | getDicItemName("sys.gateway.status") }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="roleName" label="角色名称"></el-table-column>
      <el-table-column align="center" label="操作" width="170px">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="view(scope.row)">查看</el-button>
          <el-button class="btn-center" type="text" @click="update(scope.row)">编辑</el-button>
          <el-button class="btn-center" type="text" @click="del(scope.row)">删除</el-button>
          <el-button class="btn-center" type="text" @click="setStatus(scope.row,'0')"
            v-if="scope.row.status == '1'">禁用</el-button>
          <el-button class="btn-center" type="text" @click="setStatus(scope.row,'1')"
            v-if="scope.row.status == '0'">启用</el-button>
          <el-button class="btn-center" type="text" @click="showRolePopup(scope.row)">分配角色</el-button>
          <el-button class="btn-center" type="text" @click="showOrgAuthPopup(scope.row)">机构权限</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
      :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>


    <!-- 删除 -->
    <DtPopup :isShow.sync="showDelPopup" @close="showDelPopup = false" title="提示" center :footer="false" width="30%">
      <div class="check-popup">
        <div style="text-align: center">请确认是否删除？</div>
        <div class="btn-wrap">
          <el-button type="primary" class="btn-width"
            :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px', width: '120px'}"
            @click.stop="showDelPopup = false">取消</el-button>
          <el-button type="primary" class="btn-width"
            :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color, width: '120px' }"
            @click.stop="delHandle">确认</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 新增/编辑 -->
<!--    <DtPopup :isShow.sync="showPopup" @close="closeUserPopup" size="mini" :title="popupTitle" :footer="false">-->
<!--      <el-form ref="addForm" :model="addData" label-width="120px" :rules="addRules" label-position="left">-->
<!--        <div style="max-height:70vh;overflow:auto">-->
<!--          <el-form-item label="云服账号" prop="bscUserName">-->
<!--            <el-input :disabled="!this.isAdd" v-model="addData.bscUserName" @change="changeInput" auto-complete="off"-->
<!--              class="dt-input-width" placeholder="请输入云服账号"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="姓名" prop="nickName">-->
<!--            <el-input v-model="addData.nickName" :disabled="disabled" auto-complete="off" class="dt-input-width"-->
<!--              placeholder="请输入姓名"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="手机号" prop="phone">-->
<!--            <el-input v-model="addData.phone" :disabled="disabled" auto-complete="off" class="dt-input-width"-->
<!--              placeholder="请输入手机号"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="邮箱" prop="email">-->
<!--            <el-input v-model="addData.email" :disabled="disabled" auto-complete="off" class="dt-input-width"-->
<!--              placeholder="请输入邮箱"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="企微账号" prop="wechatAccount">-->
<!--            <el-input v-model="addData.wechatAccount" auto-complete="off" class="dt-input-width"-->
<!--              placeholder="请输入企微账号"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="EHR账号" prop="ehrUserCode">-->
<!--            <el-input v-model="addData.ehrUserCode" auto-complete="off" class="dt-input-width"-->
<!--              placeholder="请输入EHR账号"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="大童销售工号" prop="agentCode">-->
<!--            <el-input v-model="addData.agentCode" auto-complete="off" class="dt-input-width"-->
<!--              placeholder="请输入大童销售工号"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="人员归属" prop="relationType">-->
<!--            <el-radio-group v-model="addData.relationType" @change="changeOrganRadio">-->
<!--              <el-radio v-model="addData.relationType" v-for="(item, index) in relationTypeList" :key="index"-->
<!--                :label="item.dicItemCode" :disabled="!isAdd">{{ item.dicItemName }}</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="" prop="selectOrgValue">-->
<!--            <el-cascader :disabled="addData.relationType == 'out'" :options="organData"-->
<!--              :props="organProps" class="dt-cascader" ref="dt-cascader" v-model="selectOrgValue"-->
<!--              @change="handleCascader" filterable clearable :key="cascaderKey" placeholder="请选择人员归属"></el-cascader>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="机构专家身份" prop="expertType">-->
<!--            <el-select v-model="addData.expertType" filterable class="dt-input-width" placeholder="请选择机构专家身份" clearable>-->
<!--              <el-option v-for="(item, index) in expertTypeList" :label="item.dicItemName" :value="item.dicItemCode"-->
<!--                :key="index">-->
<!--              </el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="擅长险种" prop="insuranceTypes">-->
<!--            <el-select v-model="selectInsuranceTypes" filterable class="dt-input-width" placeholder="请选择擅长险种" multiple clearable>-->
<!--              <el-option v-for="(item, index) in insuranceTypesList" :label="item.dicItemName" :value="item.dicItemCode"-->
<!--                :key="index">-->
<!--              </el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="擅长行业" prop="industryTypes">-->
<!--            <el-select v-model="selectIndustryTypes" filterable class="dt-input-width" placeholder="请选择擅长行业" multiple clearable>-->
<!--              <el-option v-for="(item, index) in industryTypesList" :label="item.dicItemName" :value="item.dicItemCode"-->
<!--                :key="index">-->
<!--              </el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="个人简介" prop="personDesc">-->
<!--            <el-input style="width: 81%" type="textarea" v-model="addData.personDesc"-->
<!--              :autosize="{ minRows: 5, maxRows: 8 }" placeholder="请输入人员个人简介" maxlength="300字" show-word-limit></el-input>-->
<!--          </el-form-item>-->
<!--        </div>-->

<!--          <div class="addPopupFooter">-->
<!--            <div style="padding:20px 0;text-align: center;">-->
<!--              <el-button type="primary" class="btn-width"-->
<!--                         :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px', width: '120px' }"-->
<!--                         @click="closeUserPopup">取消</el-button>-->
<!--              <el-button type="primary" class="btn-width"-->
<!--                         :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color, width: '120px' }" @click="confirm">确认</el-button>-->
<!--            </div>-->
<!--          </div>-->

<!--      </el-form>-->
<!--    </DtPopup>-->

    <!-- 查看详情 -->
    <DtPopup :isShow.sync="showDetailPopup" @close="closeDetailPopup" size="mini" title="查看人员" :footer="false">
      <el-form ref="addForm" :model="addData" label-width="120px" label-position="left">
        <div style="max-height:70vh;overflow:auto">
          <el-form-item label="云服账号" prop="bscUserName">
            <el-input disabled v-model="addData.bscUserName" @change="changeInput" auto-complete="off"
                      class="dt-input-width" placeholder="请输入角色名称"></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="nickName">
            <el-input v-model="addData.nickName" disabled auto-complete="off" class="dt-input-width"
                      placeholder="请输入角色名称"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="addData.phone" disabled auto-complete="off" class="dt-input-width"
                      placeholder="请输入角色名称"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="addData.email" disabled auto-complete="off" class="dt-input-width"
                      placeholder="请输入角色名称"></el-input>
          </el-form-item>
          <el-form-item label="企微账号" prop="wechatAccount">
            <el-input v-model="addData.wechatAccount" auto-complete="off" class="dt-input-width"
                      placeholder="请输入角色名称" disabled></el-input>
          </el-form-item>
          <el-form-item label="EHR账号" prop="ehrUserCode">
            <el-input v-model="addData.ehrUserCode" auto-complete="off" class="dt-input-width"
                      placeholder="请输入角色名称" disabled></el-input>
          </el-form-item>
          <el-form-item label="大童销售工号" prop="agentCode">
            <el-input v-model="addData.agentCode" auto-complete="off" class="dt-input-width"
                      placeholder="请输入角色名称" disabled></el-input>
          </el-form-item>
          <el-form-item label="人员归属" prop="relationType">
            <el-radio-group v-model="addData.relationType" @change="changeOrganRadio">
              <el-radio v-model="addData.relationType" v-for="(item, index) in relationTypeList" :key="index"
                        :label="item.dicItemCode" disabled>{{ item.dicItemName }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" prop="selectOrgValue">
            <el-cascader disabled :options="organData"
                         :props="organProps" class="dt-cascader" ref="dt-cascader" v-model="selectOrgValue"
                         @change="handleCascader" filterable clearable :key="cascaderKey"></el-cascader>
          </el-form-item>
          <el-form-item label="机构专家身份" prop="expertType">
            <el-select v-model="addData.expertType" filterable class="dt-input-width" placeholder="请选择" clearable disabled>
              <el-option v-for="(item, index) in expertTypeList" :label="item.dicItemName" :value="item.dicItemCode"
                         :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="擅长险种" prop="insuranceTypes">
            <el-select v-model="selectInsuranceTypes" filterable class="dt-input-width" placeholder="请选择" multiple disabled>
              <el-option v-for="(item, index) in insuranceTypesList" :label="item.dicItemName" :value="item.dicItemCode"
                         :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="擅长行业" prop="industryTypes">
            <el-select v-model="selectIndustryTypes" filterable class="dt-input-width" placeholder="请选择" multiple disabled>
              <el-option v-for="(item, index) in industryTypesList" :label="item.dicItemName" :value="item.dicItemCode"
                         :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="个人简介" prop="personDesc">
            <el-input style="width: 81%" type="textarea" v-model="addData.personDesc"
                      :autosize="{ minRows: 5, maxRows: 8 }" placeholder="请输入人员个人简介" maxlength="300字" show-word-limit disabled></el-input>
          </el-form-item>
        </div>

        <div class="addPopupFooter">
          <div style="padding:20px 0;text-align: center;">
            <el-button type="primary" class="btn-width"
                       :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color, width: '120px',}" @click="closeDetailPopup">关闭</el-button>
          </div>
        </div>

      </el-form>
    </DtPopup>

    <!-- 启用/禁用弹窗 -->
    <DtPopup :isShow.sync="showStatusPopup" @close="closeStatusPopup" :title="statusTitle" center :footer="false" width="600px">
      <div class="status-confirm">
        <div class="confirm-content">
          <p>请确认是否{{ statusTitle }}?</p>
        </div>
        <div class="confirm-actions">
          <el-button @click="closeStatusPopup" style="width: 120px">取消</el-button>
          <el-button type="primary" @click="confirmStatus" style="width: 120px">确认</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 禁用弹窗 -->
<!--    <DtPopup-->
<!--      :isShow.sync="stopPopup"-->
<!--      center-->
<!--      width="600px"-->
<!--      :footer="false"-->
<!--      title="禁用原因"-->
<!--      class="user-match"-->
<!--      @close="closeStopPopup"-->
<!--    >-->
<!--      <el-form-->
<!--        ref="recordForm"-->
<!--        class="channelFormClass"-->
<!--        :model="statusData"-->
<!--        label-width="110px"-->
<!--        label-position="left"-->
<!--      >-->
<!--        <el-form-item label="原因" prop="stopReason">-->
<!--          <el-input-->
<!--            v-model="statusData.stopReason"-->
<!--            type="textarea"-->
<!--            class="dt-input-width"-->
<!--            :rows="4"-->
<!--            :maxlength="200"-->
<!--            placeholder="请注明具体原因"-->
<!--          ></el-input>-->
<!--        </el-form-item>-->

<!--        <el-form-item>-->
<!--          <div style="padding:20px 0;">-->
<!--            <el-button type="primary" class="btn-width"-->
<!--                       :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px' }"-->
<!--                       @click="closeStopPopup">取 消</el-button>-->
<!--            <el-button type="primary" class="btn-width"-->
<!--                       :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color }" @click="confirmStop">确认</el-button>-->
<!--          </div>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--    </DtPopup>-->


    <!-- 分配角色弹窗 -->
    <DtPopup width="900px" class="roleDialog" :isShow.sync="showRole" @close="cancelRole" title="分配角色" :footer="false">
      <div v-if="showRole">
        <div class="role">
          <!-- 展示头 -->
          <div class="roleHeader" :style="{ background: themeObj.navTagUnselectedColor }">
            <div style="width: 20%;">姓名：{{ nickName }}</div>
            <div>人员归属：{{ belongOrgNamePath }}</div>
          </div>
          <!-- 分配角色表格 -->
          <div class="roleDialogWrap">
            <el-table :data="roleTableData" ref="roleTable" @selection-change="handleSelectionChange" @select="chooseRoleCheck" stripe class="dt-table" style="width: 100%">
              <el-table-column type="selection" align="right" width="70"></el-table-column>
              <el-table-column align="center" prop="roleName" label="角色名称"></el-table-column>
            </el-table>
          </div>
          <Pagination @size-change="handleRoleSizeChange" @current-change="handleRoleCurrentChange" :pageData="roleInitParam" :total="roleTotal" layout="total, sizes, prev, pager, next, jumper"></Pagination>
          <!-- 按钮 -->
          <div class="roleFooter">
            <el-button size="large" @click="cancelRole" type="primary" plain :style="{ color: themeObj.color }">关闭</el-button>
          </div>
        </div>
      </div>
    </DtPopup>


    <!-- 机构权限弹窗 -->
    <DtPopup width="900px" class="orgDialog" :isShow.sync="showOrg" @close="cancelOrg" title="机构权限" :footer="false">
        <div class="role">
          <!-- 展示头 -->
          <div class="orgHeader" :style="{ background: themeObj.navTagUnselectedColor }">
            <div style="width: 20%;">姓名：{{ nickName }}</div>
            <div>人员归属：{{ belongOrgNamePath }}</div>
          </div>
          <div class="orgHeader" :style="{ background: themeObj.navTagUnselectedColor }">
            <div>已分配角色：{{ roleName }}</div>
          </div>
          <!-- 分配角色表格 -->
          <div class="orgDialogWrap">
            <el-radio-group v-model="orgAddData.orgType" @change="changeOrgTypeRadio">
              <el-radio label="org" class="square-radio">全国机构</el-radio>
              <el-radio label="dept" class="square-radio">总公司</el-radio>
            </el-radio-group>
          </div>
          <div v-if="orgAddData.orgType == 'org'">
            <div style="height:300px;overflow-y:auto;">
              <el-tree
                  :data="legalOrgList"
                  node-key="orgCode"
                  ref="orgTree"
                  :props="orgProps"
                  show-checkbox
              ></el-tree>
            </div>

          </div>
          <!-- 按钮 -->
          <div class="roleFooter">
            <div style="padding:20px 0;text-align: center;">
              <el-button type="primary" class="btn-width"
                         :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px', width: '120px' }"
                         @click="cancelOrg">取消</el-button>
              <el-button type="primary" class="btn-width"
                         :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color, width: '120px' }" @click="saveUserOrg">保存</el-button>
            </div>
          </div>
        </div>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/userManagement/index.js";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { getRolePage } from "@/api/roleManagement/index.js";
import {findLegalOrgDataByTenantId, findRolesByUserId, getRoleList} from "@/api/userManagement/index.js";
import _ from "lodash";

export default {
  name: "userList",
  data() {
    return {
      toolListProps: {
        toolTitle: "用户管理",
        toolList: [
          {
            name: "新增人员",
            icon: "iconfont icondt8",
            // btnCode: "elms:user:add"
          }
        ]
      },
      tableData: [{}],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          nickName: "",
          roleName: "",
          organName: "",
          industryNames: "",
          insuranceNames: "",
          email: ""
        }
      },
      searchFormTemp: [
        {
          label: "姓名",
          name: "nickName",
          type: "input",
          width: "200px"
        },
        {
          label: "角色名称",
          name: "roleName",
          type: "input",
          width: "200px",
        },
        {
          label: "人员归属",
          name: "organName",
          type: "input",
          width: "200px"
        },
        {
          label: "擅长行业",
          name: "industryNames",
          type: "input",
          width: "200px",
        },
        {
          label: "擅长险种",
          name: "insuranceNames",
          type: "input",
          width: "200px",
        },
        {
          label: "邮箱",
          name: "email",
          type: "input",
          width: "200px"
        }
      ],
      total: 0,
      showDelPopup: false,
      showPopup: false,
      isAdd: true,
      popupTitle: "",
      showDetailPopup: false,
      stopPopup:false,
      showStatusPopup:false,
      statusTitle: "",
      selectOrgValue: [],
      cascaderKey: 0,
      addData: {
        email:"",
        nickName:"",
        phone:"",
        userId:"",
        relationType: "dept",
        organCode: "",
        organCodePath: "",
        stopReason:"",
        insuranceTypes:"",
        insuranceNames:"",
        industryTypes:"",
        industryNames:""
      },
      statusData: {
        id: "",
        status: "",
        stopReason:""
      },
      addRules: {
        nickName: [{ required: true, validator: validate, trigger: "blur" }],
        phone: [{
          required: true,
          validator: validate,
          trigger: "blur",
          regax: [
            {
              message: "请输入正确的手机号",
              ruleFormat: /^1[3456789]\d{9}$/,
              type: "number"
            }
          ]
        }],
        email: [{
          required: true,
          validator: validate,
          trigger: "blur",
          regax: [
            {
              message: "请输入正确的邮箱格式",
              ruleFormat:
                "/^([a-zA-Z0-9._-])+@([a-zA-Z0-9_-])+((\\.[a-zA-Z0-9_-]{2,3}){1,2})$/"
            }
          ]
        }]
      },
      roleList: [],
      expertTypeList: [],
      insuranceTypesList: [
        {dicItemCode: "企财-财产", dicItemName: "企财-财产"},
        {dicItemCode: "企财-机器损坏险", dicItemName: "企财-机器损坏险"},
        {dicItemCode: "企财-利润损失险", dicItemName: "企财-利润损失险"},
        {dicItemCode: "企财-建筑安装工程", dicItemName: "企财-建筑安装工程"},
        {dicItemCode: "企财-货物运输", dicItemName: "企财-货物运输"},
        {dicItemCode: "人员-员福", dicItemName: "人员-员福"},
        {dicItemCode: "人员-团意", dicItemName: "人员-团意"},
        {dicItemCode: "人员-高医", dicItemName: "人员-高医"},
        {dicItemCode: "责任-雇主", dicItemName: "责任-雇主"},
        {dicItemCode: "责任-安责", dicItemName: "责任-安责"},
        {dicItemCode: "责任-董责", dicItemName: "责任-董责"},
        {dicItemCode: "责任-公众责任", dicItemName: "责任-公众责任"},
        {dicItemCode: "责任-其他", dicItemName: "责任-其他"},
        {dicItemCode: "信用保证-信用", dicItemName: "信用保证-信用"},
        {dicItemCode: "信用保证-保证", dicItemName: "信用保证-保证"}
      ],
      industryTypesList: [],
      relationTypeList: [],
      disabled: true,
      organData: [],
      organProps: {
        label: 'orgName',
        value: 'orgCode',
        children: 'children',
        expandTrigger: 'hover',
        checkStrictly: true
      },
      showRole:false,
      selectionRoleSwitch:false,
      roleTableData:[],
      roleInitParam:{
        pageNum: 1,
        pageSize: 10,
        param: {
          userId:"",
        }
      },
      roleTotal:0,
      nickName:"",
      belongOrgNamePath:"",
      roleName:"",
      orgAddData: {
        orgType: "org",
        userOrgs: [],
        userId: "",
        tenantId: ""
      },
      orgProps:{
        label: 'orgName',
        children: 'children'
      },
      legalOrgList:[],
      showOrg:false,
      checkedOrgList: [],
      selectInsuranceTypes:[],
      selectIndustryTypes:[],
      deleteData:{
        userId:""
      }
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    tenantId() {
      return this.$store.state.layoutStore.currentLoginUser.tenantId;
    }
  },
  filters: {
    getRoleType(list, roleType) {
      let index = list.findIndex((v) => v.dicItemCode == roleType);
      return index > -1 ? list[index].dicItemName : "-";
    }
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    async getDicFun() {
      let roleListTemp = await api.getRoleList({});
      // 角色列表查询
      // this.roleList = [];
      // if(roleListTemp.length && roleListTemp.length > 0) {
      //   roleListTemp.forEach(item => {
      //     this.roleList.push({
      //       dicItemName: item.roleName,
      //       dicItemCode: item.id
      //     })
      //   });
      // }

      // 机构专家身份
      let expertTypeListTemp = await getDicItemList("elms.expert.type");
      expertTypeListTemp.forEach(item => {
        this.expertTypeList.push({
          dicItemName: item.dicItemName,
          dicItemCode: parseInt(item.dicItemCode)
        });
      });
      // 状态
      await getDicItemList("sys.gateway.status");
      this.relationTypeList = await getDicItemList("elms.user.belong.type");
      // 角色名称
      this.searchFormTemp[1].list = this.roleList;
      this.getDeptTree();

      this.legalOrgList = await api.findLegalOrgData({});

    },
    async initData() {
      this.initParam.param.tenantId = this.tenantId;
      let res = await api.getUserList(this.initParam);
      if (res) {
        this.total = res.total;
        this.tableData = [];
        if (res.list) {
          this.tableData = res.list ? res.list : [{}];
        }
      }
    },
    async handleTool(item) {
      if (item.name == "新增人员") {
        // this.isAdd = true;
        // this.popupTitle = "新增人员";
        // this.selectIndustryTypes = [];
        // this.selectInsuranceTypes = [];
        // this.addData = _.cloneDeep(this.$options.data().addData);
        // await this.getDeptTree();
        // await this.initIndustryData();
        // this.showPopup = true;
        this.$router.push({
          name:"userAdd",
          query:{}
        });
      }
    },
    async initIndustryData() {
      let res = await api.findIndustryList({"level": 2});
      if (res) {
        // 擅长行业
        res.forEach(item => {
          this.industryTypesList.push({
            dicItemName: item.name,
            dicItemCode: item.code
          });
        });
      }
    },
    closeUserPopup() {
      this.selectOrgValue = [];
      // this.organData = [];
      // this.getDeptTree();
      this.cascaderKey = 0;
      this.showPopup = false;
      this.disabled = true;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      });
    },

    closeDetailPopup() {
      this.showDetailPopup = false;
    },

    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },
    async view(row) {
      let res = await api.detail({ id: row.id,tenantId:this.tenantId });
      this.addData = this._.cloneDeep(res);
      // this.selectOrgValue = this.addData.organCodePath.split("/");
      await this.loadSelectData();
      await this.initIndustryData();
      this.showDetailPopup = true;
    },
    async update(row) {
      // await this.initIndustryData();
      // this.isAdd = false;
      // this.popupTitle = "修改人员";
      // let res = await api.detail({ id: row.id,tenantId:this.tenantId });
      // this.addData = this._.cloneDeep(res);
      // await this.loadSelectData();

      this.$router.push({
        name:"userAdd",
        query:{
          id: row.id,
          tenantId:this.tenantId
        }
      });

      // if (this.addData.relationType == 'dept') {
      //   await this.getDeptTree();
      // } else {
      //   await this.getOrganTree();
      // }
      // this.selectIndustryTypes = [];
      // this.selectInsuranceTypes = [];
      //
      // this.selectOrgValue = this.addData.organCodePath.split("/");
      // if (res.insuranceTypes && res.insuranceTypes.length > 0) {
      //   this.selectInsuranceTypes = this.addData.insuranceTypes.split(",");
      // }
      // if (res.industryTypes && res.industryTypes.length > 0) {
      //   this.selectIndustryTypes = this.addData.industryTypes.split(",");
      // }
      this.showPopup = true;
    },
    async loadSelectData() {
      this.selectIndustryTypes = [];
      this.selectInsuranceTypes = [];

      if (this.addData.relationType == 'dept') {
        await this.getDeptTree();
      } else if (this.addData.relationType == 'org') {
        await this.getOrganTree();
      }
      if(this.addData.organCodePath && this.addData.organCodePath !='') {
        this.selectOrgValue = this.addData.organCodePath.split("/");
      }

      if (this.addData.insuranceTypes && this.addData.insuranceTypes !='') {
        this.selectInsuranceTypes = this.addData.insuranceTypes.split(",");
      }
      if (this.addData.industryTypes && this.addData.industryTypes !='') {
        this.selectIndustryTypes = this.addData.industryTypes.split(",");
      }
    },
    setStatus(row, val) {
      this.statusData.id = row.id;
      this.statusData.userTenantId = row.userTenantId;
      this.statusData.status = val;
      // this.stopPopup = true;
      this.statusTitle = val == '1' ? '启用' : '禁用';
      this.showStatusPopup = true;
    },
    closeStatusPopup() {
      this.statusData = _.cloneDeep(this.$options.data().statusData);
      this.showStatusPopup = false;
    },
    async confirmStatus() {
      let res = await api.setUserStatus(this.statusData);
      if (res) {
        let msg = this.statusData.status == '1'?'启用成功':'禁用成功';
        this.$message.success(msg);
      }
      this.showStatusPopup = false;
      this.initData();
    },
    async showRolePopup(row) {
      this.nickName = row.nickName;
      this.belongOrgNamePath = row.organNamePath;
      this.roleInitParam.param.userId = row.userId;
      this.roleInitParam.param.tenantId = this.tenantId;
      this.getRolePage();
    },
    async getRolePage() {
      let res = await getRolePage(this.roleInitParam);
      let userRoles = await api.findRolesByUserId(this.roleInitParam.param);
      if (res) {
        this.roleTotal = res.total;
        this.roleTableData = [];
        if (res.list) {

          let roleIds = [];
          if (userRoles.length && userRoles.length > 0) {
            userRoles.forEach(item => {
              roleIds.push(item.roleId);
            });
          }

          this.roleTableData = res.list ? res.list : [{}];
          setTimeout(() => {
            res.list.forEach(item => {
              if (roleIds.indexOf(item.id) != -1) this.$refs.roleTable.toggleRowSelection(item);
            });
            this.selectionRoleSwitch = true;
          }, 100);
        }
        this.showRole = true;
      }
    },
    async confirmStop(row) {
      if(this.statusData.stopReason == '') {
        this.$message.error("禁用原因不能为空");
        return;
      }
      let res = await api.setUserStatus(this.statusData);
      if (res) {
        this.$message.success("禁用成功");
      }else {
        this.$message.error("禁用失败");
      }
      this.stopPopup = false;
      this.statusData.stopReason = "";
      this.initData();
    },
    closeStopPopup() {
      this.stopPopup = false;
    },
    async confirm() {

      if (!validateAlls(this.$refs.addForm)) { return; }

      if(this.addData.relationType !='out') {
        if(this.addData.organCode == '') {
          this.$message.error("人员归属机构/部门不能为空");
          return;
        }
      }
      let insuranceTypesArr = [];
      let insuranceNamesArr = [];
      if (this.selectInsuranceTypes.length > 0) {
        // this.selectInsuranceTypes.forEach(code => {
        //   let op = _.find(this.industryTypesList, item => {
        //     return item.dicItemCode == code;
        //   });
        //   if(op) {
        //     insuranceNamesArr.push(op.dicItemName);
        //   }
        // });
        this.addData.insuranceTypes = this.selectInsuranceTypes.join(",");
        this.addData.insuranceNames = this.selectInsuranceTypes.join(",");
      }

      let industryTypesArr = [];
      let industryNamesArr = [];
      if(this.selectIndustryTypes.length > 0) {
        this.selectIndustryTypes.forEach(code => {
          let op = _.find(this.industryTypesList, item => {
            return item.dicItemCode == code;
          });
          if(op) {
            industryNamesArr.push(op.dicItemName);
          }
        });
        this.addData.industryTypes = this.selectIndustryTypes.join(",");
        this.addData.industryNames = industryNamesArr.join(",");
      }
      let param = this._.cloneDeep(this.addData);
      param.tenantId = this.tenantId;
      // if (this.selectOrgValue.length > 0) {
      //   param.organCode = this.selectOrgValue[this.selectOrgValue.length - 1];
      // } else {
      //   param.organCode = "";
      // }
      let res = await api.saveOrUpdate(param);
      console.log(res,"-----------------------")
      if (res) {
        this.$message({
          type: "success",
          message: this.isAdd ? "新增成功" : "修改成功"
        });
        this.initData();
        this.showPopup = false;
      }
    },
    del(row) {
      this.deleteData.userId = row.userId;
      this.showDelPopup = true;
    },
    async delHandle() {
      let res = await api.deleteUser(this.deleteData);
      if (res) {
        this.$message({
          type: "success",
          message: "删除成功"
        });
      }
      this.initData();
      this.showDelPopup = false
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    },
    async changeInput(val) {
      if (this.isAdd) {
        if (!val || val == '') {
          this.disabled = true;
          return;
        }
        let res = await api.findByBscUserName({ bscUserName: val, tenantId: this.tenantId });
        if (res && res.userId) {
          this.addData.email = res.email;
          this.addData.userId = res.userId;
          this.addData.nickName = res.nickName;
          this.addData.phone = res.phone;
          this.addData.personDesc = res.personDesc;
          if (res.ehrUserCode) {
            this.addData.ehrUserCode = res.ehrUserCode
          }
          if (res.agentCode) {
            this.addData.agentCode = res.agentCode;
          }

          if (res.insuranceTypes && res.insuranceTypes.length > 0) {
            this.selectInsuranceTypes = res.insuranceTypes.split(",");
          }
          if (res.industryTypes && res.industryTypes.length > 0) {
            this.selectIndustryTypes = res.industryTypes.split(",");
          }
          this.disabled = true;
        } else {
          this.addData = {
            ..._.cloneDeep(this.$options.data().addData),
            bscUserName: val
          }
          this.disabled = true;
        }
      }
    },
    async changeOrganRadio(val) {
      ++this.cascaderKey;
      this.selectOrgValue = [];
      if (val == "org") {
        await this.getOrganTree();
      } else if (val == "dept") {
        await this.getDeptTree();
      }
    },
    async getOrganTree() {
      this.organData = [];
      let res = await api.findOrganizationData({});
      if (res) {
        let treeData = _.cloneDeep(res);
        this.organData = treeData;
      }
    },
    async getDeptTree() {
      this.organData = [];
      let res = await api.findDepartmentData({});
      if (res) {
        let treeData = _.cloneDeep(res);
        this.organData = treeData;
      }
    },
    handleCascader(name, val) {
      let dtCascader = this.$refs["dt-cascader"]
      this.$nextTick(() => {
        let textArr = [];
        let arr = dtCascader.getCheckedNodes()[0].pathNodes;
        arr.forEach((i) => {
          textArr.push(i.label)
        });
        this.addData.organCode = name[name.length - 1];
        this.addData.organName = textArr[textArr.length - 1];
        this.addData.organCodePath = this.selectOrgValue.join("/")
        this.addData.organNamePath = textArr.join("/");
      })

    },
    async handleSelectionChange(val,op) {
      if (!this.selectionRoleSwitch) return;
      if(val && val.length > 0) {
        let roleIds = [];
        val.forEach(item => {
          roleIds.push(item.id);
        });
       let res = await api.addUserRole({
          roleIds:roleIds,
          userId:this.roleInitParam.param.userId
        });
      } else if (val.length == 0) {// 取消全选
        let roleIds = this.roleTableData.map(item => {
          return item.id;
        });
        let res = await api.deleteUserRole({
          roleIds:roleIds,
          userId:this.roleInitParam.param.userId
        });
      }
    },
    async chooseRoleCheck(data, row) {
      this.selectionRoleSwitch = false;
      let isSelected = false;
      if(data.length > 0) {
        data.forEach(item => {
          if(item.id == row.id) {
            isSelected = true;
          }
        });
      }
      if (isSelected) {
        let res = await api.addUserRole({
          roleId: row.id,
          userId: this.roleInitParam.param.userId
        });
      } else {
        let res = await api.deleteUserRole({
          roleId: row.id,
          userId: this.roleInitParam.param.userId
        });
      }
    },
    handleRoleSizeChange(val) {
      this.selectionRoleSwitch = false;
      this.roleInitParam.pageSize = val;
      this.getRolePage();
    },
    handleRoleCurrentChange(val) {
      this.selectionRoleSwitch = false;
      this.roleInitParam.pageNum = val;
      this.getRolePage();
    },
    cancelRole() {
      this.selectionRoleSwitch = false;
      this.showRole = false;
      this.roleInitParam.pageNum = 1;
      this.roleInitParam.pageSize = 10;
      this.initData();
    },
    async showOrgAuthPopup(row) {
      this.nickName = row.nickName;
      this.belongOrgNamePath = row.organNamePath;
      this.roleName = row.roleName;
      this.orgAddData.userId = row.userId;
      if (row.orgType) {
        this.orgAddData.orgType = row.orgType;
      }

      this.checkedOrgList = [];
      if (!row.orgType || row.orgType == 'org') {
        // this.legalOrgList = await api.findLegalOrgData({});
        let res = await api.getCheckedUserOrg({
          userId: this.orgAddData.userId
        });
        if (res && res instanceof Array) {
          this.checkedOrgList = res;
        }
        setTimeout(() => {
          this.$refs.orgTree.setCheckedKeys(this.checkedOrgList);
        }, 100);
      }
      this.showOrg = true;
    },
    cancelOrg(){
      this.showOrg = false;
    },
    async saveUserOrg() {
      if (this.orgAddData.orgType == 'org') {
        let checkedNodes = this.$refs.orgTree.getCheckedNodes(true);
        if (checkedNodes.length == 0) {
          this.$message.error("机构不能为空");
          return;
        }
        this.orgAddData.userOrgs = checkedNodes;
      }
      this.orgAddData.tenantId = this.tenantId;
      let res = await api.saveOrUpdateUserOrg(this.orgAddData);
      if (res) {
        this.$message.success("配置成功");
      }
      this.showOrg = false;
      this.initData();
    },
    async changeOrgTypeRadio(val) {
      if (val == 'org') {
        this.legalOrgList = await api.findLegalOrgData({});
      }
    },
  }
};
</script>
<style lang="less">
.user-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }

  }
}

// 启用禁用弹窗样式
.status-confirm {
  padding: 0 20px 20px 20px;

  .confirm-content {
    margin-bottom: 20px;
    text-align: center;

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .confirm-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
  }
}

.check-popup {
  width: 100%;

  .btn-wrap {
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: center;

    .btn-width {
      width: 120px;
    }
  }

  .end-exam-text {
    margin-top: 10px;
  }
}

.roleDialog {
  .roleHeader {
    display: flex;
    margin-bottom: 15px;
    height: 38px;
    line-height: 38px;
    padding-left: 40px;
  }

  .roleFooter {
    text-align: center;
    padding: 30px 0;
    button {
      width: 180px;
    }
  }

  .el-dialog {
    padding: 0;
  }

  .roleDialogWrap {
    max-height: 45vh;
    overflow: auto;
  }

}

.orgDialog {
  .orgHeader {
    display: flex;
    margin-bottom: 15px;
    height: 38px;
    line-height: 38px;
    padding-left: 40px;
  }

  .orgFooter {
    text-align: center;
    padding: 30px 0;
    button {
      width: 180px;
    }
  }

  .orgDialogWrap {
    margin-bottom: 14px;
  }

}

.form-block {
  padding-left: 0px;
}
</style>

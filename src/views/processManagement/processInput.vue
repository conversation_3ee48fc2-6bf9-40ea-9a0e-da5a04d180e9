<template>
  <div class="process-input">
    <el-breadcrumb class="breadcrumb dt-bread" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: 'processList' }"> 流程管理 </el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: themeObj.color }">
        流程详情
      </el-breadcrumb-item>
    </el-breadcrumb>
    <TableToolTemp :toolListProps="{ toolTitle: getToolTitle() }" class="log-tool">
    </TableToolTemp>
    <div class="process-wrap">


      <el-form :model="form" :rules="rules" ref="form" label-width="100px" class="process-form"
        style="margin: 32px 0 24px 0; max-width: 600px;" v-if="!isDefaultProcess">
        <el-form-item label="流程名称" prop="name" required>
          <el-input v-model.trim="form.name" maxlength="20" show-word-limit placeholder="请输入流程名称（0/20字）"
            :disabled="isViewMode" />
        </el-form-item>
        <el-form-item label="流程说明" prop="desc">
          <el-input v-model.trim="form.desc" type="textarea" :rows="2" maxlength="100" show-word-limit
            placeholder="请输入流程说明（0/100字）" :disabled="isViewMode" />
        </el-form-item>
      </el-form>

      <!-- 前端录入条件区域 -->
      <div class="condition-section" v-if="!isDefaultProcess">
        <div class="section-title">前端录入</div>
        <div class="section-desc">当满足以下条件，执行此流程</div>

        <!-- 企业条件 -->
        <el-card class="condition-form-card">
          <div slot="header" class="clearfix">
            <span>企业条件</span>
            <el-radio-group v-model="condition.enterprise.limitType" size="mini" style="float: right;"
              :disabled="isViewMode">
              <el-radio-button label="limit">限制</el-radio-button>
              <el-radio-button label="unlimited">不限制</el-radio-button>
            </el-radio-group>
          </div>
          <el-form label-width="120px" :model="condition.enterprise">
            <div v-if="condition.enterprise.limitType === 'limit'">
              <el-form-item label="企业所在地">
                <el-cascader v-model="condition.enterprise.location" :options="locationOptions"
                  :props="{ multiple: true, checkStrictly: true }" clearable filterable placeholder="请选择企业所在地"
                  style="width: 400px" :disabled="isViewMode" />
              </el-form-item>
              <el-form-item label="企业所属行业">
                <el-cascader v-model="condition.enterprise.industry" :options="industryOptions"
                  :props="{ multiple: true, checkStrictly: true }" clearable filterable placeholder="请选择企业所属行业"
                  style="width: 400px" :disabled="isViewMode" />
              </el-form-item>
              <el-form-item label="企业规模">
                <el-select v-model="condition.enterprise.scale" multiple placeholder="请选择企业规模" style="width: 400px"
                  :disabled="isViewMode">
                  <el-option v-for="item in scaleOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </el-card>

        <!-- 顾问条件 -->
        <el-card class="condition-form-card">
          <div slot="header" class="clearfix">
            <span>顾问条件</span>
            <el-radio-group v-model="condition.consultant.limitType" size="mini" style="float: right;"
              :disabled="isViewMode">
              <el-radio-button label="limit">限制</el-radio-button>
              <el-radio-button label="unlimited">不限制</el-radio-button>
            </el-radio-group>
          </div>
          <el-form label-width="120px" :model="condition.consultant">
            <div v-if="condition.consultant.limitType === 'limit'">
              <el-form-item label="顾问所属机构">
                <el-select v-model="condition.consultant.org" filterable  @change="handleGetTransactionsListFun" multiple
                  placeholder="请选择顾问所属机构" style="width: 400px" :disabled="isViewMode" >
                  <el-option v-for="item in orgOptions" :key="item.value" :label="item.orgName" :value="item.orgCode" />
                </el-select>
              </el-form-item>
              <el-form-item label="所属营业部">
                <el-select v-model="condition.consultant.dept" multiple placeholder="请选择所属营业部" style="width: 400px"
                  :disabled="isViewMode">
                  <el-option v-for="item in deptOptions" :key="item.value" :label="item.orgName"
                    :value="item.orgCode" />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </el-card>
      </div>


      <!-- 机会管理流程步骤区域 -->
      <div class="step-section">
        <div class="section-title">机会管理</div>
        <div class="section-desc">配置机会管理流程步骤</div>

        <el-card v-for="(step, stepIdx) in steps" :key="step.id" class="step-card">
          <div class="step-header">
            <div class="step-header-left">
              <span class="step-index">第{{ stepIdx + 1 }}步</span>
            </div>
            <div class="step-header-right">
              <el-button v-if="stepIdx > 0 && !isDefaultProcess && !isViewMode" icon="el-icon-delete" size="mini"
                type="danger" @click="confirmRemoveStep(stepIdx)">删除此步</el-button>
            </div>
          </div>

          <div class="step-content">
            <el-form label-width="120px">
              <el-form-item label="机会类型">
                <el-select v-model="step.type" placeholder="请选择机会类型" style="width: 400px"
                  @change="handleTypeChange(step, stepIdx)" :disabled="isDefaultProcess || isViewMode">
                  <el-option v-for="item in opportunityTypes" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="步骤名称">
                <el-input v-model.trim="step.name" style="width: 400px" placeholder="请输入步骤名称"
                  maxlength="10" show-word-limit
                  :disabled="isDefaultProcess || isViewMode" />
              </el-form-item>
            </el-form>

            <div class="step-items">
              <div class="items-title">执行以下操作：</div>
              <el-form label-width="120px">
                <el-form-item v-if="stepIdx === 0" label="系统分配">
                  <div class="system-assign-btn-wrap">
                    <div v-if="isDefaultProcess" :style="{ color: themeObj.color }" class="default-assign-text">
                      默认：顾问所属机构全部统筹角色
                    </div>
                    <template v-else>
                      <el-button v-if="!isViewMode" type="primary" size="small"
                        @click="showSystemAssignDialog">选择分配机会</el-button>
                      <span v-if="systemAssignDialog.displaySelected" :style="{ color: themeObj.color }"
                        class="selected-summary">已选：{{
                          systemAssignDialog.displaySelected }}</span>
                    </template>
                  </div>
                </el-form-item>

                <div style="margin-bottom: 10px;" v-for="(item, itemIdx) in step.items" :key="item.id">
                  <el-form-item label="执行事项">
                    <div class="execution-item-wrap">
                      <el-button v-if="!isViewMode" type="primary" size="small"
                        @click="showExecutionItemDialog(stepIdx, itemIdx)">选择执行事项</el-button>
                      <span v-if="item.selectedItem" :style="{ color: themeObj.color }" class="selected-summary">已选：{{
                        item.selectedItem.name }}（{{ item.selectedItem.required ? '必须' : '可选' }}）</span>
                      <el-button v-if="itemIdx > 0 && !isViewMode" icon="el-icon-delete" size="mini" type="danger"
                        @click="confirmRemoveItem(stepIdx, itemIdx)"></el-button>
                    </div>
                  </el-form-item>
                </div>

                <el-form-item v-if="!isViewMode">
                  <el-button size="small" type="primary" icon="el-icon-plus"
                    @click="addItem(stepIdx)">添加执行事项</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>

        <div class="step-actions" v-if="!isDefaultProcess && !isViewMode">
          <el-button type="primary" icon="el-icon-plus" @click="addStep">增加步骤</el-button>
          <el-button type="primary" @click="handleSave">确认保存</el-button>
        </div>
        <div class="step-actions" v-else-if="!isViewMode">
          <el-button type="primary" @click="handleSave">确认保存</el-button>
        </div>
      </div>

      <!-- 系统分配机会弹窗 -->
      <DtPopup :isShow.sync="systemAssignDialog.visible" :title="'系统分配机会'" :width="'900px'"
        @confirm="handleSystemAssignConfirm" @close="systemAssignDialog.visible = false">
        <div class="system-assign-content">
          <el-form label-width="100px" class="assign-form">
            <el-form-item label="分配角色">
              <el-input value="分公司统筹" disabled style="width: 200px;" />
            </el-form-item>
            <el-form-item label="机构范围">
              <el-radio-group v-model="systemAssignDialog.orgRange" @change="handleOrgRangeChange">
                <el-radio label="1">全国机构</el-radio>
                <el-radio label="2">顾问所属机构</el-radio>
                <el-radio label="3">自定义人员</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div v-if="systemAssignDialog.orgRange === '3'" class="custom-user-section">
            <SearchForm :searchForm="systemAssignDialog.searchForm" :searchFormTemp="systemAssignDialog.searchFormTemp"
              @normalSearch="handleUserSearch" @normalResetQuery="resetUserSearch" />
            <div class="table-wrapper">
              <el-table :data="systemAssignDialog.userList" style="width: 100%;" @current-change="handleSystemAssignUserSelect">
                <el-table-column label="" width="50" align="center">
                  <template slot-scope="scope">
                    <input type="radio" :name="'userSelect'" :value="scope.row.nickName"
                      v-model="systemAssignDialog.selectedUserName" style="margin: 0;">
                  </template>
                </el-table-column>
                <el-table-column prop="nickName" label="人员姓名" align="center" />
                <el-table-column prop="phone" label="手机号" align="center" />
                <el-table-column prop="email" label="邮箱" align="center" />
                <el-table-column prop="organName" label="人员归属" align="center" />
              </el-table>
            </div>
            
            <!-- 选中人员信息显示 -->
            <div class="selected-user-info" v-if="systemAssignDialog.selectedUserInfo">
              <div class="info-item">
                <span class="label">人员姓名：</span>
                <span class="value">{{ systemAssignDialog.selectedUserInfo.nickName }}</span>
              </div>
              <div class="info-item">
                <span class="label">已参与机会：</span>
                <span class="value">{{ systemAssignDialog.selectedUserInfo.opportunityCount }}</span>
              </div>
              <div class="info-item">
                <span class="label">待处理机会：</span>
                <span class="value">{{ systemAssignDialog.selectedUserInfo.taskCount }}</span>
              </div>
            </div>
          </div>
        </div>
      </DtPopup>

      <!-- 执行事项选择弹窗 -->
      <DtPopup :isShow.sync="executionItemDialog.visible" :title="'选择执行事项'" :width="'800px'"
        @confirm="handleExecutionItemConfirm" @close="handleExecutionItemDialogClose">
        <div class="execution-item-content">
          <div class="table-container">
            <el-table :data="executionItemDialog.itemList" style="width: 100%;">
              <el-table-column label="" width="50" align="center">
                <template slot-scope="scope">
                  <input type="radio" :name="'executionItem'" :value="scope.row.name"
                    v-model="executionItemDialog.selectedItem" style="margin: 0;">
                </template>
              </el-table-column>
              <el-table-column prop="name" label="事项名称" align="center" />
              <el-table-column label="必须/可选" align="center" width="200">
                <template slot-scope="scope">
                  <el-radio-group v-model="scope.row.required" size="mini">
                    <el-radio :label="true">必须</el-radio>
                    <el-radio :label="false">可选</el-radio>
                  </el-radio-group>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </DtPopup>

      <!-- 删除确认弹窗 -->
      <DtPopup :isShow.sync="deleteConfirmDialog.visible" @close="deleteConfirmDialog.visible = false" center :footer=" false " width="30% " >
        <div class="check-popup">
          <div style="text-align: center">{{ deleteConfirmDialog.message }}</div>
          <div class="btn-wrap">
            <el-button type="primary" class="btn-width"
              :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px' }"
              @click.stop="deleteConfirmDialog.visible = false">取消</el-button>
            <el-button type="primary" class="btn-width"
              :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color }"
              @click.stop="confirmRemoveItemHandle">确认</el-button>
          </div>
        </div>
      </DtPopup>


    </div>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import DtPopup from "@/components/layouts/DtPopup";
import SearchForm from "@/components/layouts/SearchForm";
import { saveProcessDefine, getProcessDefineDetail, getBasecodeTree, getLegalList, getTransactionsList, getBranchUsers ,getIndustryTree,getEnterpriseTypeEnum} from "@/api/processManagement";
import { getDicItemList } from "@/config/tool.js";
import { countUserParticipatedOpportunities } from "@/api/workbench";
export default {
  name: "processInput",
  provide() {
    return {
    };
  },
  components: {
    TableToolTemp,
    DtPopup,
    SearchForm
  },
  data() {
    return {
      form: {
        name: '',
        desc: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入流程名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        desc: [
          { max: 100, message: '最多100个字符', trigger: 'blur' }
        ]
      },
      // mock条件数据
      condition: {
        enterprise: {
          limitType: 'limit', // 'limit' or 'unlimited'
          location: [],
          industry: [],
          scale: []
        },
        consultant: {
          limitType: 'unlimited',
          org: [],
          dept: []
        }
      },
      locationOptions: [],
      industryOptions: [],
      scaleOptions: [],
      orgOptions: [],
      deptOptions: [],
      selectDialog: {
        visible: false,
        type: '',
        title: '',
        options: [],
        temp: []
      },
      steps: [
        {
          id: 1,
          type: '', // 机会类型
          name: '', // 机会名称
          items: [
            {
              id: 1,
              type: 'custom',
              selectedItem: null
            }
          ] // 执行事项
        }
      ],
      opportunityTypes: [
        { value: '1', label: '统筹跟进' },
        { value: '2', label: '立项组队' },
        { value: '3', label: '分配比例' },
        { value: '4', label: '投标阶段' },
        { value: '5', label: '客户授权' },
        { value: '6', label: '询价阶段' },
        { value: '7', label: '排分阶段' },
        { value: '8', label: '成交出单' },
        { value: '9', label: '服务阶段' }
      ],
      executionItemOptions: [
        { value: 'action1', label: '配置项目成员' },
        { value: 'action2', label: '维护投标信息' },
        { value: 'action3', label: '上传标书文件' }
      ],
      systemAssignDialog: {
        visible: false,
        orgRange: '',
        searchForm: {
          pageNum: 1,
          pageSize: 10,
          param: {
            roleType: '1',
            organCode: "",
            nickName: ''
          }
        },
        searchFormTemp: [
          { label: '分配对象', name: 'roleType', type: 'select', width: '140px', list: [{ dicItemCode: '1', dicItemName: '分公司统筹' }],clearable:false },
          { label: '机构', name: 'organCode', type: 'select', width: '140px', list: [] },
          { label: '人员姓名', name: 'nickName', type: 'input', width: '140px' }
        ],


        userList: [

        ],
        selectedUser: null,
        selectedUserName: '',
        displaySelected: '',
        selectedUserInfo: null // 新增：用于存储选中人员的信息
      },
      executionItemDialog: {
        visible: false,
        currentStepIdx: -1,
        currentItemIdx: -1,
        selectedItem: null,
        itemList: [
          { name: '领取机会', value: '1', required: true },
          { name: '配置项目成员', value: '2', required: true },
          { name: '配置项目分工/比例', value: '3', required: true },
          { name: '确认分工/比例', value: '4', required: true },
          { name: '配置生态服务', value: '5', required: true },
          { name: '维护投标信息', value: '6', required: true },
          { name: '上传标书文件', value: '7', required: false },
          { name: '上传经济委托授权书文件', value: '8', required: true },
          { name: '上传询价记录文件', value: '9', required: true },
          { name: '上传排分结果文件', value: '10', required: true },
          { name: '填写成交保单号', value: '11', required: true },
          { name: '二次确认分工/比例', value: '12', required: true },
          { name: '关闭机会', value: '13', required: true },
          { name: '暂停机会', value: '14', required: true },
          { name: '指派统筹', value: '15', required: true },
          { name: '指派项目经理', value: '16', required: true }
        ]
      },
      deleteConfirmDialog: {
        visible: false,
        message: '',
        stepIdx: -1,
        itemIdx: -1,
        type: '' // 'step' 或 'item'
      },
      nextStepId: 2,
      nextItemId: 4,

      processJson: {
        "agentLegalCode": "", //顾问所属机构 格式是字符串 多个用逗号隔开 ''sss','aaa''
        "agentTradingCenterCode": "", // 所属营业部 格式是字符串 多个用逗号隔开 ''sss','aaa''
        "bpmJson": {
          "flows": [
            {
              "id": "", //连线的id  自己生成uuid提交
              "name": "线",  //写死 
              "sourceRef": "", // 连线的起点
              "targetRef": "" // 连线的终点
            }
          ],
          "nodes": [
            {
              "id": "", // 开始节点的id 自己生成uuid提交
              "name": "开始节点", // 写死
              "type": "startEvent" // startEvent 开始节点 写死
            },
            {
              "id": "", // 节点的id 自己生成uuid提交
              "name": "", // 机会名称
              "properties": {
                // 系统分配机会弹窗选择的自定义人员对象
                "assignee": {
                  "userId": "", // 自定义人员用户userId
                  "userName": "" // 自定义人员用户名称
                },
                // 系统分配机会弹窗的机构范围对象
                "candidateGroups": {
                  "orgType": "", // 机构范围 1-全国机构 2-顾问所属机构 3-自定义人员
                  "roleType": "1" //  1-分公司统筹 写死
                },
                "dolist": [
                  {
                    "id": "", // 执行事项的value
                    "name": "", // 执行事项的名称
                    "required": true // 是否必填
                  }
                ],
                "stepType": 1 // 机会类型 1-统筹跟进 2-立项组队 3-分配比例 4-投标阶段 5-客户授权 6-询价阶段 7-排分阶段 8-成交出单 9-服务阶段
              },
              "type": "userTask" // userTask 用户任务节点 写死
            },
            {
              "id": "", // 结束节点的id 自己生成uuid提交
              "name": "结束节点", // 写死
              "type": "endEvent" // endEvent 结束节点 写死
            }
          ],
          "processName": "", // 流程名称
          "tenantId": this.$store.state.layoutStore.currentLoginUser.tenantId // 租户id
        },
        "companyAddrs": "", // 企业所在地 格式是字符串 多个用逗号隔开 ''sss/sss/sss','aaa/aaa/aaa''
        "companyIndustry": "", // 企业所属行业 格式是字符串 多个用逗号隔开 ''sss/sss/sss','aaa/aaa/aaa''
        "companyType": "", // 企业规模 格式是字符串 多个用逗号隔开 ''sss','aaa''
        "processDesc": "", // 流程说明
        "processName": "", // 流程名称
        "processStatus": "0", // 流程状态 新增流程传"0"  编辑以调用接口回显的processStatus为准
        "processType": "2", // 流程类型  1-默认 2-自定义
        "tenantId": this.$store.state.layoutStore.currentLoginUser.tenantId, // 租户id
      },




    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    isDefaultProcess() {
      return this.$route.query.processType === '1'; // 假设1表示默认流程
    },
    pageMode() {
      return this.$route.query.mode || 'add'; // add-新增, edit-编辑, view-查看
    },
    isViewMode() {
      return this.pageMode === 'view';
    },
    isEditMode() {
      return this.pageMode === 'edit';
    },
    isAddMode() {
      return this.pageMode === 'add';
    },
    isCopyMode() {
      return this.pageMode === 'copy';
    }
  },
  watch: {
    'condition.enterprise.limitType'(newVal) {
      if (newVal === 'unlimited') {
        this.condition.enterprise.location = [];
        this.condition.enterprise.industry = [];
        this.condition.enterprise.scale = [];
      }
    },
    'condition.consultant.limitType'(newVal) {
      if (newVal === 'unlimited') {
        this.condition.consultant.org = [];
        this.condition.consultant.dept = [];
      }
    }
  },
  created() {
    this.initData();
  },
  mounted() {

  },
  methods: {
    // 生成UUID的工具方法
    generateUUID() {
      // 生成标准UUID格式
      const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16).toUpperCase();
      });
      
      return 'sid-' + uuid;
    },

    // 构建提交给后端的数据结构
    buildProcessJson() {
      const processJson = {
        agentLegalCode: "",
        agentTradingCenterCode: "",
        bpmJson: {
          flows: [],
          nodes: [],
          processName: this.isDefaultProcess ? "默认流程" : this.form.name,
          tenantId: this.$store.state.layoutStore.currentLoginUser.tenantId
        },
        companyAddrs: "",
        companyIndustry: "",
        companyType: "",
        processDesc: this.form.desc,
        processName: this.isDefaultProcess ? "默认流程" : this.form.name,
        processStatus: this.isEditMode ? this.processJson.processStatus : "0",
        processType: this.isDefaultProcess ? "1" : "2",
        tenantId: this.$store.state.layoutStore.currentLoginUser.tenantId
      };

      // 如果是编辑模式，添加流程ID
      if (this.isEditMode && this.processJson.id) {
        processJson.id = this.processJson.id;
      }
      // 复制模式不添加ID，当作新增处理

      // 1. 构建节点数组（开始节点 + 业务节点 + 结束节点）
      const nodes = [];

      // 添加开始节点
      let startNodeId;
      if (this.isEditMode && this.processJson.bpmJson && this.processJson.bpmJson.nodes) {
        // 编辑模式：从现有数据中获取开始节点ID
        const startNode = this.processJson.bpmJson.nodes.find(node => node.type === 'startEvent');
        startNodeId = startNode ? startNode.id : this.generateUUID();
      } else {
        // 新增模式或复制模式：生成新的UUID
        startNodeId = this.generateUUID();
      }

      nodes.push({
        id: startNodeId,
        name: "开始节点",
        type: "startEvent"
      });

      // 添加业务节点
      this.steps.forEach((step, index) => {
        let nodeId;
        if (this.isEditMode && this.processJson.bpmJson && this.processJson.bpmJson.nodes) {
          // 编辑模式：从现有数据中获取业务节点ID
          const businessNodes = this.processJson.bpmJson.nodes.filter(node => node.type === 'userTask');
          nodeId = businessNodes[index] ? businessNodes[index].id : this.generateUUID();
        } else {
          // 新增模式或复制模式：生成新的UUID
          nodeId = this.generateUUID();
        }

        const node = {
          id: nodeId,
          name: step.name,
          properties: {
            assignee: {},
            candidateGroups: {
              orgType: "",
              roleType: "1"
            },
            dolist: [],
            stepType: step.type
          },
          type: "userTask"
        };

        // 处理系统分配配置（仅第一步）
        if (index === 0) {
          if (this.isDefaultProcess) {
            // 默认流程：设置为顾问所属机构全部统筹角色
            node.properties.candidateGroups.orgType = "1"; // 顾问所属机构
            node.properties.candidateGroups.roleType = ""; // 分公司统筹
          } else {
            // 自定义流程：处理机构范围
            if (this.systemAssignDialog.orgRange == '1') {
              node.properties.candidateGroups.orgType = "1";
            } else if (this.systemAssignDialog.orgRange == '2') {
              node.properties.candidateGroups.orgType = "2";
            } else if (this.systemAssignDialog.orgRange == '3') {
              node.properties.candidateGroups.orgType = "3";
              // 如果有自定义人员，设置assignee
              if (this.systemAssignDialog.selectedUser) {
                node.properties.assignee = {
                  userId: this.systemAssignDialog.selectedUser.userId || "",
                  userName: this.systemAssignDialog.selectedUser.nickName || ""
                };
              } else {
                // 如果没有selectedUser但有selectedUserName，尝试从userList中找到对应的用户
                if (this.systemAssignDialog.selectedUserName) {
                  const selectedUser = this.systemAssignDialog.userList.find(u => u.nickName === this.systemAssignDialog.selectedUserName);
                  if (selectedUser) {
                    node.properties.assignee = {
                      userId: selectedUser.userId || "",
                      userName: selectedUser.nickName || ""
                    };
                  }
                }
              }
            } else {
              // 如果没有选择任何机构范围，默认为全国机构
              node.properties.candidateGroups.orgType = "1";
            }
          }
        }

        // 处理执行事项
        step.items.forEach(item => {
          if (item.selectedItem) {
            node.properties.dolist.push({
              id: item.selectedItem.value,
              name: item.selectedItem.name,
              required: item.selectedItem.required
            });
          }
        });

        nodes.push(node);
      });

      // 添加结束节点
      let endNodeId;
      if (this.isEditMode && this.processJson.bpmJson && this.processJson.bpmJson.nodes) {
        // 编辑模式：从现有数据中获取结束节点ID
        const endNode = this.processJson.bpmJson.nodes.find(node => node.type == 'endEvent');
        endNodeId = endNode ? endNode.id : this.generateUUID();
      } else {
        // 新增模式或复制模式：生成新的UUID
        endNodeId = this.generateUUID();
      }

      nodes.push({
        id: endNodeId,
        name: "结束节点",
        type: "endEvent"
      });

      processJson.bpmJson.nodes = nodes;

      // 2. 构建连线数组
      const flows = [];
      if (this.isEditMode && this.processJson.bpmJson && this.processJson.bpmJson.flows) {
        // 编辑模式：使用现有的连线ID
        for (let i = 0; i < nodes.length - 1; i++) {
          const existingFlow = this.processJson.bpmJson.flows[i];
          flows.push({
            id: existingFlow ? existingFlow.id : this.generateUUID(),
            name: "线",
            sourceRef: nodes[i].id,
            targetRef: nodes[i + 1].id
          });
        }
      } else {
        // 新增模式或复制模式：生成新的连线ID
        for (let i = 0; i < nodes.length - 1; i++) {
          flows.push({
            id: this.generateUUID(),
            name: "线",
            sourceRef: nodes[i].id,
            targetRef: nodes[i + 1].id
          });
        }
      }
      processJson.bpmJson.flows = flows;

      // 3. 处理企业条件（仅自定义流程）
      if (!this.isDefaultProcess) {
        if (this.condition.enterprise.limitType == 'limit') {
          // 企业所在地
          if (this.condition.enterprise.location && this.condition.enterprise.location.length > 0) {
            processJson.companyAddrs = this.condition.enterprise.location
              .map(loc => loc.join('/'))
              .join(',');
          }

          // 企业所属行业
          if (this.condition.enterprise.industry && this.condition.enterprise.industry.length > 0) {
            processJson.companyIndustry = this.condition.enterprise.industry
              .map(ind => ind.join('/'))
              .join(',');
          }

          // 企业规模
          if (this.condition.enterprise.scale && this.condition.enterprise.scale.length > 0) {
            processJson.companyType = this.condition.enterprise.scale.join(',');
          }
        }
      }

      // 4. 处理顾问条件（仅自定义流程）
      if (!this.isDefaultProcess) {
        if (this.condition.consultant.limitType == 'limit') {
          // 顾问所属机构
          if (this.condition.consultant.org && this.condition.consultant.org.length > 0) {
            processJson.agentLegalCode = this.condition.consultant.org.join(',');
          }

          // 所属营业部
          if (this.condition.consultant.dept && this.condition.consultant.dept.length > 0) {
            processJson.agentTradingCenterCode = this.condition.consultant.dept.join(',');
          }
        }
      }

      return processJson;
    },

    // 从后端数据回显到前端（复制模式）
    parseProcessJsonForCopy(processJson) {
      if (!processJson) return;

      // 清除ID信息，当作新增处理
      const copyProcessJson = JSON.parse(JSON.stringify(processJson));
      delete copyProcessJson.id;
      if (copyProcessJson.bpmJson) {
        // 清除所有节点的ID，让系统重新生成
        if (copyProcessJson.bpmJson.nodes) {
          copyProcessJson.bpmJson.nodes.forEach(node => {
            delete node.id;
          });
        }
        if (copyProcessJson.bpmJson.flows) {
          copyProcessJson.bpmJson.flows.forEach(flow => {
            delete flow.id;
          });
        }
      }

      // 保存完整的流程数据，包括所有节点信息（但无ID）
      this.processJson = copyProcessJson;

      // 回显基本信息
      this.form.name = copyProcessJson.processName;
      this.form.desc = copyProcessJson.processDesc;

      // 回显企业条件（仅自定义流程）
      if (!this.isDefaultProcess) {
        if (copyProcessJson.companyAddrs || copyProcessJson.companyIndustry || copyProcessJson.companyType) {
          this.condition.enterprise.limitType = 'limit';
          if (copyProcessJson.companyAddrs) {
            this.condition.enterprise.location = copyProcessJson.companyAddrs.split(',').map(addr => addr.split('/'));
          }
          if (copyProcessJson.companyIndustry) {
            this.condition.enterprise.industry = copyProcessJson.companyIndustry.split(',').map(ind => ind.split('/'));
          }
          if (copyProcessJson.companyType) {
            this.condition.enterprise.scale = copyProcessJson.companyType.split(',');
          }
        } else {
          this.condition.enterprise.limitType = 'unlimited';
        }
      }

      // 回显顾问条件（仅自定义流程）
      if (!this.isDefaultProcess) {
        if (copyProcessJson.agentLegalCode || copyProcessJson.agentTradingCenterCode) {
          this.condition.consultant.limitType = 'limit';
          if (copyProcessJson.agentLegalCode) {
            this.condition.consultant.org = copyProcessJson.agentLegalCode.split(',');
          }
          if (copyProcessJson.agentTradingCenterCode) {
            this.condition.consultant.dept = copyProcessJson.agentTradingCenterCode.split(',');
          }
        } else {
          this.condition.consultant.limitType = 'unlimited';
        }
      }

      // 回显业务节点（跳过开始和结束节点）
      if (copyProcessJson.bpmJson && copyProcessJson.bpmJson.nodes) {
        const businessNodes = copyProcessJson.bpmJson.nodes.filter(node => node.type === 'userTask');
        this.steps = businessNodes.map((node, index) => {
          const step = {
            id: index + 1,
            type: node.properties.stepType.toString(),
            name: node.name,
            items: []
          };

          // 回显执行事项
          if (node.properties.dolist) {
            step.items = node.properties.dolist.map((item, itemIndex) => ({
              id: itemIndex + 1,
              type: 'custom',
              selectedItem: {
                name: item.name,
                value: item.id,
                required: item.required
              }
            }));
          }

          // 回显系统分配配置（仅第一步）
          if (index === 0) {
            const candidateGroups = node.properties.candidateGroups;
            if (candidateGroups) {
              if (candidateGroups.orgType == '1') {
                this.systemAssignDialog.orgRange = '1';
                this.systemAssignDialog.displaySelected = '分公司统筹-全国机构';
              } else if (candidateGroups.orgType == '2') {
                this.systemAssignDialog.orgRange = '2';
                this.systemAssignDialog.displaySelected = '分公司统筹-顾问所属机构';
              } else if (candidateGroups.orgType == '3') {
                this.systemAssignDialog.orgRange = '3';
                // 回显自定义人员
                if (node.properties.assignee && node.properties.assignee.userName) {
                  this.systemAssignDialog.selectedUser = {
                    name: node.properties.assignee.userName,
                    userId: node.properties.assignee.userId
                  };
                  this.systemAssignDialog.selectedUserName = node.properties.assignee.userName;
                  this.systemAssignDialog.displaySelected = `分公司统筹-${node.properties.assignee.userName}(${node.properties.assignee.userId})`;
                } else {
                  this.systemAssignDialog.displaySelected = '分公司统筹-自定义人员';
                }
              }
            }
          }

          return step;
        });
      }
    },

    // 从后端数据回显到前端
    parseProcessJson(processJson) {
      if (!processJson) return;

      // 保存完整的流程数据，包括ID和所有节点信息
      this.processJson = processJson;

      // 回显基本信息
      this.form.name = processJson.processName;
      this.form.desc = processJson.processDesc;

      // 回显企业条件（仅自定义流程）
      if (!this.isDefaultProcess) {
        if (processJson.companyAddrs || processJson.companyIndustry || processJson.companyType) {
          this.condition.enterprise.limitType = 'limit';
          if (processJson.companyAddrs) {
            this.condition.enterprise.location = processJson.companyAddrs.split(',').map(addr => addr.split('/'));
          }
          if (processJson.companyIndustry) {
            this.condition.enterprise.industry = processJson.companyIndustry.split(',').map(ind => ind.split('/'));
          }
          if (processJson.companyType) {
            this.condition.enterprise.scale = processJson.companyType.split(',');
          }
        } else {
          this.condition.enterprise.limitType = 'unlimited';
        }
      }

      // 回显顾问条件（仅自定义流程）
      if (!this.isDefaultProcess) {
        if (processJson.agentLegalCode || processJson.agentTradingCenterCode) {
          this.condition.consultant.limitType = 'limit';
          if (processJson.agentLegalCode) {
            this.condition.consultant.org = processJson.agentLegalCode.split(',');
          }
          if (processJson.agentTradingCenterCode) {
            this.condition.consultant.dept = processJson.agentTradingCenterCode.split(',');
          }
        } else {
          this.condition.consultant.limitType = 'unlimited';
        }
      }

      // 回显业务节点（跳过开始和结束节点）
      if (processJson.bpmJson && processJson.bpmJson.nodes) {
        const businessNodes = processJson.bpmJson.nodes.filter(node => node.type === 'userTask');
        this.steps = businessNodes.map((node, index) => {
          const step = {
            id: index + 1,
            type: node.properties.stepType.toString(),
            name: node.name,
            items: []
          };

          // 回显执行事项
          if (node.properties.dolist) {
            step.items = node.properties.dolist.map((item, itemIndex) => ({
              id: itemIndex + 1,
              type: 'custom',
              selectedItem: {
                name: item.name,
                value: item.id,
                required: item.required
              }
            }));
          }

          // 回显系统分配配置（仅第一步）
          if (index === 0) {
            const candidateGroups = node.properties.candidateGroups;
            if (candidateGroups) {
              if (candidateGroups.orgType == '1') {
                this.systemAssignDialog.orgRange = '1';
                this.systemAssignDialog.displaySelected = '分公司统筹-全国机构';
              } else if (candidateGroups.orgType == '2') {
                this.systemAssignDialog.orgRange = '2';
                this.systemAssignDialog.displaySelected = '分公司统筹-顾问所属机构';
              } else if (candidateGroups.orgType == '3') {
                this.systemAssignDialog.orgRange = '3';
                // 回显自定义人员
                if (node.properties.assignee && node.properties.assignee.userName) {
                  this.systemAssignDialog.selectedUser = {
                    name: node.properties.assignee.userName,
                    userId: node.properties.assignee.userId
                  };
                  this.systemAssignDialog.selectedUserName = node.properties.assignee.userName;
                  this.systemAssignDialog.displaySelected = `分公司统筹-${node.properties.assignee.userName}(${node.properties.assignee.userId})`;
                } else {
                  this.systemAssignDialog.displaySelected = '分公司统筹-自定义人员';
                }
              }
            }
          }

          return step;
        });
      }
    },

    async initData() {
      if (!this.isDefaultProcess) {
        await this.handleGetBasecodeTreeFun();
        await this.handleGetLegalListFun();
        await this.handleGetIndustryTreeFun();
        await this.handleGetEnterpriseTypeEnumFun();
      }
      // 如果是编辑/查看模式，加载数据
      if (this.isEditMode || this.isViewMode) {
        await this.loadEditData();
      } else if (this.isCopyMode) {
        // 如果是复制模式，加载原有流程数据并回显
        await this.loadCopyData();
      } else if (this.isDefaultProcess) {
        // 如果是默认流程，初始化9个步骤
        this.initDefaultSteps();
      }
    },


    async handleGetEnterpriseTypeEnumFun() {
      let res = await getEnterpriseTypeEnum();
      if (res) {
        this.scaleOptions = res.map(item => ({ value: item.code, label: item.name }));
      }
    },
    async handleGetLegalListFun() {
      let res = await getLegalList();
      if (res) {
        this.orgOptions = res;
        this.systemAssignDialog.searchFormTemp[1].list = res.map(item => ({ dicItemCode: item.orgCode, dicItemName: item.orgName }));
      }
    },

    async handleGetBasecodeTreeFun() {
      let res = await getBasecodeTree();
      if (res) {
        this.locationOptions = res;
      }
    },
    async handleGetIndustryTreeFun() {
      let res = await getIndustryTree();
      if (res) {
        this.industryOptions = res;
      }
    },
    handleGetTransactionsListFun() {
      this.handleGetTransactionsListFun()
    },

    async handleGetTransactionsListFun() {
      let res = await getTransactionsList({ orgCodes: this.condition.consultant.org });
      if (res) {
        this.deptOptions = res;
      }
    },

    getToolTitle() {
      if (this.isViewMode) {
        return this.isDefaultProcess ? '查看默认流程' : '查看自定义流程';
      } else if (this.isEditMode) {
        return this.isDefaultProcess ? '编辑默认流程' : '编辑自定义流程';
      } else if (this.isCopyMode) {
        return this.isDefaultProcess ? '复制默认流程' : '复制自定义流程';
      } else {
        return this.isDefaultProcess ? '新增默认流程' : '新增自定义流程';
      }
    },

    handleOrgRangeChange(val) {
      if (val === '3') {
        this.handleGetBranchUsersFun();
      }
    },
    async handleGetBranchUsersFun() {
      this.systemAssignDialog.userList = []
      this.systemAssignDialog.searchForm.param.tenantId = this.$store.state.layoutStore.currentLoginUser.tenantId
      let res = await getBranchUsers(this.systemAssignDialog.searchForm.param);
      if (res && this._.isArray(res)) {
        this.systemAssignDialog.userList = res;
      }
    },


    addStep() {
      this.steps.push({
        id: this.nextStepId++,
        type: '',
        name: '',
        items: [
          {
            id: this.nextItemId++,
            type: 'custom',
            selectedItem: null
          }
        ]
      });
    },
    removeStep(idx) {
      this.steps.splice(idx, 1);
    },
    addItem(stepIdx) {
      this.steps[stepIdx].items.push({
        id: this.nextItemId++,
        type: 'custom',
        action: ''
      });
    },
    confirmRemoveItem(stepIdx, itemIdx) {
      this.deleteConfirmDialog.stepIdx = stepIdx;
      this.deleteConfirmDialog.itemIdx = itemIdx;
      this.deleteConfirmDialog.type = 'item';
      this.deleteConfirmDialog.message = '请确认是否删除该执行事项？';
      this.deleteConfirmDialog.visible = true;
    },
    confirmRemoveStep(stepIdx) {
      this.deleteConfirmDialog.stepIdx = stepIdx;
      this.deleteConfirmDialog.itemIdx = -1;
      this.deleteConfirmDialog.type = 'step';
      this.deleteConfirmDialog.message = '请确认是否删除该步骤？';
      this.deleteConfirmDialog.visible = true;
    },
    confirmRemoveItemHandle() {
      if (this.deleteConfirmDialog.type === 'item') {
        this.removeItem(this.deleteConfirmDialog.stepIdx, this.deleteConfirmDialog.itemIdx);
      } else if (this.deleteConfirmDialog.type === 'step') {
        this.removeStep(this.deleteConfirmDialog.stepIdx);
      }
      this.deleteConfirmDialog.visible = false;
    },
    removeItem(stepIdx, itemIdx) {
      this.steps[stepIdx].items.splice(itemIdx, 1);
    },
    initDefaultSteps() {
      this.steps = this.opportunityTypes.map((type, index) => ({
        id: index + 1,
        type: type.value,
        name: type.label,
        items: [
          {
            id: 1,
            type: 'custom',
            selectedItem: null
          }
        ]
      }));

      // 为默认流程设置默认的系统分配配置
      this.systemAssignDialog.orgRange = '1';
      this.systemAssignDialog.displaySelected = '分公司统筹-全国机构';
    },

    // 加载编辑数据
    async loadEditData() {
      try {
        // 从路由参数获取流程ID
        const id = this.$route.query.id;
        if (!id) {
          this.$message.error('流程ID不能为空');
          return;
        }

        // 调用接口获取流程详情
        const res = await getProcessDefineDetail({ id });
        if (res) {
          // 使用新的回显方法
          this.parseProcessJson(res);
        }
      } catch (error) {
        console.error('加载编辑数据失败:', error);
        this.$message.error('加载数据失败，请重试');
      }
    },

    // 加载复制数据
    async loadCopyData() {
      try {
        // 从路由参数获取流程ID
        const id = this.$route.query.id;
        if (!id) {
          this.$message.error('流程ID不能为空');
          return;
        }

        // 调用接口获取流程详情
        const res = await getProcessDefineDetail({ id });
        if (res) {
          // 使用新的回显方法，但清除ID信息，当作新增处理
          this.parseProcessJsonForCopy(res);
        }
      } catch (error) {
        console.error('加载复制数据失败:', error);
        this.$message.error('加载数据失败，请重试');
      }
    },
    handleSave() {
      // 1. 校验流程名称必填（仅自定义流程）
      if (!this.isDefaultProcess && (!this.form.name || this.form.name.trim() == '')) {
        this.$message.error('请输入流程名称！');
        return;
      }

      // 2. 校验前端录入模块（仅自定义流程）
      if (!this.isDefaultProcess) {
        // 企业条件校验
        if (this.condition.enterprise.limitType == 'limit') {
          if (!this.condition.enterprise.location || this.condition.enterprise.location.length == 0) {
            this.$message.error('请选择企业所在地！');
            return;
          }
          if (!this.condition.enterprise.industry || this.condition.enterprise.industry.length == 0) {
            this.$message.error('请选择企业所属行业！');
            return;
          }
          if (!this.condition.enterprise.scale || this.condition.enterprise.scale.length == 0) {
            this.$message.error('请选择企业规模！');
            return;
          }
        }
        // 顾问条件校验
        if (this.condition.consultant.limitType == 'limit') {
          if (!this.condition.consultant.org || this.condition.consultant.org.length == 0) {
            this.$message.error('请选择顾问所属机构！');
            return;
          }
          if (!this.condition.consultant.dept || this.condition.consultant.dept.length == 0) {
            this.$message.error('请选择所属营业部！');
            return;
          }
        }
      }

      // 3. 校验机会管理模块
      // 第一步系统分配必填（仅自定义流程）
      if (!this.isDefaultProcess && (!this.systemAssignDialog.displaySelected || this.systemAssignDialog.displaySelected == '')) {
        this.$message.error('第一步系统分配为必填项！');
        return;
      }

      // 校验机会类型是否重复（仅自定义流程）
      if (!this.isDefaultProcess) {
        const selectedTypes = this.steps.map(step => step.type);
        const uniqueTypes = new Set(selectedTypes);
        if (selectedTypes.length !== uniqueTypes.size) {
          this.$message.error('机会类型不能重复，请修改后保存！');
          return;
        }
      }

      // 校验每个步骤的机会类型和名称必填（仅自定义流程）
      if (!this.isDefaultProcess) {
        for (let stepIdx = 0; stepIdx < this.steps.length; stepIdx++) {
          const step = this.steps[stepIdx];
          if (!step.type || step.type.trim() == '') {
            this.$message.error(`第${stepIdx + 1}步的机会类型为必填项！`);
            return;
          }
          if (!step.name || step.name.trim() == '') {
            this.$message.error(`第${stepIdx + 1}步的机会名称为必填项！`);
            return;
          }
        }
      }

      // 校验执行事项 - 每个步骤内不能重复，但不同步骤间可以重复
      for (let stepIdx = 0; stepIdx < this.steps.length; stepIdx++) {
        const step = this.steps[stepIdx];
        const stepExecutionItems = [];

        for (let itemIdx = 0; itemIdx < step.items.length; itemIdx++) {
          const item = step.items[itemIdx];
          if (item.selectedItem) {
            // 检查当前步骤内是否重复
            if (stepExecutionItems.includes(item.selectedItem.name)) {
              this.$message.error(`第${stepIdx + 1}步的执行事项"${item.selectedItem.name}"重复，请修改后保存！`);
              return;
            }
            stepExecutionItems.push(item.selectedItem.name);
          } else {
            // 如果有执行事项项但没有选择具体事项，提示必填
            this.$message.error(`第${stepIdx + 1}步的执行事项为必填项！`);
            return;
          }
        }
      }

      // 构建提交给后端的数据结构
      const processJson = this.buildProcessJson();

      console.log('提交给后端的数据:', processJson);

      // 调用保存接口
      this.saveProcess(processJson);
    },

    // 保存流程到后端
    async saveProcess(processJson) {
      try {
        let res= await saveProcessDefine(processJson);

        if (res) {
          this.$message.success(this.isEditMode ? '更新成功' : '保存成功');
          // 跳转到流程列表页面
          this.$router.go(-1);
        }
      } catch (error) {
        console.error('保存流程失败:', error);
        this.$message.error('保存失败，请重试');
      }
    },
    handleTypeChange(step, stepIdx) {
      const selectedType = this.opportunityTypes.find(t => t.value == step.type);
      if (selectedType) {
        step.name = selectedType.label;
      }
    },
    showSystemAssignDialog() {
      if(this.systemAssignDialog.orgRange == '3'){
        this.handleGetBranchUsersFun();
      }
      this.systemAssignDialog.visible = true;
    },
    handleSystemAssignConfirm() {
      // 校验自定义人员必须选中
      if (this.systemAssignDialog.orgRange == '3' && !this.systemAssignDialog.selectedUserName) {
        this.$message.error('请选择一个自定义人员！');
        return;
      }

      // 展示已选内容
      let selected = '';
      if (this.systemAssignDialog.orgRange == '1') {
        selected = '全国机构';
      } else if (this.systemAssignDialog.orgRange == '2') {
        selected = '顾问所属机构';
      } else if (this.systemAssignDialog.orgRange == '3' && this.systemAssignDialog.selectedUserName) {
        // 找到选中的用户对象，获取userId
        const selectedUser = this.systemAssignDialog.userList.find(u => u.nickName == this.systemAssignDialog.selectedUserName);
        if (selectedUser && selectedUser.userId) {
          selected = `${this.systemAssignDialog.selectedUserName}(${selectedUser.userId})`;
        } else {
          selected = this.systemAssignDialog.selectedUserName;
        }
      }

      this.systemAssignDialog.displaySelected = '分公司统筹-' + selected;

      // 保存系统分配配置，不添加到执行事项中
      if (this.systemAssignDialog.selectedUserName) {
        const selectedUser = this.systemAssignDialog.userList.find(u => u.nickName == this.systemAssignDialog.selectedUserName);
        if (selectedUser) {
          this.systemAssignDialog.selectedUser = selectedUser;
        }
      }
      this.systemAssignDialog.visible = false;
      // 不清空selectedUserName，保持用户选择状态
      // this.systemAssignDialog.selectedUserName = '';
      // 清空选中人员信息
      this.systemAssignDialog.selectedUserInfo = null;
    },
    handleUserSearch(form) {
      this.systemAssignDialog.selectedUserInfo = null; // 清空选中人员信息
      this.handleGetBranchUsersFun();
    },
    resetUserSearch() {
      this.systemAssignDialog.userList = [];
      this.systemAssignDialog.searchForm.param.organCode = "";
      this.systemAssignDialog.searchForm.param.nickName = '';
      this.systemAssignDialog.selectedUserInfo = null; // 清空选中人员信息
    },
    handleUserSelect(row) {
      this.systemAssignDialog.selectedUser = row;
      this.systemAssignDialog.selectedUserName = row.nickName;
    },
    
    // 处理系统分配机会弹窗中的人员选择
    async handleSystemAssignUserSelect(row) {
      if (!row) {
        this.systemAssignDialog.selectedUserInfo = null;
        return;
      }

      try {
        // 调用接口获取参与机会数和待处理机会
        const res = await countUserParticipatedOpportunities({
          userId: row.userId
        });

        if (res) {
          this.systemAssignDialog.selectedUserInfo = {
            ...row,
            opportunityCount: res.opportunityCount || 0,
            taskCount: res.taskCount || 0
          };
        } else {
          // 如果接口调用失败，使用默认值
          this.systemAssignDialog.selectedUserInfo = {
            ...row,
            opportunityCount: 0,
            taskCount: 0
          };
        }
      } catch (error) {
        console.error('获取用户参与机会信息失败:', error);
        // 接口调用失败时使用默认值
        this.systemAssignDialog.selectedUserInfo = {
          ...row,
          opportunityCount: 0,
          taskCount: 0
        };
      }
    },
    showExecutionItemDialog(stepIdx, itemIdx) {
      this.executionItemDialog.currentStepIdx = stepIdx;
      this.executionItemDialog.currentItemIdx = itemIdx;
      
      // 重置itemList的required状态为默认值
      this.executionItemDialog.itemList.forEach(item => {
        // 根据原始定义重置required状态
        if (item.name === '上传标书文件') {
          item.required = false;
        } else {
          item.required = true;
        }
      });
      
      // 获取当前已选中的执行事项
      const currentSelectedItem = this.steps[stepIdx].items[itemIdx].selectedItem;
      
      // 回显已选中的执行事项
      if (currentSelectedItem) {
        this.executionItemDialog.selectedItem = currentSelectedItem.name;
        
        // 更新itemList中对应项的required状态，确保回显正确
        const itemInList = this.executionItemDialog.itemList.find(item => item.name === currentSelectedItem.name);
        if (itemInList) {
          itemInList.required = currentSelectedItem.required;
        }
      } else {
        this.executionItemDialog.selectedItem = null;
      }
      
      this.executionItemDialog.visible = true;
    },
    handleExecutionItemConfirm() {
      if (!this.executionItemDialog.selectedItem) {
        this.$message.error('请选择一个执行事项！');
        return;
      }

      const stepIdx = this.executionItemDialog.currentStepIdx;
      const itemIdx = this.executionItemDialog.currentItemIdx;

      // 找到选中的完整事项对象
      const selectedItemObj = this.executionItemDialog.itemList.find(item => item.name == this.executionItemDialog.selectedItem);

      if (selectedItemObj) {
        // 使用深拷贝确保数据独立性
        this.steps[stepIdx].items[itemIdx].selectedItem = this._.cloneDeep(selectedItemObj);
      }
      
      this.executionItemDialog.visible = false;
    },
    handleExecutionItemDialogClose() {
      // 关闭弹窗时重置状态
      this.executionItemDialog.visible = false;
      this.executionItemDialog.selectedItem = null;
      this.executionItemDialog.currentStepIdx = -1;
      this.executionItemDialog.currentItemIdx = -1;
    }
  },
};
</script>

<style lang="less">
.process-input {
  .process-wrap {
    padding: 0 20px;
  }

  .condition-section {
    background: #fafbfc;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }

  .section-title {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 4px;
  }

  .section-desc {
    color: #888;
    font-size: 13px;
    margin-bottom: 16px;
  }

  .condition-form-card {
    margin-bottom: 16px;
  }

  .condition-form-card .el-form {
    padding-top: 18px;
  }

  .condition-form-card .el-form-item {
    margin-bottom: 10px;
  }

  .condition-card {
    margin-bottom: 0;
  }

  .condition-group-title {
    font-weight: bold;
    margin: 12px 0 4px 0;
  }

  .condition-list {
    margin-bottom: 8px;
  }

  .condition-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
  }

  .selected-value {
    color: #666;
    margin-left: 8px;
  }

  .step-section {
    background: #fafbfc;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-top: 16px;
  }

  .step-card {
    margin-bottom: 16px;

    .step-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .step-index {
        font-weight: bold;
        font-size: 14px;
      }
    }

    .step-content {
      padding: 0 16px;
    }

    .step-items {
      margin-top: 16px;

      .items-title {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 12px;
      }

      .add-item {
        margin-top: 12px;
      }
    }
  }

  .step-actions {
    display: flex;
    gap: 16px;
    margin-top: 16px;
  }

  .system-assign-content {
    height: 400px;
    padding: 20px;
    overflow-y: auto;
  }

  .assign-form {
    margin-bottom: 16px;
  }

  .custom-user-section {
    background: #f9f9f9;
    padding: 12px 16px 8px 16px;
    border-radius: 6px;
    margin-top: 8px;
  }

  .table-wrapper {
    // max-height: 280px;
    // overflow-y: auto;
    margin-top: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    
    .el-table {
      margin: 0;
      
      .el-table__body-wrapper {
        // overflow-y: auto;
        // max-height: 230px;
      }
    }
  }

  .search-bar {
    margin-bottom: 8px;
  }

  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 8px 16px;
    align-items: center;
  }

  // 在选择分配机会按钮右侧展示已选内容
  .system-assign-btn-wrap {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .selected-summary {
    font-size: 14px;
  }

  // 在主流程按钮处：
  .step-items .el-form-item:last-child {
    margin-bottom: 0;
  }

  .execution-item-wrap {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .selected-summary {
    font-size: 14px;
  }

  .execution-item-content {
    padding: 20px;
    height:400px;
    overflow-y: auto;
  }

  .table-container {
    max-height: 400px;
    // overflow-y: auto;
  }

  .check-popup {
    width: 100%;

    .btn-wrap {
      margin-top: 20px;
      margin-bottom: 20px;
      text-align: center;

      .btn-width {
        width: 100px;
      }
    }
  }

  // 选中人员信息显示样式
  .selected-user-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f2f5;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .info-item {
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 200px;

      .label {
        font-weight: bold;
        margin-right: 8px;
        min-width: 80px;
      }

      .value {
        color: v-bind('themeObj.color');
        flex: 1;
      }
    }

    // 在小屏幕上调整布局
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 10px;

      .info-item {
        flex-direction: column;
        align-items: flex-start;
        min-width: auto;

        .label {
          margin-bottom: 4px;
          min-width: auto;
        }
      }
    }
  }

  // 默认分配文本样式
  .default-assign-text {
    font-size: 14px;
    font-weight: 500;
    display: inline-block;
  }

  // radio按钮选中时的主题色样式
  input[type="radio"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #dcdfe6;
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
    
    &:checked {
      border-color: v-bind('themeObj.color');
      background-color: v-bind('themeObj.color');
      
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 6px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
      }
    }
    
    &:hover {
      border-color: v-bind('themeObj.color');
    }
  }
}
</style>

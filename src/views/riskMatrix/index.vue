<template>
  <div class="risk-matrix-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="风险矩阵管理"
      subtitle="企业风险管理矩阵配置，支持多维度风险评估"
      title-icon="el-icon-s-data"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="380"
      :search-label-width="'100px'"
      add-button-text="新增矩阵"
      empty-title="暂无风险矩阵数据"
      empty-description="点击上方新增矩阵按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 适用企业类型列插槽 -->
      <template #enterpriseTypes="{ row }">
        <div class="enterprise-types">
          <el-tag
            v-for="type in row.enterpriseTypes"
            :key="type"
            size="small"
            :type="getTypeTagType(type)"
            style="margin-right: 4px; margin-bottom: 4px;"
          >
            {{ getTypeName(type) }}
          </el-tag>
        </div>
      </template>

      <!-- 创建时间列插槽 -->
      <template #createTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.createTime }}
        </div>
      </template>
    </UniversalTable>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该风险矩阵？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import { getRiskMatrixList, deleteRiskMatrix } from '@/api/riskMatrix/index.js'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'RiskMatrixList',
  components: {
    UniversalTable,
    ConfirmDialog
  },

  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        param: {
          name: '',
          category: '',
          status: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'name',
          label: '矩阵名称',
          minWidth: 200,
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'description',
          label: '描述',
          minWidth: 300,
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'enterpriseTypes',
          label: '适用企业类型',
          width: 200,
          align: 'center',
          slot: true
        },
        {
          prop: 'categoryCount',
          label: '核心类别',
          width: 150,
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center',
          slot: true
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          class: 'edit-btn',
          size: 'mini'
        },
        {
          key: 'categoryConfig',
          label: '配置核心类别',
          icon: 'el-icon-s-grid',
          class: 'config-btn',
          size: 'mini'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn',
          size: 'mini'
        }
      ],
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '矩阵名称',
          name: 'name',
          type: 'input',
          placeholder: '请输入矩阵名称'
        },
        {
          label: '类别',
          name: 'category',
          type: 'select',
          placeholder: '请选择类别',
          list: [
            { dicItemCode: '行业风险', dicItemName: '行业风险' },
            { dicItemCode: '企业风险', dicItemName: '企业风险' },
            { dicItemCode: '产品风险', dicItemName: '产品风险' }
          ]
        },
        {
          label: '状态',
          name: 'status',
          type: 'select',
          placeholder: '请选择状态',
          list: [
            { dicItemCode: '1', dicItemName: '启用' },
            { dicItemCode: '0', dicItemName: '禁用' }
          ]
        }
      ],
      currentRow: null
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          ...this.searchForm.param
        }

        const response = await getRiskMatrixList(params)
        if (response) {
          this.tableData = response.list
          this.pagination.total = response.total
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
      handleSizeChange(val) {
        this.pagination.pageSize = val
        this.pagination.pageNum = 1
        this.loadData()
      },
      handleCurrentChange(val) {
        this.pagination.pageNum = val
        this.loadData()
      },
      handleSearch(searchData) {
        this.searchForm = { ...searchData }
        this.pagination.pageNum = 1
        this.loadData()
      },
      handleReset() {
        this.searchForm = {
          param: {
            name: '',
            category: '',
            status: ''
          }
        }
        this.pagination.pageNum = 1
        this.loadData()
      },
      handleAction({ action, row }) {
        switch (action) {
          case 'view':
            this.handleView(row)
            break
          case 'edit':
            this.handleEdit(row)
            break
          case 'categoryConfig':
            this.handleCategoryConfig(row)
            break
          case 'delete':
            this.currentRow = row
            this.$refs.confirmDialog.show()
            break
        }
      },
      async confirmDelete() {
        if (!this.currentRow) return

        try {
          await deleteRiskMatrix(this.currentRow.id)
          this.$message.success('删除成功')
          this.loadData()
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      },
      handleAdd() {
        this.$router.push({ name: 'riskMatrixEdit' })
      },

      getTypeName(code) {
        const typeMap = {
          'A': 'A类',
          'B': 'B类',
          'C': 'C类',
          'D': 'D类',
          'E': 'E类'
        }
        return typeMap[code] || code
      },
      getTypeTagType(type) {
        const typeMap = {
          'A': 'success',
          'B': 'warning',
          'C': 'info',
          'D': 'danger',
          'E': 'info'
        }
        return typeMap[type] || 'info'
      },
      handleView(row) {
        this.$router.push({ name: 'riskMatrixDetail', query: { id: row.id } })
      },
      handleEdit(row) {
        this.$router.push({ name: 'riskMatrixEdit', params: { id: row.id } })
      },
      handleCategoryConfig(row) {
        this.$router.push({ name: 'categoryList', params: { matrixId: row.id } })
      }
  }
}
</script>

<template>
  <div class="level-list-container">
    <EditPageContainer
      :title="pageTitle"
      :icon="pageIcon"
      :breadcrumb-items="breadcrumbItems"
      :loading="loading"
      :isView="true"
      @back="handleBack"
      @breadcrumb-click="handleBreadcrumbClick"
    >
      <div class="category-list-content">
        <!-- 操作栏 -->
        <div class="action-bar">
          <div class="left-actions">
            <span class="total-count">共 {{ levelList.length }} 个档次</span>
          </div>
          <div class="right-actions">
            <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增档次</el-button>
          </div>
        </div>

        <UniversalTable
          ref="universalTable"
          :show-header="false"
          :loading="loading"
          :table-data="levelList"
          :columns="tableColumns"
          :actions="tableActions"
          :pagination-data="pagination"
          :total="pagination.total"
          :show-search-form="false"
          :action-column-width="220"
          @action-click="handleAction"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <!-- 区间范围列插槽 -->
          <template #range="{ row }">
            <span class="range-display">{{ row.minValue }} ~ {{ row.maxValue }}</span>
          </template>

          <!-- 创建时间列插槽 -->
          <template #createTime="{ row }">
            <span>{{ formatDate(row.createTime) }}</span>
          </template>
        </UniversalTable>
      </div>
    </EditPageContainer>

    <!-- 档次编辑弹窗 -->
    <UniversalFormDialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :form-fields="computedFormFields"
      :form-data="levelForm"
      :form-rules="levelFormRules"
      :is-edit="isEditMode"
      :loading="loading"
      width="600px"
      @confirm="handleSave"
      @cancel="handleDialogClose"
      @close="handleDialogClose"
    />

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      :message="`确定要删除档次${currentLevel.name}吗？删除后无法恢复。`"
      :icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      :loading="deleting"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'
import UniversalFormDialog from '@/components/layouts/UniversalFormDialog.vue'
import {getRiskMatrixDetail, getCategoryDetail, getLevelsByCategoryId, saveLevelsForCategory} from '@/api/riskMatrix'

export default {
  name: 'LevelList',
  components: {
    UniversalTable,
    EditPageContainer,
    ConfirmDialog,
    UniversalFormDialog
  },
  data() {
    return {
      loading: false,
      saving: false,
      deleting: false,
      matrixId: null,
      categoryId: null,
      enterpriseType: null,
      matrixInfo: {
        id: null,
        name: '',
        description: ''
      },
      categoryInfo: {
        name: ''
      },
      levelList: [],
      dialogVisible: false,
      isViewMode: false,
      isEditMode: false,
      currentLevel: {},
      levelForm: {
        name: '',
        minValue: 0,
        maxValue: 0,
        description: ''
      },
      levelFormRules: {
        name: [
          { required: true, message: '请输入档次名称', trigger: 'blur' }
        ],
        minValue: [
          { required: true, message: '请输入最小值', trigger: 'blur' }
        ],
        maxValue: [
          { required: true, message: '请输入最大值', trigger: 'blur' }
        ]
      },
      formFields: [
        {
          type: 'input',
          prop: 'name',
          label: '档次名称',
          placeholder: '请输入档次名称',
          required: true,
          disabled: false
        },
        {
          type: 'number',
          prop: 'minValue',
          label: '最小值',
          placeholder: '请输入最小值',
          min: 0,
          max: 100,
          precision: 2,
          step: 0.1,
          required: true,
          disabled: false
        },
        {
          type: 'number',
          prop: 'maxValue',
          label: '最大值',
          placeholder: '请输入最大值',
          min: 0,
          max: 100,
          precision: 2,
          step: 0.1,
          required: true,
          disabled: false
        },
        {
          type: 'textarea',
          prop: 'description',
          label: '档次描述',
          placeholder: '请输入档次描述',
          rows: 3,
          disabled: false
        }
      ],
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      tableColumns: [
        {
          prop: 'name',
          label: '档次名称',
          width: 150,
          align: 'center'
        },
        {
          prop: 'range',
          label: '区间范围',
          width: 220,
          align: 'center',
          slot: true
        },
        {
          prop: 'description',
          label: '档次描述',
          minWidth: 300,
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center',
          slot: true
        }
      ],
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          type: 'text',
          class: 'action-btn view-btn'
        },
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          type: 'text',
          class: 'action-btn edit-btn'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          type: 'text',
          class: 'action-btn delete-btn'
        }
      ]
    }
  },
  computed: {
    pageTitle() {
      return `档次配置管理 - ${this.categoryInfo.name || '类别'}`
    },
    pageIcon() {
      return 'el-icon-s-operation'
    },
    breadcrumbItems() {
      return [
        { text: '风险矩阵管理', icon: 'el-icon-s-grid', to: { name: 'riskMatrixList' } },
        { text: '核心类别管理', icon: 'el-icon-s-operation', to: { name: 'categoryList', params: { matrixId: this.matrixId, enterpriseType: this.enterpriseType } } },
        { text: '档次配置管理', icon: 'el-icon-s-grid' }
      ]
    },
    dialogTitle() {
      if (this.isViewMode) return '查看档次'
      return this.isEditMode ? '编辑档次' : '新增档次'
    },
    computedFormFields() {
      return this.formFields.map(field => ({
        ...field,
        disabled: this.isViewMode
      }))
    }
  },
  created() {
    this.matrixId = this.$route.params.matrixId
    this.categoryId = parseInt(this.$route.params.categoryId)
    this.enterpriseType = this.$route.params.enterpriseType
    this.loadData()
  },
  methods: {
    async loadData() {
      if (!this.matrixId || !this.categoryId) {
        this.$message.error('缺少必要参数')
        return
      }

      this.loading = true
      try {

        const levelsResponse = await getLevelsByCategoryId(this.matrixId, this.categoryId)
        // 处理档次列表
        if (levelsResponse.code === 200 && levelsResponse.data) {
          this.levelList = levelsResponse.data
          console.log('加载的档次列表数据:', this.levelList)
          this.pagination.total = levelsResponse.data.length
        } else {
          this.levelList = []
          this.pagination.total = 0
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
        this.levelList = []
        this.pagination.total = 0
      } finally {
        this.loading = false
      }
    },

    handleAction({ action, row }) {
      switch (action) {
        case 'view':
          this.handleView(row)
          break
        case 'edit':
          this.handleEdit(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },

    handleView(row) {
      this.currentLevel = row
      this.levelForm = {
        name: row.name,
        minValue: row.minValue,
        maxValue: row.maxValue,
        description: row.description
      }
      this.isViewMode = true
      this.isEditMode = false
      this.dialogVisible = true
    },

    handleEdit(row) {
      this.currentLevel = row
      this.levelForm = {
        name: row.name,
        minValue: row.minValue,
        maxValue: row.maxValue,
        description: row.description
      }
      this.isViewMode = false
      this.isEditMode = true
      this.dialogVisible = true
    },

    handleAdd() {
      this.currentLevel = {}
      this.levelForm = {
        name: '',
        minValue: 0,
        maxValue: 0,
        description: ''
      }
      this.isViewMode = false
      this.isEditMode = false
      this.dialogVisible = true
    },

    handleDelete(row) {
      this.currentLevel = row
      this.$refs.confirmDialog.show()
    },

    async handleSave({ formData, isEdit }) {
      try {
        // 使用传入的formData而不是this.levelForm
        const levelFormData = formData

        // 验证区间范围
        if (levelFormData.minValue >= levelFormData.maxValue) {
          this.$message.error('最小值必须小于最大值')
          return
        }
        let levels = []
        // 验证区间是否与其他档次重叠
        if (this.levelList.length > 0){
          console.log('当前档次列表:', this.levelList)
          const otherLevels = this.levelList.filter(level =>
            isEdit ? level.id !== this.currentLevel.id : true
          )

          for (const level of otherLevels) {
            if (levelFormData.minValue < level.maxValue && levelFormData.maxValue > level.minValue) {
              this.$message.error(`区间范围与档次 "${level.name}" 重叠`)
              return
            }
          }
          // 基于当前档次列表进行操作
          levels = [...this.levelList]
        }

        this.saving = true

        if (isEdit) {
          // 编辑档次
          const levelIndex = levels.findIndex(level => level.id === this.currentLevel.id)
          if (levelIndex > -1) {
            levels[levelIndex] = {
              ...levels[levelIndex],
              ...levelFormData,
              updateTime: new Date().toISOString()
            }
          }
        } else {
          // 新增档次
          const newLevel = {
            id: null, // 新增时ID为null，由后端生成
            ...levelFormData,
            createTime: new Date().toISOString()
          }
          levels.push(newLevel)
        }

        // 转换档次数据为后端期望的格式
        const levelRequests = levels.map(level => ({
          id: level.id,
          name: level.name || '',
          minValue: level.minValue !== undefined && level.minValue !== null ? level.minValue : 0,
          maxValue: level.maxValue !== undefined && level.maxValue !== null ? level.maxValue : 100,
          description: level.description || '',
          color: level.color || ''
        }))


        // 调用保存档次配置 API
        const updateResponse = await saveLevelsForCategory(this.matrixId, this.categoryId, levelRequests)
        if (updateResponse) {
          this.$message.success(isEdit ? '档次编辑成功' : '档次新增成功')
          this.dialogVisible = false
          this.loadData()
        } else {
          console.error('保存档次配置失败:', updateResponse)
          this.$message.error(updateResponse.message || '保存失败')
        }
      } catch (error) {
        console.error('保存档次配置异常:', error)
        this.$message.error('保存失败：' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    },

    async confirmDelete() {
      try {
        this.deleting = true

        // 基于当前档次列表进行删除操作
        let levels = [...this.levelList]

        // 检查是否是最后一个档次
        if (levels.length <= 1) {
          this.$message.error('至少需要保留一个档次')
          this.deleting = false
          return
        }

        // 删除档次
        levels = levels.filter(level => level.id !== this.currentLevel.id)

        // 转换档次数据为后端期望的格式
        const levelRequests = levels.map(level => ({
          id: level.id,
          name: level.name || '',
          minValue: level.minValue !== undefined && level.minValue !== null ? level.minValue : 0,
          maxValue: level.maxValue !== undefined && level.maxValue !== null ? level.maxValue : 100,
          description: level.description || '',
          color: level.color || ''
        }))


        // 调用保存档次配置 API
        const updateResponse = await saveLevelsForCategory(this.matrixId, this.categoryId, levelRequests)
        if (updateResponse) {
          this.$message.success('档次删除成功')
          this.$refs.confirmDialog.hide()
          await this.loadData()
        } else {
          console.error('删除档次配置失败:', updateResponse)
          this.$message.error(updateResponse.message || '删除失败')
        }
      } catch (error) {
        console.error('删除档次配置异常:', error)
        this.$message.error('删除失败：' + (error.message || '未知错误'))
      } finally {
        this.deleting = false
      }
    },

    handleDialogClose() {
      this.dialogVisible = false
    },

    handleCurrentChange(current) {
      this.pagination.current = current
    },

    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
    },

    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    handleBack() {
      this.$router.push({
        name: 'categoryList',
        params: { matrixId: this.matrixId, enterpriseType: this.enterpriseType }
      })
    },

    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push(item.to)
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/shared-styles.less";

.level-list-container {
  min-height: 100vh;
  background: #fbf6ee;
  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .left-actions {
      display: flex;
      align-items: center;

      .total-count {
        color: #606266;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .right-actions {
      display: flex;
      gap: 12px;

      .el-button {
        background: #D7A256;
        border-color: #D7A256;

        &:hover {
          background: #E6B366;
          border-color: #E6B366;
        }
      }
    }
  }
  .level-form {
    .el-form-item {
      margin-bottom: 24px;
    }
  }

  .range-display {
    font-weight: 500;
    color: #409EFF;
  }

  .delete-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
    font-size: 16px;
  }

  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 12px;
    }
  }
}
</style>

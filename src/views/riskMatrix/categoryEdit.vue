<template>
  <div class="category-edit-container">
    <EditPageContainer
      :title="pageTitle"
      :icon="pageIcon"
      :breadcrumb-items="breadcrumbItems"
      :is-view="isViewMode"
      :loading="saving"
      @back="handleBack"
      @save="handleSave"
      @breadcrumb-click="handleBreadcrumbClick"
    >
      <div class="category-edit-content">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="category-tabs">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="basic-info-section">
              <UniversalForm
                ref="basicForm"

                :form-data="form"
                :form-rules="formRules"
                :form-groups="formGroups"
                :is-view="isViewMode"
                label-width="120px"
              >

              </UniversalForm>
            </div>
          </el-tab-pane>

          <!-- 档次配置 -->
          <el-tab-pane label="档次配置" name="levels">
            <div class="levels-section">
              <div class="levels-header">
                <div class="header-left">
                  <h3>档次配置</h3>
                  <span class="level-count">当前共 {{ form.levels.length }} 个档次</span>
                </div>
                <div class="header-right" v-if="!isViewMode">
                  <el-button type="primary" size="small" icon="el-icon-plus" @click="addLevel">添加档次</el-button>
                </div>
              </div>

              <div class="levels-table-wrapper">
                <el-table :data="form.levels" class="levels-table" empty-text="暂无档次配置" style="min-width: 700px">
                  <el-table-column label="序号" type="index" width="60" align="center" />

                  <el-table-column label="区间范围" width="260" align="center">
                    <template slot-scope="scope">
                      <div class="range-inputs" v-if="!isViewMode">
                        <el-input-number
                          v-model="scope.row.min"
                          :precision="2"
                          :step="0.1"
                          size="mini"
                          style="width: 100px"
                          placeholder="最小值"
                        />
                        <span class="range-separator">~</span>
                        <el-input-number
                          v-model="scope.row.max"
                          :precision="2"
                          :step="0.1"
                          size="mini"
                          style="width: 100px"
                          placeholder="最大值"
                        />
                      </div>
                      <div v-else class="range-display">
                        {{ scope.row.min }} ~ {{ scope.row.max }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="档次名称" min-width="120">
                    <template slot-scope="scope">
                      <el-input
                        v-if="!isViewMode"
                        v-model="scope.row.name"
                        size="mini"
                        placeholder="请输入档次名称"
                        maxlength="20"
                      />
                      <span v-else>{{ scope.row.name }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="对应文案" min-width="200">
                    <template slot-scope="scope">
                      <el-input
                        v-if="!isViewMode"
                        v-model="scope.row.description"
                        size="mini"
                        placeholder="请输入对应文案"
                        maxlength="100"
                      />
                      <span v-else>{{ scope.row.description }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="80" align="center" v-if="!isViewMode">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="removeLevel(scope.$index)"
                        class="delete-btn"
                        :disabled="form.levels.length <= 1"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div class="levels-tips" v-if="!isViewMode">
                <el-alert
                  title="配置提示"
                  type="info"
                  :closable="false"
                  show-icon
                >
                  <div slot="description">
                    <p>• 区间范围不能重叠，系统会自动验证</p>
                    <p>• 至少需要配置一个档次</p>
                    <p>• 建议按照分数从低到高的顺序配置档次</p>
                  </div>
                </el-alert>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </EditPageContainer>
  </div>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getRiskMatrixDetail, getCategoryDetail, saveSingleCategory, getScoreItemOptions } from '@/api/riskMatrix'

export default {
  name: 'CategoryEdit',
  components: {
    EditPageContainer,
    UniversalForm
  },
  data() {
    return {
      loading: false,
      saving: false,
      activeTab: 'basic',
      matrixId: null,
      categoryId: null,
      enterpriseType: null,
      scoreItemOptions: [],
      form: {
        id: null,
        name: '',
        description: '',
        calculationMethod: 'sum',
        scoreItems: [],
        levels: [
          {
            min: 0,
            max: 100,
            name: '',
            description: ''
          }
        ]
      },
      formRules: {
        name: [
          { required: true, message: '请输入类别名称', trigger: 'blur' },
          { min: 2, max: 50, message: '类别名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        calculationMethod: [
          { required: true, message: '请选择计算方式', trigger: 'change' }
        ],
        scoreItems: [
          { required: true, message: '请选择关联评分项', trigger: 'change' }
        ]
      }
    }
  },

  computed: {
    isNewCategory() {
      return this.categoryId === 'new'
    },
    isViewMode() {
      return this.$route.query.mode === 'view'
    },
    pageTitle() {
      if (this.isViewMode) {
        return `查看类别 - ${this.form.name || '核心类别'}`
      }
      return this.isNewCategory ? '新增核心类别' : `编辑类别 - ${this.form.name || '核心类别'}`
    },
    pageIcon() {
      return this.isViewMode ? 'el-icon-view' : (this.isNewCategory ? 'el-icon-plus' : 'el-icon-edit')
    },
    breadcrumbItems() {
      return [
        { text: '风险矩阵管理', icon: 'el-icon-s-grid', to: { name: 'riskMatrixList' } },
        { text: '核心类别管理', icon: 'el-icon-s-operation', to: { name: 'categoryList', params: { matrixId: this.matrixId } } },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    },
    formGroups() {
      return [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              {
                type: 'input',
                prop: 'name',
                label: '类别名称',
                placeholder: '请输入类别名称',
                maxlength: 50,
                showWordLimit: true,
                span: 12
              },
              {
                type: 'select',
                prop: 'calculationMethod',
                label: '计算方式',
                placeholder: '请选择计算方式',
                options: [
                  { label: '加和', value: 'sum' },
                  { label: '平均数', value: 'average' },
                  { label: '加和平均数', value: 'sumAndAvg' }
                ],
                span: 12
              }
            ],
            [
              {
                type: 'select',
                prop: 'scoreItems',
                label: '关联评分项',
                placeholder: '请选择关联评分项',
                required: true,
                multiple: true,
                collapseTags: true,
                filterable: true,
                options: this.scoreItemOptions,
                span: 24
              }
            ],
            [
              {
                type: 'textarea',
                prop: 'description',
                label: '类别描述',
                placeholder: '请输入类别描述（可选）',
                rows: 3,
                maxlength: 200,
                showWordLimit: true,
                span: 16
              }
            ]
          ]
        }
      ]
    }
  },
  async created() {
    this.matrixId = this.$route.params.matrixId
    this.categoryId = this.$route.params.categoryId
    this.enterpriseType = this.$route.params.enterpriseType

    // 如果有指定标签页，则切换到对应标签页
    if (this.$route.query.tab) {
      this.activeTab = this.$route.query.tab
    }
    if (!this.enterpriseType){
      await this.loadMatrixInfo()
    }
    await this.loadScoreItemOptions()
    await this.loadData()
  },
  methods: {
    async loadMatrixInfo() {
      const response = await getRiskMatrixDetail(this.matrixId)
      if (response.code === 200 && response.data) {
        this.enterpriseType = response.data.enterpriseTypesDisplay
      }
    },
    // 加载评分项选项
    async loadScoreItemOptions() {
      try {
        // 构建查询参数，包含企业类型过滤
        const queryParams = {}

        // 如果有企业类型信息，则添加企业类型过滤
        if (this.enterpriseType) {
          queryParams.enterpriseTypes = this.enterpriseType
        }

        const response = await getScoreItemOptions(queryParams)

        console.log('评分项选项API响应:', response)

        // 处理不同的返回格式
        let scoreItems = []
        if (response && response.code === 200 && response.data) {
          scoreItems = response.data
        } else if (response && Array.isArray(response)) {
          scoreItems = response
        } else {
          console.warn('评分项选项API返回数据格式异常:', response)
          scoreItems = []
        }

        this.scoreItemOptions = scoreItems.map(item => ({
          label: item.name,
          value: item.id
        }))

        console.log('处理后的评分项选项:', this.scoreItemOptions)

      } catch (error) {
        console.error('加载评分项选项失败:', error)
        this.$message.error('加载评分项选项失败')
        this.scoreItemOptions = []
      }
    },


    async loadData() {
      if (!this.matrixId) {
        this.$message.error('缺少风险矩阵ID')
        return
      }

      this.loading = true
      try {
        if (!this.isNewCategory) {
          // 编辑或查看模式，加载类别详情
          await this.loadCategoryDetail()
        } else {
          // 新增模式，初始化空表单
          this.initNewCategoryForm()
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 加载类别详情
    async loadCategoryDetail() {
      try {
        const response = await getCategoryDetail(this.matrixId, this.categoryId)
        if (response.code === 200 && response.data) {
          const category = response.data
          this.processCategoryData(category)
        }
      } catch (error) {
        console.error('加载类别详情失败:', error)
        // 回退到原有方式
      }
    },

    // 处理类别数据
    processCategoryData(category) {
      // 处理评分项数据：确保是数组格式
      let scoreItems = category.scoreItems || []
      if (!Array.isArray(scoreItems)) {
        scoreItems = scoreItems ? [scoreItems] : []
      }

      this.form = {
        id: category.id,
        name: category.name || '',
        description: category.description || '',
        calculationMethod: category.calculationMethod || 'sum',
        scoreItems: scoreItems,
        levels: category.levels && category.levels.length > 0 ?
          category.levels.map(level => ({
            id: level.id,
            min: level.minValue || level.min || 0,
            max: level.maxValue || level.max || 100,
            name: level.name || '',
            description: level.description || '',
            color: level.color || ''
          })) : [
          {
            min: 0,
            max: 100,
            name: '',
            description: ''
          }
        ]
      }
    },

    // 初始化新类别表单
    initNewCategoryForm() {
      // 新增模式，生成新的ID
      this.form.id = 'category_' + Date.now()
      // 新增模式下也强制重新渲染表单
      this.$nextTick(() => {
        this.formKey++
      })
    },
    addLevel() {
      this.form.levels.push({
        min: 0,
        max: 100,
        name: '',
        description: ''
      })
    },
    removeLevel(index) {
      if (this.form.levels.length > 1) {
        this.form.levels.splice(index, 1)
      }
    },
    validateLevels() {
      const levels = this.form.levels

      // 检查是否有空的档次名称
      for (let i = 0; i < levels.length; i++) {
        if (!levels[i].name || !levels[i].name.trim()) {
          this.$message.error(`第 ${i + 1} 个档次的名称不能为空`)
          return false
        }
      }

      // 检查区间是否有效
      for (let i = 0; i < levels.length; i++) {
        const level = levels[i]

        // 检查区间值是否为空或无效
        if (level.min === undefined || level.min === null || level.max === undefined || level.max === null) {
          this.$message.error(`第 ${i + 1} 个档次的区间范围不能为空`)
          return false
        }

        if (level.min >= level.max) {
          this.$message.error(`第 ${i + 1} 个档次的区间范围无效，最小值应小于最大值`)
          return false
        }
      }

      // 检查区间是否重叠
      for (let i = 0; i < levels.length; i++) {
        for (let j = i + 1; j < levels.length; j++) {
          const level1 = levels[i]
          const level2 = levels[j]

          if ((level1.min < level2.max && level1.max > level2.min)) {
            this.$message.error(`第 ${i + 1} 个档次与第 ${j + 1} 个档次的区间范围重叠`)
            return false
          }
        }
      }

      return true
    },
    async handleSave() {
      try {
        // 验证基本信息表单
        await this.$refs.basicForm.validate()

        // 验证档次配置
        if (!this.validateLevels()) {
          this.activeTab = 'levels'
          return
        }

        this.saving = true

        // 构建当前类别的数据
        const currentCategory = {
          id: this.isNewCategory ? null : this.categoryId,
          name: this.form.name,
          description: this.form.description,
          calculationMethod: this.form.calculationMethod,
          scoreItems: this.form.scoreItems || [],
          levels: this.form.levels ? this.form.levels.map(level => ({
            id: level.id || null,
            name: level.name || '',
            minValue: level.min !== undefined && level.min !== null ? level.min : 0,
            maxValue: level.max !== undefined && level.max !== null ? level.max : 100,
            description: level.description || '',
            color: level.color || ''
          })) : []
        }

        // 验证当前类别数据格式
        this.validateSingleCategoryData(currentCategory)

        // 尝试使用新的单个类别保存API
        let updateResponse = await saveSingleCategory(this.matrixId, currentCategory)

        if (updateResponse && updateResponse.code === 200) {
          this.$message.success(this.isNewCategory ? '类别新增成功' : '类别保存成功')
          this.handleBack()
        } else {
          console.error('保存类别配置失败:', updateResponse)
          this.$message.error(updateResponse.message || '保存失败')
        }
      } catch (error) {
        console.error('保存类别配置异常:', error)
        this.$message.error('保存失败：' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    },

    /**
     * 验证单个类别数据格式
     */
    validateSingleCategoryData(category) {
      if (!category.name || category.name.trim() === '') {
        throw new Error('类别名称不能为空')
      }

      if (category.levels && Array.isArray(category.levels)) {
        for (let j = 0; j < category.levels.length; j++) {
          const level = category.levels[j]
          if (!level.name || level.name.trim() === '') {
            throw new Error(`第${j + 1}个档次名称不能为空`)
          }

          // 验证区间范围
          if (level.minValue === undefined || level.minValue === null) {
            throw new Error(`第${j + 1}个档次最小值不能为空`)
          }
          if (level.maxValue === undefined || level.maxValue === null) {
            throw new Error(`第${j + 1}个档次最大值不能为空`)
          }
          if (level.minValue >= level.maxValue) {
            throw new Error(`第${j + 1}个档次区间范围无效，最小值应小于最大值`)
          }
        }
      }

      return true
    },

    /**
     * 验证类别数据格式（批量）
     */
    validateCategoryData(categoryRequests) {
      if (!Array.isArray(categoryRequests)) {
        throw new Error('类别数据必须是数组格式')
      }

      for (let i = 0; i < categoryRequests.length; i++) {
        const category = categoryRequests[i]
        if (!category.name || category.name.trim() === '') {
          throw new Error(`第${i + 1}个类别的名称不能为空`)
        }

        if (category.levels && Array.isArray(category.levels)) {
          for (let j = 0; j < category.levels.length; j++) {
            const level = category.levels[j]
            if (!level.name || level.name.trim() === '') {
              throw new Error(`第${i + 1}个类别的第${j + 1}个档次名称不能为空`)
            }

            // 验证区间范围
            if (level.minValue === undefined || level.minValue === null) {
              throw new Error(`第${i + 1}个类别的第${j + 1}个档次最小值不能为空`)
            }
            if (level.maxValue === undefined || level.maxValue === null) {
              throw new Error(`第${i + 1}个类别的第${j + 1}个档次最大值不能为空`)
            }
            if (level.minValue >= level.maxValue) {
              throw new Error(`第${i + 1}个类别的第${j + 1}个档次区间范围无效，最小值应小于最大值`)
            }
          }
        }
      }

      return true
    },



    handleCancel() {
      this.handleBack()
    },
    handleBack() {
      this.$router.push({
        name: 'categoryList',
        params: { matrixId: this.matrixId }
      })
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push(item.to)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.category-edit-container {
  min-height: 100vh;
  background: #fbf6ee;
}

.category-edit-content {
  padding: 24px;

  .category-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    /deep/ .el-tabs__header {
      margin: 0;
      background: #fbf6ee;
      border-radius: 8px 8px 0 0;

      .el-tabs__nav-wrap {
        padding: 0 24px;

        .el-tabs__item {
          font-size: 16px;
          font-weight: 500;
          color: #666;

          &.is-active {
            color: #D7A256;
          }
        }

        .el-tabs__active-bar {
          background-color: #D7A256;
        }
      }
    }

    /deep/ .el-tab-pane {
      padding: 24px;
    }
  }

  .basic-info-section {
    .category-form {
      max-width: 800px;

      /deep/ .el-form-item__label {
        font-weight: 500;
        color: #2c3e50;
      }
    }
  }

  .levels-section {
    .levels-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .header-left {
        h3 {
          margin: 0 0 4px 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
        }

        .level-count {
          color: #666;
          font-size: 14px;
        }
      }

      .header-right {
        .el-button {
          background: #D7A256;
          border-color: #D7A256;

          &:hover {
            background: #E6B366;
            border-color: #E6B366;
          }
        }
      }
    }

    .levels-table-wrapper {
      margin-bottom: 20px;
      border: 1px solid #f7ecdd;
      border-radius: 6px;
      overflow-x: auto;
      overflow-y: hidden;

      .levels-table {
        /deep/ .el-table__header-wrapper {
          .el-table__header {
            th {
              background: #fbf6ee;
              font-weight: 600;
              font-size: 14px;
              color: #2c3e50;
              border-bottom: 1px solid #f7ecdd;
            }
          }
        }

        /deep/ .el-table__body-wrapper {
          .el-table__row {
            td {
              border-bottom: 1px solid #f7ecdd;
              padding: 12px 0;
            }
          }
        }

        .range-inputs {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          min-width: 220px;

          .range-separator {
            color: #666;
            font-weight: 500;
            flex-shrink: 0;
          }

          .el-input-number {
            flex-shrink: 0;
          }
        }

        .range-display {
          text-align: center;
          color: #2c3e50;
          font-weight: 500;
        }

        .delete-btn {
          color: #f56c6c;

          &:hover {
            color: #f56c6c;
            background: rgba(245, 108, 108, 0.1);
          }

          &.is-disabled {
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }
      }
    }

    .levels-tips {
      /deep/ .el-alert {
        border-radius: 6px;

        .el-alert__description {
          p {
            margin: 4px 0;
            color: #666;
          }
        }
      }
    }
  }

  .action-buttons {
    margin-top: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    text-align: right;

    .el-button {
      margin-left: 12px;

      &[type="primary"] {
        background: #D7A256;
        border-color: #D7A256;

        &:hover {
          background: #E6B366;
          border-color: #E6B366;
        }
      }
    }
  }
}

// 关联评分项选择框样式 - 参照UniversalForm标准样式
/deep/ .score-item-select-field,
/deep/ .el-form-item .score-item-select-field,
/deep/ .el-form-item .el-select {
  width: 240px !important;
  min-width: 240px !important;
  max-width: 300px !important;
  box-sizing: border-box;

  // 多选标签样式优化 - 参照UniversalForm标准
  .el-select__tags {
    max-height: 80px;
    overflow-y: auto;
    padding-right: 20px; // 为下拉箭头留出空间

    .el-tag {
      margin: 2px 4px 2px 0;
      max-width: 120px; // 调整标签最大宽度以适应240px容器
      height: 24px;
      line-height: 22px;
      font-size: 12px;

      .el-tag__content {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
      }

      .el-tag__close {
        margin-left: 4px;
      }
    }

    // 折叠标签样式
    .el-tag--info {
      background-color: #f4f4f5;
      border-color: #e9e9eb;
      color: #909399;
      font-size: 12px;
    }
  }

  // 输入框样式
  .el-input {
    width: 100% !important;

    .el-input__inner {
      width: 100% !important;
      padding-right: 30px; // 为下拉箭头留出空间
    }
  }
}

// 限制关联评分项下拉框宽度 - 与选择框宽度一致
/deep/ .score-item-select-dropdown {
  min-width: 240px !important;
  max-width: 300px !important;

  // 确保下拉选项文本不会过长
  .el-select-dropdown__item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 12px;
    line-height: 34px;
    font-size: 14px;

    // 鼠标悬停效果
    &:hover {
      background-color: #f5f7fa;
    }

    // 选中状态
    &.selected {
      color: #409EFF;
      font-weight: 500;
    }
  }

  // 加载更多和加载中选项的样式
  .el-select-dropdown__item[disabled] {
    color: #409EFF;
    background-color: transparent;
    font-size: 13px;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  // 空数据提示样式
  .el-select-dropdown__empty {
    padding: 10px 0;
    margin: 0;
    text-align: center;
    color: #999;
    font-size: 14px;
  }
}
</style>

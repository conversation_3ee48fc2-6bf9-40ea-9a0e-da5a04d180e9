<template>
  <div class="score-item-container">
    <UniversalTable
      title="核心评分项管理"
      subtitle="统一管理所有可用的核心评分项，支持公式关联，可在风险矩阵中复用"
      title-icon="el-icon-s-flag"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :pagination-data="pagination"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :total="pagination.total"
      :action-column-width="220"
      add-button-text="新建评分项"
      empty-title="暂无评分项"
      empty-description="点击上方新建按钮添加评分项"
      @add="openEdit"
      @size-change="handleSizeChange"
      @current-change="handlePageChange"
      @action-click="handleAction"
      @search="handleSearch"
      @reset="handleReset"
    >
      <template #enterpriseTypes="{ row }">
        <el-tag v-for="type in row.enterpriseTypes" :key="type" size="small" :type="getTypeTagType(type)" style="margin-right: 4px;">
          {{ getTypeName(type) }}
        </el-tag>
      </template>
    </UniversalTable>
    <UniversalFormDialog
      v-model="dialogVisible"
      :formFields="formFields"
      :formRules="formRules"
      :formData="editForm"
      :title="editIndex === -1 ? '新建评分项' : '编辑评分项'"
      width="600px"
      @confirm="saveEdit"
      @cancel="dialogVisible=false"
    />
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该评分项？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>
<script>
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import UniversalFormDialog from '@/components/layouts/UniversalFormDialog.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'
import { getScoreItemList, saveScoreItem, deleteScoreItem } from '@/api/riskMatrix/index.js'
import { getFormulaList } from '@/api/formulaEngine'
import {getEnterpriseTypeOptions} from "@/api/enterprise/type";
export default {
  name: 'ScoreItemList',
  components: { UniversalTable, UniversalFormDialog, ConfirmDialog },
  data() {
    return {
      loading: false,
      tableData: [],
      dialogVisible: false,
      editForm: {
        id: '',
        name: '',
        formulaId: '',
        formulaName: '',
        coefficient: 1.00,
        enterpriseTypes: []
      },
      editIndex: -1,
      formulaOptions: [],
      enterpriseTypeOptions: [],
      formFields: [
        { prop: 'name', label: '评分项名称', type: 'input', placeholder: '请输入评分项名称', maxlength: 50, showWordLimit: true, required: true },
        { prop: 'formulaId', label: '关联公式', type: 'select', placeholder: '请选择关联公式', options: [], required: true },
        { prop: 'coefficient', label: '系数', type: 'number', min: 0, max: 999, precision: 2, step: 0.01, required: true, placeholder: '请输入系数，支持两位小数' },
        { prop: 'enterpriseTypes', label: '企业类型', type: 'select', multiple: true, options: [], placeholder: '请选择企业类型', required: true }
      ],
      formRules: {
        name: [
          { required: true, message: '请输入评分项名称', trigger: 'blur' },
          { min: 1, max: 50, message: '评分项名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        formulaId: [
          { required: true, message: '请选择关联公式', trigger: 'change' }
        ],
        coefficient: [
          { required: true, message: '请输入系数', trigger: 'blur' },
          { type: 'number', min: 0, message: '系数不能小于0', trigger: 'blur' }
        ],
        enterpriseTypes: [
          { required: true, message: '请选择企业类型', trigger: 'change' }
        ]
      },
      tableColumns: [
        { prop: 'name', label: '评分项名称', minWidth: 200, align: 'center' },
        { prop: 'formulaName', label: '关联公式', minWidth: 200, align: 'center' },
        { prop: 'coefficient', label: '系数', width: 100, align: 'center' },
        { prop: 'enterpriseTypes', label: '企业类型', width: 200, align: 'center', slot: true },
        { prop: 'createTime', label: '创建时间', width: 180, align: 'center' }
      ],
      tableActions: [
        { key: 'view', label: '查看', icon: 'el-icon-view', class: 'view-btn', size: 'mini' },
        { key: 'edit', label: '编辑', icon: 'el-icon-edit', class: 'edit-btn', size: 'mini' },
        { key: 'delete', label: '删除', icon: 'el-icon-delete', class: 'delete-btn', size: 'mini' }
      ],
      searchForm: {
        param: {
          name: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '评分项名称',
          name: 'name',
          type: 'input',
          placeholder: '请输入评分项名称'
        }
      ],
      deleteRow: null
    }
  },
  mounted() {
    this.loadData()
    this.loadEnterpriseTypeOptions()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param:{
            // 添加搜索参数
            name: this.searchForm.param.name || ''
          }
        }
        const response = await getScoreItemList(params)
        if (response && response.list) {
          this.tableData = response.list
          this.pagination.total = response.total
        } else {
          this.$message.error('加载数据失败')
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 加载企业类型选项
    async loadEnterpriseTypeOptions() {
      try {
        this.enterpriseTypeLoading = true
        const response = await getEnterpriseTypeOptions()
        if (response && Array.isArray(response)) {
          this.enterpriseTypeOptions = response
        } else {
          this.enterpriseTypeOptions = []
        }
      } catch (error) {
        console.error('加载企业类型选项失败:', error)
        this.$message.error('加载企业类型选项失败')
        this.enterpriseTypeOptions = []
      } finally {
        this.enterpriseTypeLoading = false
      }
    },
    handlePageChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },
    openEdit(row) {
      if (row && row.id) {
        this.$router.push({ name: 'scoreItemEdit', params: { id: row.id } })
      } else {
        this.$router.push({ name: 'scoreItemEdit' })
      }
    },
    handleAction({ action, row }) {
      if (action === 'view') {
        this.$router.push({ name: 'scoreItemEdit', params: { id: row.id }, query: { mode: 'view' } })
      } else if (action === 'edit') {
        this.openEdit(row)
      } else if (action === 'delete') {
        this.deleteRow = row
        this.$refs.confirmDialog.show()
      }
    },
    async saveEdit() {
      // 设置公式名称
      const formula = this.formulaOptions.find(f => f.id === this.editForm.formulaId)
      this.editForm.formulaName = formula ? formula.name : ''
      try {
        const response = await saveScoreItem(this.editForm)
        if (response.code === 200) {
          this.$message.success('保存成功')
          this.dialogVisible = false
          this.loadData()
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      }
    },
    confirmDelete() {
      if (!this.deleteRow) return
      deleteScoreItem(this.deleteRow.id).then(res => {
        if (res.code === 200) {
          this.$message.success('删除成功')
          this.loadData()
        } else {
          this.$message.error(res.message || '删除失败')
        }
        this.deleteRow = null
        this.$refs.confirmDialog.hide()
      }).catch(() => {
        this.$message.error('删除失败')
        this.deleteRow = null
        this.$refs.confirmDialog.hide()
      })
    },
    getTypeName(type) {
      const item = this.enterpriseTypeOptions.find(t => t.code === type)
      return item ? item.name : type
    },
    getTypeTagType(type) {
      const map = { A: 'primary', B: 'success', C: 'info', D: 'warning', E: 'danger' }
      return map[type] || 'default'
    },
    // 处理搜索事件
    handleSearch(searchData) {
      console.log('搜索参数:', searchData)
      // 更新搜索表单数据
      this.searchForm = { ...searchData }
      // 重置到第一页
      this.pagination.pageNum = 1
      // 重新加载数据
      this.loadData()
    },
    // 处理重置事件
    handleReset() {
      console.log('重置搜索')
      // 重置搜索表单
      this.searchForm = {
        param: {
          name: '',
          enterpriseTypes: ''
        }
      }
      // 重置到第一页
      this.pagination.pageNum = 1
      // 重新加载数据
      this.loadData()
    }
  }
}
</script>
<style lang="less" scoped>
.score-item-container {
  min-height: 100vh;
  background: #fbf6ee;
}
</style>

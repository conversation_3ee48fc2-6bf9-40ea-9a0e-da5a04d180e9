<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>
<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getScoreItemList, getScoreItemDetail, saveScoreItem } from '@/api/riskMatrix/index.js'
import { getFormulaList } from '@/api/formulaEngine'
import {getEnterpriseTypeOptions} from "@/api/enterprise/type";
export default {
  name: 'ScoreItemEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      loading: false,
      isView: false,
      form: {
        id: '',
        name: '',
        isFormula: 0,
        formulaId: '',
        formulaName: '',
        coefficient: 1.00,
        enterpriseTypes: []
      },
      formGroups: [
        {
          title: '评分项信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'name', label: '评分项名称', type: 'input', placeholder: '请输入评分项名称', maxlength: 50, showWordLimit: true, required: true },
              { prop: 'coefficient', label: '系数', type: 'number', min: 0, max: 999, precision: 2, step: 0.01, required: true, placeholder: '请输入系数，支持两位小数' }
            ],
            [
              { prop: 'isFormula', label: '是否关联公式', type: 'radio', options: [{ label: '否', value: 0 }, { label: '是', value: 1 }], required: true },
              { prop: 'enterpriseTypes', label: '企业类型', type: 'select', multiple: true, options: [], placeholder: '请选择企业类型', required: true }
            ]
          ]
        }
      ],
      formRules: {
        name: [
          { required: true, message: '请输入评分项名称', trigger: 'blur' },
          { min: 1, max: 50, message: '评分项名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        isFormula: [
          { required: true, message: '请选择是否关联公式', trigger: 'change' }
        ],
        formulaId: [
          { required: false, message: '请选择关联公式', trigger: 'change' }
        ],
        coefficient: [
          { required: true, message: '请输入系数', trigger: 'blur' },
          { type: 'number', min: 0, message: '系数不能小于0', trigger: 'blur' }
        ],
        enterpriseTypes: [
          { required: true, message: '请选择企业类型', trigger: 'change' }
        ]
      },
      formulaOptions: [],
      enterpriseTypeOptions: [],
      // 动态公式字段配置
      formulaFieldConfig: { prop: 'formulaId', label: '关联公式', type: 'select', placeholder: '请选择关联公式', options: [], required: false }
    }
  },
  watch: {
    'form.isFormula': {
      handler(newVal) {
        this.updateFormulaFieldVisibility(newVal)
        // 当选择"否"时，清空公式相关字段
        if (newVal === 0) {
          this.form.formulaId = ''
          this.form.formulaName = ''
        }
      },
      immediate: true
    }
  },
  computed: {
    pageTitle() {
      if (this.isView) return '查看评分项'
      return this.form.id ? '编辑评分项' : '新建评分项'
    },
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.form.id ? 'el-icon-edit' : 'el-icon-plus')
    },
    breadcrumbItems() {
      return [
        { text: '核心评分项管理', icon: 'el-icon-s-flag', to: { name: 'scoreItemList' } },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  async created() {
    this.isView = this.$route.query.mode === 'view'

    // 加载详情数据
    const id = this.$route.params.id
    if (id) {
      await this.loadDetail(id)
    }
    // 并行加载选项数据
    await Promise.all([
      this.loadOptions(),
      this.loadEnterpriseTypeOptions()
    ])
  },
  methods: {
    async loadOptions() {
      // 加载公式选项
      try {
        const res = await getFormulaList({ pageNum: 1, pageSize: 100 })
        console.log('公式列表API响应:', res)
        if (res && res.list) {
          this.formulaOptions = res.list.map(f => ({ label: f.name, value: f.id }))
          // 更新动态公式字段配置中的选项
          this.formulaFieldConfig.options = this.formulaOptions
          console.log('公式选项已更新:', this.formulaOptions)
        }
      } catch (error) {
        console.error('加载公式选项失败:', error)
        this.formulaOptions = []
      }
    },
    // 加载企业类型选项
    async loadEnterpriseTypeOptions() {
      try {
        this.enterpriseTypeLoading = true
        const response = await getEnterpriseTypeOptions()

        if (response && Array.isArray(response)) {
          // 转换数据格式：{name, code} -> {label, value}
          this.enterpriseTypeOptions = response.map(item => ({
            label: item.name,
            value: item.code
          }))

          // 更新表单配置中的企业类型选项
          this.updateEnterpriseTypeOptions()
        }
      } catch (error) {
        console.error('加载企业类型选项失败:', error)
        this.$message.error('加载企业类型选项失败')
      } finally {
        this.enterpriseTypeLoading = false
      }
    },
    // 更新表单配置中的企业类型选项
    updateEnterpriseTypeOptions() {
      const group = this.formGroups[0]
      if (group && group.fields && group.fields[1] && group.fields[1][1]) {
        group.fields[1][1].options = this.enterpriseTypeOptions
        console.log('已更新表单配置中的企业类型选项:', group.fields[1][1].options)
      }
    },
    async loadDetail(id) {
      this.loading = true
      try {
        console.log('加载评分项详情，ID:', id)
        const res = await getScoreItemDetail(id)
        console.log('评分项详情API响应:', res)

        if (res.code === 200 && res.data) {
          const data = res.data
          this.form = {
            id: data.id,
            name: data.name,
            isFormula: data.isFormula || 0,
            formulaId: data.formulaId,
            formulaName: data.formulaName,
            coefficient: data.coefficient,
            enterpriseTypes: data.enterpriseTypes || []
          }
          console.log('表单数据已更新:', this.form)
        } else {
          console.error('获取评分项详情失败:', res.message)
          this.$message.error(res.message || '获取评分项详情失败')
        }
      } catch (error) {
        console.error('加载评分项详情失败:', error)
        this.$message.error('加载评分项详情失败')
      } finally {
        this.loading = false
      }
    },
    async handleSave() {
      if (this.isView) return
      try {
        // 自定义验证逻辑
        if (this.form.isFormula === 1 && !this.form.formulaId) {
          this.$message.error('选择关联公式时，必须选择一个公式')
          return
        }

        await this.$refs.universalForm.validate()
        this.loading = true

        // 设置公式名称
        if (this.form.isFormula === 1 && this.form.formulaId) {
          const formula = this.formulaOptions.find(f => f.value === this.form.formulaId)
          this.form.formulaName = formula ? formula.label : ''
        } else {
          // 如果不关联公式，清空公式相关字段
          this.form.formulaId = null
          this.form.formulaName = ''
        }

        const res = await saveScoreItem(this.form)
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.handleBack()
        } else {
          this.$message.error(res.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    },
    // 更新公式字段的显示状态
    updateFormulaFieldVisibility(isFormula) {
      const group = this.formGroups[0]

      // 移除现有的公式字段（如果存在）
      if (group && group.fields) {
        // 检查是否已经有公式字段行
        const formulaRowIndex = group.fields.findIndex(row =>
          row.some(field => field.prop === 'formulaId')
        )

        if (formulaRowIndex !== -1) {
          // 移除现有的公式字段行
          group.fields.splice(formulaRowIndex, 1)
        }

        // 如果选择关联公式，添加公式字段行
        if (isFormula === 1) {
          // 更新公式字段配置
          this.formulaFieldConfig.required = true
          this.formulaFieldConfig.options = this.formulaOptions

          // 添加公式字段行
          group.fields.push([this.formulaFieldConfig])

          // 更新验证规则
          this.formRules.formulaId = [
            { required: true, message: '请选择关联公式', trigger: 'change' }
          ]
        } else {
          // 更新验证规则
          this.formRules.formulaId = []
        }

        // 强制更新表单
        this.$nextTick(() => {
          if (this.$refs.universalForm) {
            this.$refs.universalForm.$forceUpdate()
          }
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.score-item-edit-container {
  min-height: 100vh;
  background: #fbf6ee;
}
</style>

<template>
  <div class="wh100 tree-transfer">
    <el-container class="wh100 dt-menuList-wrap">
      <!-- 左侧选择栏 -->
      <el-aside
        :width="sideWidth"
        class="tree-search-custom"
        v-show="auth == 'edit'"
      >
        <div class="search-view">
          <el-input
            placeholder="请输入关键字"
            clearable
            v-model="filterText"
          ></el-input>
        </div>
        <div class="tree-view">
          <el-tree
            class="el-tree left-tree"
            ref="tree"
            show-checkbox
            :node-key="nodeKey"
            :data="treeList0"
            :props="treeDefaultProps"
            @check="check"
            @node-expand="treeExpand"
            @node-collapse="treeExpand"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
              <el-tooltip
                v-if="data.authDesc"
                :content="data.authDesc"
                placement="top"
                effect="light"
                popper-class="auth-desc-tooltip"
              >
                <i class="el-icon-question auth-desc-icon"></i>
              </el-tooltip>
            </span>
          </el-tree>
        </div>
      </el-aside>
      <!--右侧配置栏 -->
      <div
        class="wh100 tree-selected-main"
        :class="{ 'only-look': auth == 'look' }"
        :style="{ 'background-color': themeObj.navTagUnselectedColor }"
      >
        <TableToolTemp
          :toolListProps="toolListProps"
          @handleTool="handleTool"
          v-if="auth == 'edit'"
        ></TableToolTemp>
        <div v-if="title" class="title-view" :style="{ color: themeObj.color }">
          {{ title }}
        </div>
        <div
          class="selected-container"
          :class="{ 'no-title': !title, 'only-look': auth == 'look' }"
        >
          <el-tree
            class="right-tree"
            ref="tree1"
            :node-key="nodeKey"
            :data="treeList1"
            :props="treeDefaultProps"
            :filter-node-method="filterTree1Node"
            @node-expand="tree1Expand"
            @node-collapse="tree1Expand"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
              <el-tooltip
                v-if="data.authDesc"
                :content="data.authDesc"
                placement="top"
                effect="light"
                popper-class="auth-desc-tooltip"
              >
                <i class="el-icon-question auth-desc-icon"></i>
              </el-tooltip>
            </span>
          </el-tree>
        </div>
        <div class="btn-container" v-if="auth == 'edit'">
          <el-button type="primary" @click="confirm" class="dt-btn"
            >保存</el-button
          >
          <el-button
            @click="cancel"
            class="dt-btn"
            type="primary"
            plain
            :style="{ color: $store.state.layoutStore.themeObj.color }"
            >取消</el-button
          >
        </div>
      </div>
    </el-container>
    <!--删除弹窗-->
    <DtPopup
      :isShow.sync="showDel"
      @close="showDel = false"
      size="mini"
      @confirm="clearAll"
    >
      <div class="text-c puoup-del dt-fz16">
        删除后无法恢复，请确认是否删除？
      </div>
    </DtPopup>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import DtPopup from "@/components/layouts/DtPopup";
export default {
  name: "treeTransfer",
  props: {
    treeList: {
      //源树列表
      type: Array,
      default: () => {
        return [];
      }
    },
    defaultCheckedKeys: {
      //源树默认勾选数组
      type: Array,
      default: () => {
        return [];
      }
    },
    nodeKey: {
      type: String,
      default: "id"
    },
    nodeName: {
      type: String,
      default: "name"
    },
    treeDefaultProps: {
      type: Object,
      default: () => {
        return {
          children: "childs",
          label: "directName"
        };
      }
    },
    title: {
      type: String,
      default: ""
    },
    toolListProps: {
      type: Object,
      default: () => {
        return {
          toolTitle: "",
          toolTitleIcon: "",
          toolList: []
        };
      }
    },
    sideWidth: {
      type: String,
      default: "428px"
    },
    auth: {
      type: String,
      default: "edit",
      validator(value) {
        return ["edit", "look"].indexOf(value) >= 0;
      }
    }
  },
  data() {
    return {
      treeList0: [], //左侧树
      treeList1: [], //右侧树
      checkedKeys: [], //勾选的值(包含全选和半选状态),用于右侧树的回显
      filterText: "",
      filterKeys: [], //每次保存tree过滤后的funcId集合，用于左侧树结构的使用
      showDel: false,
      checkLoading: false //防止点击加载过程中延迟导致数据丢失
    };
  },
  components: {
    TableToolTemp,
    DtPopup
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  methods: {
    echoTree(arr) {
      //给左侧树回显
      this.$refs.tree && this.$refs.tree.setCheckedKeys(arr); //勾选tree选中的值
    },
    echoTree1() {
      //给右侧树只展示回显节点
      this.$refs.tree1.filter();
    },
    filterTree(menuList, keyword,nodesMap,filterKeys) {
      menuList.forEach(item => {
        nodesMap[item.authCode].visible = false; //初始化所有的节点，默认隐藏
        if (item.authName.match(keyword)) {
          nodesMap[item.authCode].visible = true;
          filterKeys.push(item.authCode);
          this.containChild(item.children,nodesMap,filterKeys);
          this.addNodeParent(nodesMap[item.authCode].parent);
        } else if (item.children) {
          this.filterTree(item.children, keyword,nodesMap,filterKeys);
        }
      });
    },
    containChild(list, nodesMap,filterKeys) {
      //给所有子节点设置visible=true;
      if (!list || !Object.prototype.toString.call(list) === "[object Array]")
        list = [];
      return list.map(item => {
        nodesMap[item.authCode].visible = true;
        filterKeys.push(item.authCode);
        if (item.children) {
          this.containChild(item.children, nodesMap,filterKeys);
        }
      });
    },
    addNodeParent(parentObj) {
      //给所有父节点设置visible=true;
      parentObj.visible = true;
      if (parentObj.parent) {
        this.addNodeParent(parentObj.parent);
      }
    },
    filterTree1Node(arr, data) {
      //tree1用过滤条件充当显示节点
      //过滤tree1的非勾选节点,data为循环树的item
      return this.checkedKeys.findIndex(item => item == data.authCode) > -1
        ? true
        : false;
    },
    check: _.throttle(function(e) {
      let treeNodesMap = this.$refs.tree.store.nodesMap;
      if (this.checkLoading == true) {
        treeNodesMap[e[this.nodeKey]].checked = !treeNodesMap[e[this.nodeKey]]
          .checked;
        return;
      }
      this.checkLoading = true;
      let checkList = [];
      this.getTreeChildkeys([e], checkList);

      //往this.checkedKeys中添加或移除funId及其子集的funId
      if (treeNodesMap[e[this.nodeKey]].checked) {
        //防止重复push
        if (this.checkedKeys.findIndex(authCode => authCode == e.authCode) == -1) {
          //添加勾选 - 包含子节点
          this.checkedKeys.push(...checkList);
        }

        // 自动选中所有父节点
        this.selectAllParents(e[this.nodeKey], treeNodesMap);
      } else {
        //移除勾选
        this.checkedKeys = _.pullAll(this.checkedKeys, checkList);

        // 检查是否需要取消父节点的选中状态
        this.checkParentDeselection(e[this.nodeKey], treeNodesMap);
      }
      this.echoTree1();
      this.checkLoading = false;
    }, 500),
    treeExpand: _.throttle(function(e) {
      this.$nextTick(() => {
        this.asyncExpanded(e[this.nodeKey], this.$refs.tree, this.$refs.tree1);
      });
    }, 500),
    tree1Expand: _.throttle(function(e) {
      this.$nextTick(() => {
        this.asyncExpanded(e[this.nodeKey], this.$refs.tree1, this.$refs.tree);
      });
    }, 500),
    asyncExpanded(id, tree, tree1) {
      //同步两棵树已勾选节点的展开
      if (tree1.store.nodesMap[id]) {
        tree1.store.nodesMap[id].expanded = tree.store.nodesMap[id].expanded;
      }
    },
    packUpNode(keys, tree, status) {
      if (!keys || Object.prototype.toString.call(keys) !== "[object Array]")
        return;
      //收起树的所有节点
      keys.forEach(key => {
        let itemNode = tree.store.nodesMap[key];
        if (itemNode && itemNode.data.children) {
          itemNode.expanded = status;
          this.asyncParent(key, tree, status);
        }
      });
      this.$forceUpdate();
    },
    asyncParent(id, tree, status) {
      //同步已勾选节点父级展开
      let parent = tree.store.nodesMap[id] && tree.store.nodesMap[id].parent;
      if (parent && parent.data.authCode) {
        tree.store.nodesMap[parent.data.authCode].expanded = status;
        this.asyncParent(parent.data.authCode, tree, status);
      }
      this.$forceUpdate();
    },
    handleTool(val) {
      if (val.name == "清空") {
        this.showDel = true;
      }
    },
    clearAll() {
      this.$refs.tree.setCheckedKeys([]);
      this.packUpNode(this.$refs.tree.store.nodesMap, this.$refs.tree, false); //收起所有的树节点
      this.treeList1 = [];
      this.showDel = false;
    },
    //获取树中的子节点funcId的列表（包括当前节点和所有子节点）
    getTreeChildkeys(trees, list) {
      if (!trees || !Object.prototype.toString.call(trees) === "[object Array]")
        return [];
      trees.forEach(item => {
        // 添加当前节点
        list.push(item.authCode);
        // 递归添加子节点
        if (item.children) {
          this.getTreeChildkeys(item.children, list);
        }
      });
    },

    // 自动选中所有父节点
    selectAllParents(nodeKey, treeNodesMap) {
      const currentNode = treeNodesMap[nodeKey];
      if (currentNode && currentNode.parent && currentNode.parent.data.authCode) {
        const parentKey = currentNode.parent.data.authCode;

        // 如果父节点还没有被选中，则选中它
        if (this.checkedKeys.findIndex(authCode => authCode === parentKey) === -1) {
          this.checkedKeys.push(parentKey);
          // 设置父节点为选中状态
          treeNodesMap[parentKey].checked = true;
        }

        // 递归处理父节点的父节点
        this.selectAllParents(parentKey, treeNodesMap);
      }
    },

    // 检查是否需要取消父节点的选中状态
    checkParentDeselection(nodeKey, treeNodesMap) {
      const currentNode = treeNodesMap[nodeKey];
      if (currentNode && currentNode.parent && currentNode.parent.data.authCode) {
        const parentKey = currentNode.parent.data.authCode;
        const parentNode = treeNodesMap[parentKey];

        // 检查父节点的所有子节点是否都未选中
        if (parentNode && parentNode.childNodes) {
          const hasSelectedChild = parentNode.childNodes.some(child => {
            return this.checkedKeys.includes(child.data.authCode);
          });

          // 如果父节点没有任何选中的子节点，则取消父节点的选中状态
          if (!hasSelectedChild) {
            this.checkedKeys = this.checkedKeys.filter(key => key !== parentKey);
            parentNode.checked = false;

            // 递归检查父节点的父节点
            this.checkParentDeselection(parentKey, treeNodesMap);
          }
        }
      }
    },
    cancel() {
      //取消
      this.$emit("cancel");
    },
    confirm() {
      //确认
      let selectKey = this.checkedKeys; //获取所有选中的key值

      let selectNodes = _.cloneDeep(
        selectKey.map(id => {
          return this.$refs.tree.store.nodesMap[id];
        })
      );
      this.$emit(
        "confirm",
        selectKey,
        selectNodes,
        this.$refs.tree.store.nodesMap
      );
    }
  },
  watch: {
    treeList: {
      handler(_new, _old) {
        if (_new.length > 0) {
          this.treeList0 = _.cloneDeep(_new);
          this.treeList1 = _.cloneDeep(_new);
          this.checkedKeys = _.cloneDeep(this.defaultCheckedKeys);
          this.$nextTick(() => {
            this.echoTree(this.checkedKeys);
            this.echoTree1();
          });
        }
      },
      immediate: true //设置为true才会在实例化时触发
    },
    filterText(val) {
      const nodesMap = this.$refs.tree.store.nodesMap;
      this.filterKeys = [];
      this.filterTree(this.treeList0, this.filterText,nodesMap, this.filterKeys); //手动获取过滤后新树的节点集合this.filterKeys
    }
  }
};
</script>
<style lang="less">
.tree-transfer {
  .dt-menuList-wrap {
    padding: 0px 20px 0;
    height: calc(100vh - 240px);
    .tree-search-custom {
      width: 518px;
      height: auto;
      background: #f6f6f6;
      margin-right: 20px;
      .search-view {
        padding: 20px;
      }
      .tree-view {
        // max-height:800px;
        // min-height:50vh;
        height: calc(100% - 76px);
        overflow-y: auto;
      }
    }
    .el-tree {
      background: #f6f6f6;
      overflow-y: auto;
      &.allTree {
        height: 100%;
      }
      .el-tree-node__content {
        background: #ebebeb;
        height: 52px;
        border-bottom: 1px solid #f6f6f6;
      }
      .el-tree-node__children {
        .el-tree-node__content {
          background: #f6f6f6;
          border: none;
        }
      }
      .el-tree-node__expand-icon {
        margin-left: 15px;
      }
      .el-tree__empty-block {
        margin-top: 10vh;
      }
    }
    .right-tree {
      background: transparent;
      .el-tree-node__content {
        background: transparent;
      }
      .el-tree-node__children .el-tree-node__content {
        background: transparent;
      }
    }
    .tree-selected-main {
      position: relative;
      &.only-look {
        width: 428px;
      }
      .title-view {
        height: 52px;
        line-height: 52px;
        padding-left: 20px;
      }
      .tab-tool {
        padding-bottom: 10px;
        margin-bottom: 0px;
      }
      .selected-container {
        height: calc(100% - 180px);
        &.no-title {
          height: calc(100% - 128px);
        }
        &.only-look {
          height: calc(100% - 0px);
        }
        overflow: auto;
      }
      .btn-container {
        line-height: 70px;
        padding-left: 20px;
      }
    }
  }
  
  // 自定义树节点样式
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    
    .auth-desc-icon {
      color: #909399;
      font-size: 14px;
      margin-left: 8px;
      cursor: pointer;
      transition: color 0.3s;
      
      &:hover {
        color: #409EFF;
      }
    }
  }
}

// 全局样式，用于tooltip
.auth-desc-tooltip {
  max-width: 300px !important;
  word-wrap: break-word;
  line-height: 1.5;
}
</style>

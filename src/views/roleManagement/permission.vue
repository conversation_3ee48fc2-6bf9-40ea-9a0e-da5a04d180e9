<template>
  <div class="dt-menuList">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item class="trendsTitle" @click.native="goBack"
        >角色管理</el-breadcrumb-item
      >
      <el-breadcrumb-item :style="{ color: themeObj.color }"
        >权限配置</el-breadcrumb-item
      >
    </el-breadcrumb>
    <div class="tool-temp">
      <TableToolTemp :toolListProps="menuListPros"></TableToolTemp>
      <span style="padding-bottom: 10px;"
        >（权限位的修改，会将具有该角色权限的用户踢出登录状态，请慎重操作）</span
      >
    </div>
    <el-form class="dt-fz14 pdl-20 df">
      <el-form-item
        label="后台应用："
        v-if="$route.query.serverAppName"
        class="df"
        style="margin-bottom:10px"
      >
        <el-select :value="$route.query.serverAppName" disabled></el-select>
      </el-form-item>
      <el-form-item
        label="前台应用："
        v-if="$route.query.clientAppName"
        class="df"
        style="margin-bottom:10px"
      >
        <el-select :value="$route.query.clientAppName" disabled></el-select>
      </el-form-item>
    </el-form>
    <el-container class="wh100" style="padding-bottom:20px">
      <treeTransfer
        ref="tree"
        :treeList="treeList"
        title="已有配置"
        :toolListProps="toolListProps"
        :treeDefaultProps="treeDefaultProps"
        :defaultCheckedKeys="defaultCheckedKeys"
        nodeKey="authCode"
        nodeName="authName"
        @confirm="associateConfirm"
        @cancel="goBack"
      ></treeTransfer>
    </el-container>
  </div>
</template>

<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { selectAuthTree, saveOrUpdateRoleAuth } from "@/api/roleManagement/index";
import treeTransfer from "./components/treeTransfer";
export default {
  name: "permission",
  data() {
    return {
      menuListPros: {
        toolTitle: "权限配置"
      },
      toolListProps: {
        toolTitle: "权限配置",
        toolList: [
          {
            name: "清空",
            btnCode: ""
          }
        ]
      },
      param: {
        roleId: "",
        roleName: ""
      },
      treeList: [], // 树列表
      treeDefaultProps: {
        children: "children",
        label: "authName"
      },
      defaultCheckedKeys:[]
    };
  },
  components: {
    TableToolTemp,
    treeTransfer
  },
  created() {
    this.initData();
  },
  methods: {
    async initData() {
      this.param.roleId = this.$route.query.roleId;
      this.param.roleName = this.$route.query.roleName;
      let res = await selectAuthTree(this.param);
      if (res ) {
        let keys = [];
        this.treeList = this.formateDta(res, keys);
        this.defaultCheckedKeys = keys;
      }
    },
    //整理树的初始结构和整理勾选选中的值
    formateDta(allChildren, keys) {
      // 将接口数据整理成树需要的数据
      allChildren &&
        allChildren.forEach(item => {
          if (!item.children && item.isSelected)
            keys.push(item.authCode);
          if (item.children) {
            this.formateDta(item.children, keys);
          } 
        });
      return allChildren;
    },
    goBack() {
      this.$router.go(-1);
    },
    async associateConfirm(keys, nodes, nodesMap) {
      let operatAuth = [];
      let schema = {
        roleId: this.param.roleId,
        roleName: this.$route.query.roleName,
        tenantId: this.$store.state.layoutStore.currentLoginUser.tenantId,
        auths:[]
      };

      //递归获取完整路径
      let getFullPath = (item) => {
        let path = [];
        let current = item;

        // 从当前节点向上遍历到根节点
        while (current) {
          // 只添加有效的key值，过滤掉undefined和null
          if (current.key) {
            path.unshift(current.key);
          }
          current = current.parent;
        }

        // 如果路径数组不为空，则用/连接，否则返回空字符串
        let result = path.length > 0 ? path.join('/') : '';
        return result;
      };

      // 获取节点的所有父节点
      let getAllParents = (node) => {
        let parents = [];
        let current = node.parent;

        while (current && current.key) {
          parents.push({
            authCode: current.key,
            authPath: getFullPath(current)
          });
          current = current.parent;
        }

        return parents;
      };

      // 使用Set来避免重复的权限节点
      let authSet = new Set();
      let authMap = new Map(); // 用于存储完整的权限对象

      // 处理所有选中的节点
      nodes.forEach(node => {
        // 添加当前节点
        let currentAuth = {
          authCode: node.key,
          authPath: getFullPath(node)
        };

        let authKey = `${currentAuth.authCode}_${currentAuth.authPath}`;
        if (!authSet.has(authKey)) {
          authSet.add(authKey);
          authMap.set(authKey, currentAuth);
        }

        // 添加所有父节点
        let parents = getAllParents(node);
        parents.forEach(parent => {
          let parentKey = `${parent.authCode}_${parent.authPath}`;
          if (!authSet.has(parentKey)) {
            authSet.add(parentKey);
            authMap.set(parentKey, parent);
          }
        });
      });

      // 将Map中的值转换为数组
      operatAuth = Array.from(authMap.values());

      if (operatAuth.length == 0) {
        return this.$message({
          type: "error",
          message: "权限不能配置为空!"
        });
      }
      schema.auths = operatAuth




      let res = await saveOrUpdateRoleAuth(schema);
      if (res) {
        this.$message({
          type: "success",
          message: "保存成功"
        });
        this.$router.push({
          name: "roleList"
        });
      }
    }
  },
  watch: {
    $route(to, from) {
      if (to.name == "permission") {
        if (
          (to.query.roleId && to.query.roleId != this.param.roleId) ||
          (to.query.roleName && to.query.roleName != this.roleName)
        ) {
          this.initData();
        }
      }
    }
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  }
};
</script>

<style lang="less">
.main-bg {
  height: 100%;
  // padding-bottom: 20px;
}
.dt-menuList {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .bread {
    padding: 14px 0 14px 20px;
    border-bottom: 1px solid #eeeeee;
  }
  .tab-tool {
    line-height: 46px;
    /*padding-top: 0;*/
  }
  .tool-temp {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    span {
      color: #ff0000;
    }
  }
  .limitsListProps-title {
    padding-top: 38px;
  }
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    /*background-color: #fffcf7 !important;*/
  }
}
</style>

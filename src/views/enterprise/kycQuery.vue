<template>
  <InfoPageLayout
    title="KYC企业信息查询"
    subtitle="企业KYC核心信息展示"
    icon="el-icon-user"
    :breadcrumbItems="breadcrumbItems"
    @back="handleBack"
  >
    <div v-loading="loading">
      <!-- 基本信息卡片 -->
      <InfoCard title="企业基本信息" icon="el-icon-office-building" class="basic-info-card">
        <el-row :gutter="20">
          <el-col :span="8">
            <InfoItem label="公司名称" :value="basicInfo.name || '-'" />
          </el-col>
          <el-col :span="8">
            <InfoItem label="法定代表人" :value="basicInfo.legalPersonName || '-'" />
          </el-col>
          <el-col :span="8">
            <InfoItem label="纳税人识别号" :value="basicInfo.creditCode || '-'" />
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 10px;">
          <el-col :span="8">
            <InfoItem label="行业" :value="basicInfo.industry || '-'" />
          </el-col>
          <el-col :span="8">
            <InfoItem label="营业期限" :value="basicInfo.formattedBusinessTerm || '-'" />
          </el-col>
          <el-col :span="8">
            <InfoItem label="注册地址" :value="basicInfo.regLocation || '-'" />
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 10px;">
          <el-col :span="24">
            <InfoItem label="经营范围" :value="basicInfo.businessScope || '-'" />
          </el-col>
        </el-row>
      </InfoCard>

      <!-- 股东和受益人信息卡片 -->
      <InfoCard title="股东和受益人信息" icon="el-icon-user-solid" class="stakeholder-info-card">
        <el-row :gutter="20">
          <el-col :span="12">
            <InfoItem label="股东名" :value="getShareholderNames()" />
          </el-col>
          <el-col :span="12">
            <InfoItem label="最终受益人名称" :value="getBeneficiaryNames()" />
          </el-col>
        </el-row>
      </InfoCard>
    </div>
  </InfoPageLayout>
</template>

<script>
import InfoPageLayout from '@/components/layouts/InfoPageLayout'
import InfoCard from '@/components/layouts/InfoCard'
import InfoItem from '@/components/layouts/InfoItem'
import { getEnterpriseFullInfo } from '@/api/enterprise'

export default {
  name: 'KycQuery',
  components: {
    InfoPageLayout,
    InfoCard,
    InfoItem
  },
  data() {
    return {
      loading: false,
      breadcrumbItems: [
        { text: '企业信息管理', icon: 'el-icon-office-building', to: { name: 'EnterpriseManagement' } },
        { text: 'KYC查询', icon: 'el-icon-user' }
      ],
      basicInfo: {},
      shareholderList: [],
      beneficiaryList: []
    }
  },
  created() {
    // 从路由参数获取企业信用代码
    const { creditCode } = this.$route.params

    if (creditCode) {
      this.loadEnterpriseData(creditCode)
    } else {
      this.$message.error('企业信用代码不能为空')
      this.$router.back()
    }
  },
  methods: {
    async loadEnterpriseData(creditCode) {
      this.loading = true
      try {
        const data = await getEnterpriseFullInfo(creditCode)
        console.log('KYC API响应:', data)

        this.basicInfo = data.basicInfo || {}
        this.shareholderList = data.shareholderList || []
        this.beneficiaryList = data.beneficiaryList || []

        console.log('KYC设置的数据:', {
          basicInfo: this.basicInfo,
          shareholderList: this.shareholderList,
          beneficiaryList: this.beneficiaryList
        })
      } catch (error) {
        console.error('加载KYC企业信息失败:', error)
        this.$message.error('加载企业信息失败')
      } finally {
        this.loading = false
      }
    },

    // 获取股东名称列表
    getShareholderNames() {
      if (!this.shareholderList || this.shareholderList.length === 0) {
        return '-'
      }
      return this.shareholderList.map(shareholder => shareholder.name).filter(name => name).join('、') || '-'
    },

    // 获取最终受益人名称列表
    getBeneficiaryNames() {
      if (!this.beneficiaryList || this.beneficiaryList.length === 0) {
        return '-'
      }
      return this.beneficiaryList.map(beneficiary => beneficiary.name).filter(name => name).join('、') || '-'
    },

    // 返回上一页
    handleBack() {
      this.$router.back()
    }
  }
}
</script>

<style lang="less" scoped>
.basic-info-card,
.stakeholder-info-card {
  margin-bottom: 20px;
}
</style>

<template>
  <div class="questionnaire-management-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="问卷管理"
      subtitle="管理所有问卷，支持新建、编辑、预览和删除操作"
      title-icon="el-icon-document"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchParamsWithParam"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="400"
      :search-label-width="'120px'"
      add-button-text="新建问卷"
      empty-title="暂无问卷数据"
      empty-description="点击上方新建问卷按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 适用企业类型列插槽 -->
      <template #enterpriseTypeList="{ row }">
        <div v-if="row.enterpriseTypeList && row.enterpriseTypeList.length > 0">
          <el-tag
            v-for="type in row.enterpriseTypeList"
            :key="type"
            :type="getEnterpriseTypeTagType(type)"
            size="small"
            class="enterprise-type-tag"
            style="margin-right: 4px; margin-bottom: 4px;"
          >
            {{ getEnterpriseTypeName(type) }}
          </el-tag>
        </div>
        <span v-else class="no-enterprise-type">全部类型</span>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag
          :type="row.status === 1 ? 'success' : row.status === 2 ? 'warning' : 'info'"
          size="small"
        >
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <template #updateTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.updateTime }}
        </div>
      </template>
    </UniversalTable>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该问卷？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import { getQuestionnaireList, deleteQuestionnaire } from '@/api/questionnaire/management'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'
import {getEnterpriseTypeOptions} from "@/api/enterprise/type";

export default {
  name: 'QuestionnaireManagement',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  computed: {
    // 添加一个计算属性，将searchForm转换为带param的结构
    searchParamsWithParam() {
      return {
        param: { ...this.searchForm }
      }
    },

    // 搜索表单配置 - 使用计算属性确保响应式更新
    searchFormConfig() {
      return [
        {
          label: '问卷标题',
          name: 'title',
          type: 'input',
          placeholder: '请输入问卷标题'
        },
        {
          label: '描述',
          name: 'description',
          type: 'input',
          placeholder: '请输入描述'
        },
        {
          label: '企业类型',
          name: 'enterpriseType',
          type: 'select',
          placeholder: '请选择企业类型',
          list: this.enterpriseTypeOptions.map(item => ({
            dicItemName: item.name || item.label,
            dicItemCode: item.code || item.value
          }))
        }
      ]
    }
  },
  data() {
    return {
      loading: false,
      enterpriseTypeLoading: false, // 企业类型选项加载状态
      tableData: [],
      searchForm: {
        title: '',
        description: '',
        enterpriseType: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      enterpriseTypeOptions: [], // 企业类型选项
      // 表格列配置
      tableColumns: [
        {
          prop: 'title',
          label: '问卷标题',
          minWidth: 200,
          align: 'center'
        },
        {
          prop: 'description',
          label: '描述',
          minWidth: 300,
          align: 'center'
        },
        {
          prop: 'enterpriseTypeList',
          label: '适用企业类型',
          minWidth: 180,
          align: 'center'
        },
        {
          prop: 'questionCount',
          label: '题目数',
          width: 100,
          align: 'center'
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          align: 'center'
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          width: 180,
          align: 'center'
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          class: 'edit-btn',
          size: 'mini'
        },
        {
          key: 'configQuestions',
          label: '配置题目',
          icon: 'el-icon-menu',
          class: 'config-btn',
          size: 'mini'
        },
        {
          key: 'preview',
          label: '预览',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn',
          size: 'mini'
        }
      ],

      deleteRowData: null
    }
  },
  created() {
    this.loadQuestionnaireList()
    this.loadEnterpriseTypeOptions()
  },
  methods: {
    // 加载问卷列表
    async loadQuestionnaireList() {
      try {
        this.loading = true
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }

        const response = await getQuestionnaireList(params)

        if (response) {
          this.tableData = response.list
          this.pagination.total = response.total || 0
        } else if (response && Array.isArray(response)) {
          // 兼容直接返回数组的情况
          this.tableData = response
          this.pagination.total = response.length
        } else {
          console.warn('问卷列表数据格式异常:', response)
          this.tableData = []
          this.pagination.total = 0
        }
      } catch (error) {
        console.error('加载问卷列表失败:', error)
        this.$message.error('加载问卷列表失败')
        this.tableData = []
        this.pagination.total = 0
      } finally {
        this.loading = false
      }
    },

    // 加载企业类型选项
    async loadEnterpriseTypeOptions() {
      try {
        this.enterpriseTypeLoading = true
        const response = await getEnterpriseTypeOptions()
        if (response && Array.isArray(response)) {
          this.enterpriseTypeOptions = response
          console.log('企业类型选项加载成功:', response)
        } else {
          console.warn('企业类型选项数据格式异常:', response)
          this.enterpriseTypeOptions = []
        }
      } catch (error) {
        console.error('加载企业类型选项失败:', error)
        this.$message.error('加载企业类型选项失败')
        this.enterpriseTypeOptions = []
      } finally {
        this.enterpriseTypeLoading = false
      }
    },

    // 搜索
    handleSearch(searchParams) {
      this.searchForm = { ...searchParams.param }
      this.pagination.pageNum = 1
      this.loadQuestionnaireList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        description: '',
        enterpriseType: ''
      }
      this.pagination.pageNum = 1
      this.loadQuestionnaireList()
    },

    // 新增问卷
    handleAdd() {
      this.$router.push({ name: 'questionnaireEdit' })
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadQuestionnaireList()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadQuestionnaireList()
    },

    // 操作按钮点击
    handleAction(eventData) {
      const { action, row } = eventData
      switch (action) {
        case 'edit':
          this.handleEdit(row)
          break
        case 'configQuestions':
          this.handleConfigQuestions(row)
          break
        case 'preview':
          this.handlePreview(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },

    // 编辑问卷
    handleEdit(row) {
      this.$router.push({
        name: 'questionnaireEdit',
        params: {
          id: row.id
        }
      })
    },

    // 配置题目
    handleConfigQuestions(row) {
      this.$router.push({
        name: 'questionConfig',
        params: {
          id: row.id
        }
      })
    },

    // 预览问卷
    handlePreview(row) {
      this.$router.push({
        name: 'questionnaireFill',
        params: {
          id: row.id
        }
      })
    },

    // 删除问卷
    handleDelete(row) {
      this.deleteRowData = row
      this.$refs.confirmDialog.show()
    },

    // 确认删除
    async confirmDelete() {
      try {
        await deleteQuestionnaire(this.deleteRowData.id)
        this.$message.success('删除成功')
        this.loadQuestionnaireList()
      } catch (error) {
        console.error('删除失败:', error)
        this.$message.error('删除失败')
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '启用',
        0: '禁用',
        2: '草稿'
      }
      return statusMap[status] || '未知'
    },

    // 获取企业类型标签类型
    getEnterpriseTypeTagType(type) {
      const typeMap = {
        'A': 'primary',
        'B': 'success',
        'C': 'info',
        'D': 'warning',
        'E': 'danger'
      }
      return typeMap[type] || 'default'
    },

    // 获取企业类型名称
    getEnterpriseTypeName(code) {
      if (!this.enterpriseTypeOptions || !Array.isArray(this.enterpriseTypeOptions)) {
        return code
      }

      // 兼容多种数据格式
      const enterpriseType = this.enterpriseTypeOptions.find(item =>
        item.value === code ||
        item.code === code ||
        item.dicItemCode === code
      )

      return enterpriseType ?
        (enterpriseType.label || enterpriseType.name || enterpriseType.dicItemName || code) :
        code
    }
  }
}
</script>

<style lang="less" scoped>
.questionnaire-management-container {
  min-height: 100vh;
  background: #fbf6ee;

  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;

    i {
      color: #D7A256;
      font-size: 14px;
    }
  }

  .enterprise-type-tag {
    margin-right: 4px;
    margin-bottom: 4px;
  }

  /* 确保企业类型标签内文字垂直居中 */
  /deep/ .enterprise-type-tag.el-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    vertical-align: middle;
  }

  .no-enterprise-type {
    color: #999;
    font-size: 12px;
    font-style: italic;
  }

  /deep/ .universal-table-container {
    .action-buttons {
      .action-btn {
        margin: 0 2px;
        white-space: nowrap;
        padding: 6px 12px;
        min-width: auto; // 移除固定最小宽度，让按钮自适应

        &.edit-btn {
          color: #409EFF;
          border-color: #409EFF;

          &:hover {
            background: #409EFF;
            color: white;
          }
        }

        &.view-btn {
          color: #67C23A;
          border-color: #67C23A;

          &:hover {
            background: #67C23A;
            color: white;
          }
        }

        &.config-btn {
          color: #E6A23C;
          border-color: #E6A23C;

          &:hover {
            background: #E6A23C;
            color: white;
          }
        }

        &.delete-btn {
          color: #F56C6C;
          border-color: #F56C6C;

          &:hover {
            background: #F56C6C;
            color: white;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="parameter-form-container">
    <el-form
      ref="parameterForm"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="parameter-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">
          <i class="el-icon-info"></i>
          基本信息
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="参数名称" prop="name">
              <el-input
                v-if="!isView"
                v-model="formData.name"
                placeholder="请输入参数名称"
              />
              <div v-else class="view-value">{{ formData.name || '暂无' }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="参数编码" prop="code">
              <el-input
                v-if="!isView"
                v-model="formData.code"
                placeholder="请输入参数编码"
              />
              <div v-else class="view-value">{{ formData.code || '暂无' }}</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select
                v-if="!isView"
                v-model="formData.category"
                placeholder="请选择分类"
                class="full-width"
              >
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div v-else class="view-value">{{ formData.category || '暂无' }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="数据类型" prop="dataType">
              <el-select
                v-if="!isView"
                v-model="formData.dataType"
                placeholder="请选择数据类型"
                class="full-width"
              >
                <el-option label="数值" value="number" />
                <el-option label="变量" value="variable" />
              </el-select>
              <div v-else class="view-value">{{ formData.dataType === 'number' ? '数值' : '变量' }}</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input
            v-if="!isView"
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入参数描述"
          />
          <div v-else class="view-value description-view">{{ formData.description || '暂无描述' }}</div>
        </el-form-item>
      </div>

      <!-- 数值设置 -->
      <div class="form-section">
        <div class="section-title">
          <i class="el-icon-setting"></i>
          数值设置
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="默认值" prop="defaultValue">
              <el-input-number
                v-if="!isView"
                v-model="formData.defaultValue"
                :precision="4"
                :step="0.1"
                class="full-width"
                placeholder="请输入参数默认值"
              />
              <div v-else class="view-value">{{ formData.defaultValue || '暂无' }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input
                v-if="!isView"
                v-model="formData.unit"
                placeholder="请输入单位（可选）"
              />
              <div v-else class="view-value">{{ formData.unit || '暂无' }}</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <div v-if="!isView" class="status-switch-wrapper">
                <el-switch
                  v-model="formData.status"
                  :active-value="1"
                  :inactive-value="0"
                  active-color="#67c23a"
                  inactive-color="#dcdfe6"
                />
                <span class="status-text">{{ formData.status === 1 ? '启用' : '禁用' }}</span>
              </div>
              <div v-else class="view-value">
                <el-tag :type="formData.status === 1 ? 'success' : 'danger'" size="small">
                  {{ formData.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions" v-if="!isView">
      <el-button @click="handleCancel" class="cancel-btn">
        取消
      </el-button>
      <el-button
        type="primary"
        @click="handleSave"
        :loading="saving"
        class="save-btn"
      >
        {{ isEdit ? '更新' : '保存' }}
      </el-button>
    </div>


  </div>
</template>

<script>
// 注意：此组件实际调用的是 getParameterCategoryList，不是 getConstantCategoryList

export default {
  name: 'ParameterForm',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      saving: false,
      categoryOptions: [],

      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入参数名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '参数名称必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入参数编码', trigger: 'blur' },
          { min: 3, max: 30, message: '长度在 3 到 30 个字符', trigger: 'blur' },
          { pattern: /^[A-Z][A-Z0-9_]*$/, message: '参数编码必须大写，只能包含大写字母、数字和下划线', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ],
        dataType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ],
        defaultValue: [
          { required: true, message: '请输入默认值', trigger: 'blur' }
        ]
      }
    };
  },

  watch: {
    formData: {
      handler(newVal) {
        this.initializeFormData(newVal);
      },
      deep: true,
      immediate: true
    },

  },

  mounted() {
    this.loadCategoryOptions();
  },

  methods: {
    // 初始化表单数据
    initializeFormData(data) {
      if (!data.status && data.status !== 0) {
        this.$set(this.formData, 'status', 1);
      }
      if (!data.unit) {
        this.$set(this.formData, 'unit', '');
      }
    },

    // 加载分类选项
    async loadCategoryOptions() {
      try {
        const response = await getParameterCategoryList();
        if (response.code === 200) {
          this.categoryOptions = response.data;
        }
      } catch (error) {
        // 加载分类选项失败
      }
    },



    // 保存
    async handleSave() {
      try {
        // 验证表单
        await this.$refs.parameterForm.validate();

        this.saving = true;
        this.$emit('save', { ...this.formData });
      } catch (error) {
        this.$message.error('表单验证失败');
      } finally {
        this.saving = false;
      }
    },

    // 取消
    handleCancel() {
      this.$emit('cancel');
    }
  }
};
</script>

<style lang="less" scoped>
.parameter-form-container {
  padding: 20px;

  .form-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #ebeef5;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .full-width {
      width: 100%;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;

    .cancel-btn {
      padding: 10px 20px;
      border-radius: 6px;
    }

    .save-btn {
      padding: 10px 20px;
      border-radius: 6px;
    }
  }
}

// 表单样式优化
.parameter-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }

    .el-input,
    .el-select,
    .el-input-number {
      .el-input__inner {
        border-radius: 6px;
        border: 1px solid #dcdfe6;

        &:focus {
          border-color: #409eff;
        }
      }
    }

    .el-textarea {
      .el-textarea__inner {
        border-radius: 6px;
        border: 1px solid #dcdfe6;

        &:focus {
          border-color: #409eff;
        }
      }
    }

    .status-switch-wrapper {
      display: flex;
      align-items: center;
      gap: 12px;

      .status-text {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }
    }

    .view-value {
      padding: 8px 12px;
      background: #f5f7fa;
      border-radius: 4px;
      color: #606266;
      font-size: 14px;
      line-height: 1.4;
      min-height: 20px;

      &.description-view {
        white-space: pre-wrap;
        word-break: break-word;
      }
    }


  }
}
</style>

<template>
  <div class="parameter-usage-container">
    <div class="usage-header">
      <div class="parameter-info">
        <div class="parameter-name">
          <i class="el-icon-variable"></i>
          {{ usageData.parameterName }}
        </div>
        <div class="usage-stats">
          <el-tag type="success" size="small">
            使用次数: {{ usageData.usageCount }}
          </el-tag>
          <el-tag type="info" size="small" v-if="usageData.lastUsedTime">
            最后使用: {{ usageData.lastUsedTime }}
          </el-tag>
        </div>
      </div>
    </div>

    <div class="usage-content">
      <div class="usage-section">
        <div class="section-title">
          <i class="el-icon-data-analysis"></i>
          使用公式列表
        </div>

        <div v-if="loading" class="loading-wrapper">
          <el-loading-spinner></el-loading-spinner>
          <p>加载中...</p>
        </div>

        <div v-else-if="usageData.usedInFormulas && usageData.usedInFormulas.length > 0" class="formula-list">
          <div
            v-for="formula in usageData.usedInFormulas"
            :key="formula.formulaId"
            class="formula-item"
          >
            <div class="formula-header">
              <div class="formula-name">
                <i class="el-icon-data-analysis"></i>
                {{ formula.formulaName }}
              </div>
              <div class="formula-category">
                <el-tag size="small" type="primary">
                  {{ formula.category }}
                </el-tag>
              </div>
            </div>

            <div class="formula-actions">
              <el-button
                type="text"
                size="small"
                icon="el-icon-view"
                @click="viewFormula(formula.formulaId)"
              >
                查看公式
              </el-button>
              <el-button
                type="text"
                size="small"
                icon="el-icon-edit"
                @click="editFormula(formula.formulaId)"
              >
                编辑公式
              </el-button>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <i class="el-icon-document"></i>
          <p>该参数暂未被任何公式使用</p>
        </div>
      </div>

      <div class="usage-section" v-if="usageData.usageCount > 0">
        <div class="section-title">
          <i class="el-icon-pie-chart"></i>
          使用统计
        </div>

        <div class="usage-chart">
          <div class="chart-item">
            <div class="chart-label">使用频率</div>
            <div class="chart-value">
              <el-progress
                :percentage="getUsagePercentage()"
                :color="getUsageColor()"
                :stroke-width="8"
              />
            </div>
          </div>

          <div class="chart-item">
            <div class="chart-label">分类分布</div>
            <div class="category-distribution">
              <div
                v-for="(count, category) in getCategoryDistribution()"
                :key="category"
                class="category-item"
              >
                <span class="category-name">{{ category }}</span>
                <span class="category-count">{{ count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="usage-footer">
      <el-button @click="handleClose" class="close-btn">
        关闭
      </el-button>
    </div>
  </div>
</template>

<script>
// 注意：此组件实际调用的是 getParameterUsage，不是 getConstantUsage

export default {
  name: 'ParameterUsage',
  props: {
    parameterId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      loading: true,
      usageData: {
        parameterId: null,
        parameterName: '',
        usageCount: 0,
        usedInFormulas: [],
        lastUsedTime: ''
      }
    };
  },

  mounted() {
    this.loadUsageData();
  },

  methods: {
    // 加载使用情况数据
    async loadUsageData() {
      this.loading = true;
      try {
        const response = await getParameterUsage(this.parameterId);
        if (response.code === 200) {
          this.usageData = response.data;
        } else {
          this.$message.error(response.message || '加载使用情况失败');
        }
      } catch (error) {
        this.$message.error('加载使用情况失败');
      } finally {
        this.loading = false;
      }
    },

    // 获取使用百分比
    getUsagePercentage() {
      if (this.usageData.usageCount === 0) return 0;

      // 根据使用次数计算百分比（这里可以根据实际需求调整计算逻辑）
      const maxUsage = 20; // 假设最大使用次数为20
      return Math.min((this.usageData.usageCount / maxUsage) * 100, 100);
    },

    // 获取使用颜色
    getUsageColor() {
      const percentage = this.getUsagePercentage();
      if (percentage < 30) return '#67c23a';
      if (percentage < 70) return '#e6a23c';
      return '#f56c6c';
    },

    // 获取分类分布
    getCategoryDistribution() {
      const distribution = {};
      if (this.usageData.usedInFormulas) {
        this.usageData.usedInFormulas.forEach(formula => {
          distribution[formula.category] = (distribution[formula.category] || 0) + 1;
        });
      }
      return distribution;
    },

    // 查看公式
    viewFormula(formulaId) {
      this.$router.push({
        path: '/formula-engine',
        query: {
          action: 'view',
          id: formulaId
        }
      });
      this.handleClose();
    },

    // 编辑公式
    editFormula(formulaId) {
      this.$router.push({
        path: '/formula-engine',
        query: {
          action: 'edit',
          id: formulaId
        }
      });
      this.handleClose();
    },

    // 关闭
    handleClose() {
      this.$emit('close');
    }
  }
};
</script>

<style lang="less" scoped>
.parameter-usage-container {
  padding: 20px;

  .usage-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;

    .parameter-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .parameter-name {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .usage-stats {
        display: flex;
        gap: 8px;
      }
    }
  }

  .usage-content {
    .usage-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 16px;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .loading-wrapper {
        text-align: center;
        padding: 40px 0;
        color: #909399;

        p {
          margin-top: 12px;
        }
      }

      .formula-list {
        .formula-item {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          border: 1px solid #e9ecef;

          &:last-child {
            margin-bottom: 0;
          }

          .formula-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .formula-name {
              font-weight: 500;
              color: #303133;
              display: flex;
              align-items: center;

              i {
                margin-right: 8px;
                color: #409eff;
              }
            }

            .formula-category {
              .el-tag {
                border-radius: 4px;
              }
            }
          }

          .formula-actions {
            display: flex;
            gap: 12px;

            .el-button {
              padding: 4px 8px;
              font-size: 12px;

              &:first-child {
                color: #409eff;
              }

              &:last-child {
                color: #67c23a;
              }
            }
          }
        }
      }

      .empty-state {
        text-align: center;
        padding: 40px 0;
        color: #909399;

        i {
          font-size: 48px;
          margin-bottom: 16px;
          color: #c0c4cc;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .usage-chart {
        .chart-item {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .chart-label {
            font-weight: 500;
            color: #606266;
            margin-bottom: 8px;
          }

          .chart-value {
            .el-progress {
              .el-progress-bar {
                .el-progress-bar__outer {
                  border-radius: 4px;
                }
              }
            }
          }

          .category-distribution {
            .category-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 0;
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              .category-name {
                color: #606266;
                font-size: 14px;
              }

              .category-count {
                color: #409eff;
                font-weight: 500;
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  .usage-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;

    .close-btn {
      padding: 8px 20px;
      border-radius: 6px;
    }
  }
}
</style>

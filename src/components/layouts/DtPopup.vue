<template>
	<div class="">
		<el-dialog
			:title="title"
			:visible.sync="show"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="isShowClose"
			:width="width"
			:center="isCenter"
      :destroy-on-close="destroyOnClose"
			class="dt-popup"
			:class="{isCenter: center}"
			@close="closePopup"
		>
			<slot></slot>
			<span v-if="footer" slot="footer" class="dialog-footer">
				<el-button v-if="isShowClose" class="dt-btn" type="primary" plain :style="{color:$store.state.layoutStore.themeObj.color}" @click="show = false">{{closeText}}</el-button>
				<el-button type="primary" class="dt-btn" @click="confirm">{{confirmText}}</el-button>
			</span>
			<span v-if="isShowClose" class="iconfont icondt25" style="cursor: pointer;" @click="show = false"></span>
		</el-dialog>
	</div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "系统提示"
    },
    isShow: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: "670px"
    },
    footer: {
      type: Boolean,
      default: true
    },
    center: {
      type: Boolean,
      default: false
    },
    isCenter: {
      type: Boolean,
      default: true
    },
    isShowClose: {
      type: Boolean,
      default: true
    },
    destroyOnClose: {
      type: Boolean,
      default: true
    },
	closeText:{
    	type:String,
		default:"取 消"
	},
  	confirmText:{
		type:String,
		default:"确 认"
	}
  },
  data() {
    return {
      show: this.isShow
    };
  },
  watch:{
    isShow(newVal,oldVal) {
      this.show = newVal;
      if(this.show){
        this.$emit('open')
      }
    }
  },
  methods: {
    closePopup() {
      this.$emit("close");
    },
    confirm() {
       this.$emit("confirm",()=>{
            this.show = false;
        });
    }
  }
};
</script>

<style lang="less">
.dt-popup {
  .el-dialog {
    min-width: 300px;
    min-height: 150px;
    border-radius: 10px;
    position: absolute;
    margin: 0!important;
    width: auto;
    padding: 0 40px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 90vh; // 限制弹窗最大高度
    overflow: hidden; // 防止弹窗本身溢出

    .el-dialog__body {
      padding: 0;
      max-height: calc(90vh - 120px); // 减去头部和底部的高度
      overflow-y: auto; // 内容区域可滚动

      .dt-popup-wrap{
          max-height: calc(90vh - 140px);
          overflow-y: auto;
          width:calc(~'100% - 10px');
          padding-right:10px;
      }
    }
    .el-dialog__header {
      padding-bottom: 20px;
      flex-shrink: 0; // 防止头部被压缩
    }
    .el-dialog__footer {
      padding-bottom: 40px;
      padding-top: 20px;
      flex-shrink: 0; // 防止底部被压缩
    }

    // 小屏幕优化
    @media (max-height: 800px) {
      max-height: 85vh;
      padding: 0 30px;

      .el-dialog__body {
        max-height: calc(85vh - 100px);

        .dt-popup-wrap{
          max-height: calc(85vh - 120px);
        }
      }

      .el-dialog__header {
        padding-bottom: 15px;
      }

      .el-dialog__footer {
        padding-bottom: 30px;
        padding-top: 15px;
      }
    }

    @media (max-height: 700px) {
      max-height: 83vh;
      padding: 0 25px;

      .el-dialog__body {
        max-height: calc(83vh - 90px);

        .dt-popup-wrap{
          max-height: calc(83vh - 110px);
        }
      }

      .el-dialog__header {
        padding-bottom: 12px;
      }

      .el-dialog__footer {
        padding-bottom: 25px;
        padding-top: 12px;
      }
    }

    @media (max-height: 600px) {
      max-height: 75vh;
      padding: 0 20px;

      .el-dialog__body {
        max-height: calc(75vh - 140px);

        .dt-popup-wrap{
          max-height: calc(75vh - 100px);
        }
      }

      .el-dialog__header {
        padding-bottom: 10px;
      }

      .el-dialog__footer {
        padding-bottom: 20px;
        padding-top: 10px;
      }
    }

    // 宽度响应式优化
    @media (max-width: 1200px) {
      max-width: 90vw;
    }

    @media (max-width: 900px) {
      max-width: 95vw;
      padding: 0 15px;
    }

    @media (max-width: 600px) {
      max-width: 98vw;
      padding: 0 10px;
    }
  }
  &.isCenter {
    .el-dialog__body {
      display: flex;
      justify-content: center;
    }
  }
  &.small {
    .el-dialog {
      width: 700px;

      @media (max-width: 800px) {
        width: 90vw;
      }
    }
  }
  &.mini {
    .el-dialog {
      width: 400px;

      @media (max-width: 500px) {
        width: 95vw;
      }
    }
  }
  &.large {
    .el-dialog {
      width: 1000px;

      @media (max-width: 1100px) {
        width: 90vw;
      }
    }
  }

  // 针对特定弹窗的优化
  &.restart-close-popup,
  &.assign-popup,
  &.manager-popup {
    .el-dialog {
      @media (max-width: 1000px) {
        width: 90vw;
      }

      @media (max-width: 768px) {
        width: 95vw;
        padding: 0 15px;
      }

      @media (max-width: 600px) {
        width: 98vw;
        padding: 0 10px;
      }
    }
  }
  .icondt25 {
    position: absolute;
    font-size: 65px !important;
    top: -12px ;
    right: -11px ;
    color: #B8B8B8;
  }
  .dialog-footer {

  }
  .el-dialog__title{
    font-weight: bold;
  }
}
</style>

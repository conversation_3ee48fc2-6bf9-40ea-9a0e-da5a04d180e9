import http from "@/utils/httpService";
import {
  rootPath
} from "@/utils/globalParam";

// 用户列表分页接口
export const getUserList = (data) =>
  http.Axios.post(rootPath + "/api/user/page", data);

// 用户权限树接口
export const selectAuthTree = (data) =>
  http.Axios.post(rootPath + "/api/user/selectAuthTree", data);

// 根据云服账号获取用户信息
export const findByBscUserName = (data) =>
  http.Axios.post(rootPath + "/api/user/findByBscUserName", data);

// 用户详情
export const detail = (data) =>
    http.Axios.post(rootPath + "/api/user/detail", data);

// 用户增改
export const saveOrUpdate = (data) =>
  http.Axios.post(rootPath + "/api/user/saveOrUpdate", data);

// 删除用户
export const deleteUser = (data) =>
  http.Axios.post(rootPath + "/api/user/deleteUser", data);

// 获取行业
export const findIndustryList = (data) =>
  http.Axios.post(rootPath + "/api/industry/findIndustryList", data);

// 擅长险种查询
export const findInsuranceList = (data) =>
  http.Axios.post(rootPath + "/api/insurance/list", data);

// 擅长险种新增
export const saveInsurance = (data) =>
  http.Axios.post(rootPath + "/api/insurance/save", data);

// 擅长险种删除
export const deleteInsurance = (data) =>
  http.Axios.post(rootPath + "/api/insurance/delete", data);

// 擅长险种修改排序
export const updateInsuranceSort = (data) =>
  http.Axios.post(rootPath + "/api/insurance/updateSort", data);

// 根据行业code获取行业集合数据
export const findIndustryListByCode = (data) =>
  http.Axios.post(rootPath + "/api/industry/findIndustryListByCode", data);

// 用户启用/禁用
export const setUserStatus = (data) =>
  http.Axios.post(rootPath + "/api/user/setUserStatus", data);

// 角色权限配置
export const saveOrUpdateRoleAuth = (data) =>
  http.Axios.post(rootPath + "/api/user/saveOrUpdateRoleAuth", data);

// 分配角色
export const addUserRole = (data) =>
  http.Axios.post(rootPath + "/api/user/addUserRole", data);

// 查用户所属角色
export const findRolesByUserId = (data) =>
  http.Axios.post(rootPath + "/api/user/findRolesByUserId", data);

// 删除角色
export const deleteUserRole = (data) =>
  http.Axios.post(rootPath + "/api/user/deleteUserRole", data);

// 租户下所有角色
export const getRoleList = (data) =>
  http.Axios.post(rootPath + "/api/role/list", data);

// 总部树结构数据查询
export const findDepartmentData = (data) =>
  http.Axios.post(rootPath + "/api/user/findDepartmentData", data);

// 分公司树结构数据查询
export const findOrganizationData = (data) =>
  http.Axios.post(rootPath + "/api/user/findOrganizationData", data);

// 法人机构列表数据
export const findLegalOrgData = (data) =>
  http.Axios.post(rootPath + "/api/user/findLegalOrgData", data);

// 分公司数据
export const findCompanyOrgData = (data) =>
  http.Axios.post(rootPath + "/api/user/findCompanyOrgData", data);

// 机构权限配置
export const saveOrUpdateUserOrg = (data) =>
  http.Axios.post(rootPath + "/api/user/saveOrUpdateUserOrg", data);

// 获取用户选中的机构权限
export const getCheckedUserOrg = (data) =>
  http.Axios.post(rootPath + "/api/user/getCheckedUserOrg", data);

// 获取当前用户归属租户相关信息
export const getCurrentUserTenantInfo = (data) =>
  http.Axios.post(rootPath + "/api/user/getCurrentUserTenantInfo", data);

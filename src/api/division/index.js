import http from "@/utils/httpService";
import {
  rootPath
} from "@/utils/globalParam";

// 列表分页接口
export const getDivisionPage = (data) =>
  http.Axios.post(rootPath + "/api/division/ratio/page", data);

// 新增
export const saveDivision = (data) =>
  http.Axios.post(rootPath + "/api/division/ratio/add", data);

// 修改
export const updateDivision = (data) =>
  http.Axios.post(rootPath + "/api/division/ratio/update", data);

// 删除
export const deleteDivision = (data) =>
  http.Axios.post(rootPath + "/api/division/ratio/delete", data);

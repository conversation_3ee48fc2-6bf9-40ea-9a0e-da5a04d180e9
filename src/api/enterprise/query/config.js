// 企业查询配置相关 API
import http from "@/utils/httpService";
import { rootPath } from '@/utils/globalParam';

// 获取查询使用统计列表
export const getQueryUsageStatistics = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/query/config/statistics", data);

// 获取通用配置
export const getGeneralConfig = () =>
  http.Axios.get(rootPath + "/api/enterprise/query/config/general");

// 更新通用配置
export const updateGeneralConfig = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/query/config/general/update", data);

// 设置个人配置
export const setPersonalConfig = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/query/config/personal/set", data);

// 删除个人配置
export const removePersonalConfig = (agentCode) =>
  http.Axios.post(rootPath + "/api/enterprise/query/config/personal/remove", null, {
    params: { agentCode }
  });

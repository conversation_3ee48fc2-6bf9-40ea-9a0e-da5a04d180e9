// 企业信息管理相关 API
import http from "@/utils/httpService";
import { rootPath } from '@/utils/globalParam';

// 获取企业信息分页列表
export const getEnterpriseList = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/base/info/page", data);

// 获取企业详情
export const getEnterpriseDetail = (id) => {
  return http.Axios.post(rootPath + "/api/enterprise/base/basic", { creditCode: id });
};

// 删除企业
export const deleteEnterprise = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/base/delete", data);

// 获取企业完整信息（根据信用代码）
export const getEnterpriseFullInfo = (creditCode) =>
  http.Axios.get(rootPath + `/api/enterprise/base/complete/${creditCode}`);

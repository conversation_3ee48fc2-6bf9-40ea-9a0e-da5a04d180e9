import http from "@/utils/httpService";
import { rootPath} from "@/utils/globalParam";

//获取登录用户在该应用下的租户
export const getWebUserInfo = data =>
  http.Axios.post(rootPath + "/api/bsc/getWebUserInfo", data, {
    noLoading: true
});

//获取登录用户在该应用下权限位等信息
export const getTenantUsers = data =>
  http.Axios.post(rootPath + "/api/bsc/tenant/getTenantUsers", data, {
    noLoading: true
});

// 获取企客用户权限
export const getQikeUserInfo = data =>
  http.Axios.post(rootPath + "/api/user/getCurrentUserAuth", data, {
    noLoading: true
});



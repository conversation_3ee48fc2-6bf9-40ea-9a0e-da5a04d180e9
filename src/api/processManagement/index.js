import http from "@/utils/httpService";
import {
  rootPath
} from "@/utils/globalParam";



export const getLegalList = (data) =>
  http.Axios.post(rootPath + "/api/bsc/getLegalList", data);




export const getTransactionsList = (data) =>
  http.Axios.post(rootPath + "/api/bsc/getTransactionsList", data);



export const getProcessDefineList = (data) =>
  http.Axios.post(rootPath + "/api/processDefine/list", data);

export const copyProcessDefine = (data) =>
  http.Axios.post(rootPath + "/api/processDefine/copy", data);

export const deleteProcessDefine = (data) =>
  http.Axios.post(rootPath + "/api/processDefine/delete", data);

export const startProcessDefine = (data) =>
  http.Axios.post(rootPath + "/api/processDefine/start", data);

export const stopProcessDefine = (data) =>
  http.Axios.post(rootPath + "/api/processDefine/stop", data);

  export const saveProcessDefine = (data) =>
  http.Axios.post(rootPath + "/api/processDefine/save", data);
export const getProcessDefineDetail = (data) =>
  http.Axios.post(rootPath + "/api/processDefine/detail", data);

export const getBasecodeTree = (data) =>
  http.Axios.post(rootPath + "/api/basecode/tree", data);

export const getBranchUsers = (data) =>
  http.Axios.post(rootPath + "/api/user/branchUsers", data);



export const getIndustryTree = (data) =>
  http.Axios.post(rootPath + "/api/industry/tree", data);



export const getEnterpriseTypeEnum = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/type/enum", data);







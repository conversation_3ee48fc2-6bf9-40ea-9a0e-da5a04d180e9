/* 级联选择器交互优化样式 */

/* 多选标签样式优化 */
.el-cascader .el-tag {
  margin: 2px 6px 2px 0;
  border-radius: 12px;
  border: 1px solid #dcdfe6;
  background-color: #f4f4f5;
  color: #606266;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
  transition: all 0.3s ease;
}

.el-cascader .el-tag:hover {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.el-cascader .el-tag .el-icon-close {
  font-size: 12px;
  margin-left: 4px;
  color: #c0c4cc;
  transition: color 0.3s ease;
}

.el-cascader .el-tag .el-icon-close:hover {
  color: #409eff;
  background-color: transparent;
}

/* 折叠标签样式 */
.el-cascader .el-tag--info {
  background-color: #f0f9ff;
  border-color: #91d5ff;
  color: #1890ff;
  font-weight: 500;
}

/* 级联面板样式优化 */
.el-cascader-panel {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.el-cascader-panel .el-cascader-node {
  padding: 8px 12px;
  border-radius: 4px;
  margin: 2px 4px;
  transition: all 0.3s ease;
}

.el-cascader-panel .el-cascader-node:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.el-cascader-panel .el-cascader-node.is-selectable.in-active-path {
  background-color: #ecf5ff;
  color: #409eff;
  font-weight: 500;
}

.el-cascader-panel .el-cascader-node.is-active {
  background-color: #409eff;
  color: #fff;
  font-weight: 500;
}

/* 搜索输入框样式 */
.el-cascader .el-input .el-input__inner {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.el-cascader .el-input .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 清空按钮样式 */
.el-cascader .el-input__icon.el-icon-circle-close {
  color: #c0c4cc;
  transition: color 0.3s ease;
}

.el-cascader .el-input__icon.el-icon-circle-close:hover {
  color: #909399;
}

/* 下拉箭头样式 */
.el-cascader .el-input__icon.el-icon-arrow-down {
  color: #c0c4cc;
  transition: all 0.3s ease;
}

.el-cascader .el-input__icon.el-icon-arrow-down.is-reverse {
  transform: rotate(180deg);
  color: #409eff;
}

/* 多选模式下的输入框优化 - 自适应高度 */
.el-cascader.is-multiple .el-input .el-input__inner {
  padding-left: 6px;
  padding-right: 30px;
  min-height: 32px;
  height: auto !important;  /* 允许高度自适应 */
  line-height: 1.3;
  padding-top: 4px;
  padding-bottom: 4px;
}

/* 级联选择器基础样式修复 */
.el-cascader {
  width: 100% !important;
  display: block !important;
}

.el-cascader .el-input {
  width: 100% !important;
  display: block !important;
}

.el-cascader .el-input .el-input__inner {
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 多选级联选择器的特殊处理 - 自适应高度 */
.el-cascader.is-multiple .el-input .el-input__inner {
  min-height: 34px !important;
  height: auto !important;
  line-height: 1.2 !important;
  padding: 4px 30px 4px 8px !important;
}

.universal-form-container .el-cascader .el-input,
.scoped-universal-form .el-cascader .el-input {
  width: 100% !important;
}

.universal-form-container .el-col[span="16"] .el-cascader .el-input,
.scoped-universal-form .el-col[span="16"] .el-cascader .el-input {
  width: 100% !important;
}

.universal-form-container .el-col[span="16"] .el-cascader.is-multiple .el-input .el-input__inner,
.scoped-universal-form .el-col[span="16"] .el-cascader.is-multiple .el-input .el-input__inner {
  min-height: 36px !important;
  padding: 4px 30px 4px 8px !important;
}

/* 标签样式优化 - 自适应显示 */
.el-cascader .el-tag {
  margin: 2px 6px 2px 0;
  max-width: 200px;
  border-radius: 12px;
  height: 24px;
  line-height: 22px;
  padding: 0 8px;
  font-size: 12px;
  display: inline-block;
  vertical-align: middle;
}

/* 宽字段的标签样式 */
.universal-form-container .el-col[span="16"] .el-cascader .el-tag,
.scoped-universal-form .el-col[span="16"] .el-cascader .el-tag {
  margin: 3px 6px 3px 0;
  max-width: 240px;  /* 增加最大宽度 */
  border-radius: 14px;
  height: 26px;
  line-height: 24px;
  padding: 0 10px;
  font-size: 13px;
  display: inline-block;
  vertical-align: middle;
}

/* 搜索表单中的级联选择器样式修复 */
.dt-cascader {
  width: 100% !important;
}

.dt-cascader .el-input {
  width: 100% !important;
}

.dt-cascader .el-input__inner {
  width: 100% !important;
  box-sizing: border-box !important;
}

.dt-cascader.is-multiple .el-input .el-input__inner {
  min-height: 32px !important;
  padding: 4px 30px 4px 8px !important;
}

/* 搜索表单中设置了自定义宽度的级联选择器 */
/* .dt-cascader[style*="width: 300px"] {
  width: 300px !important;
  min-width: 300px !important;
}

.dt-cascader[style*="width: 300px"] .el-input {
  width: 100% !important;
}

.dt-cascader[style*="width: 300px"] .el-input__inner {
  width: 100% !important;
  min-width: 300px !important;
} */

/* 级联选择器字段专用样式类 */
.cascader-field {
  width: 100% !important;
  display: block !important;
  box-sizing: border-box !important;
}

.cascader-field .el-input {
  width: 100% !important;
  display: block !important;
}

.cascader-field .el-input__inner {
  width: 100% !important;
  box-sizing: border-box !important;
}

.cascader-field.is-multiple .el-input__inner {
  min-height: 34px !important;
  height: auto !important;  /* 允许高度自适应 */
  padding: 4px 30px 4px 8px !important;
  line-height: 1.2 !important;
}

/* 确保标签能够自然换行 */
.cascader-field.is-multiple .el-input__inner {
  white-space: normal !important;
  overflow: visible !important;
}

/* 强制覆盖任何可能的样式冲突 */
.scoped-universal-form .cascader-field,
.universal-form-container .cascader-field {
  width: 100% !important;
  max-width: 100% !important;
}

.scoped-universal-form .cascader-field .el-input,
.universal-form-container .cascader-field .el-input {
  width: 100% !important;
  max-width: 100% !important;
}

.scoped-universal-form .cascader-field .el-input__inner,
.universal-form-container .cascader-field .el-input__inner {
  width: 100% !important;
  max-width: 100% !important;
}

/* 空状态提示 */
.el-cascader-panel .el-cascader-menu__empty-text {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

/* 加载状态 */
.el-cascader-panel .el-cascader-menu__loading-text {
  color: #409eff;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

/* 复选框样式优化 */
.el-cascader-panel .el-checkbox {
  margin-right: 8px;
}

.el-cascader-panel .el-checkbox__inner {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.el-cascader-panel .el-checkbox__inner:hover {
  border-color: #409eff;
}

.el-cascader-panel .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409eff;
  border-color: #409eff;
}

.el-cascader-panel .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #409eff;
  border-color: #409eff;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .el-cascader-panel {
    max-width: 90vw;
  }
  
  .el-cascader .el-tag {
    font-size: 11px;
    height: 22px;
    line-height: 20px;
    padding: 0 6px;
  }
  
  .el-cascader.is-multiple .el-input .el-input__inner {
    min-height: 28px;
  }
}

/* 高亮搜索结果 */
.el-cascader-panel .el-cascader-node > .el-cascader-node__label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-cascader-panel .el-cascader-node > .el-cascader-node__label mark {
  background-color: #fff566;
  color: #333;
  padding: 0 2px;
  border-radius: 2px;
}

/* 动画效果 */
.el-cascader-panel {
  animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 滚动条样式 */
.el-cascader-menu {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

.el-cascader-menu::-webkit-scrollbar {
  width: 6px;
}

.el-cascader-menu::-webkit-scrollbar-track {
  background: transparent;
}

.el-cascader-menu::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

.el-cascader-menu::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}


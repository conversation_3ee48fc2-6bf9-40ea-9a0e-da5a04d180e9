/* 级联选择器交互优化样式 */
@import './cascader-optimize.css';

.clearfix:before,
.clearfix:after {
  display: table;
  content: " ";
}
.clearfix:after {
  clear: both;
}
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
::-webkit-scrollbar-track-piece {
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:vertical {
  height: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:horizontal {
  width: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}
.el-popover {
  padding: 0 !important;
}
.dt-input-width {
  width: 217px !important;
}
.dt-input-width-max {
  width: 300px !important;
}
.dt-textarea-width {
  width: 426px !important;
}
.dt-el-tabs {
  margin-left: -15px;
  margin-right: -15px;
}
.dt-fz14 {
  font-size: 14px;
}
.dt-fz15 {
  font-size: 15px;
}
.dt-fz16 {
  font-size: 16px;
}
.dt-fz22 {
  font-size: 22px;
}
.text-c {
  text-align: center;
}
.c-999 {
  color: #999;
}
.c-333 {
  color: #333;
}
.mr-20 {
  margin-right: 20px;
}
.ml-20 {
  margin-left: 20px;
}
.ptb-20 {
  padding: 20px 0;
}
.plr-20 {
  padding:0 20px;
}
.df {
  display: flex;
}
.df-ac {
  display: flex;
  align-items: center;
}
.df-cc {
  display: flex;
  align-items: center;
  justify-content: center;
}
.dt-table.el-table::before {
  height: 0;
  width: 0;
}
.dt-table.el-table thead {
  color: #333;
}
.dt-table.el-table .el-table__row td {
  border: none;
}
.dt-table.el-table tr th {
  background-color: #F9F9F9;
}
.dt-table.el-table tr th .cell {
  padding-left: 14px;
}
.dt-table.el-table tr th.is-leaf {
  border: none;
}
.dt-bread {
  padding: 14px 0 14px 20px;
  border-bottom: 1px solid #eeeeee;
}
.dt-bread .el-breadcrumb__inner.is-link {
  color: #999 !important;
  font-size: 14px;
  font-weight: 400 !important;
}
.dt-bread .el-breadcrumb__inner {
  color: inherit !important;
}

export default [
  {
    path: '/data-template',
    name: 'dataTemplate',
    component: () => import('@/views/basicConfig/dataTemplate/index.vue'),
    meta: { title: '数据模板配置' }
  },
  {
    path: '/service-process',
    name: 'serviceProcess',
    component: () => import('@/views/basicConfig/serviceProcess/index.vue'),
    meta: { title: '服务流程配置' }
  },
  {
    path: '/data-template/fields-config/:id',
    name: 'FieldsConfig',
    component: () => import('@/views/basicConfig/dataTemplate/fieldsConfig.vue')
  },
  {
    path: '/data-template/field-mapping/:id',
    name: 'FieldMapping',
    component: () => import('@/views/basicConfig/dataTemplate/fieldMapping.vue')
  },
  {
    path: '/data-template/edit/:id?',
    name: 'dataTemplateEdit',
    component: () => import('@/views/basicConfig/dataTemplate/edit.vue'),
    meta: { title: '数据模板编辑' }
  },
  {
    path: '/data-template/view/:id',
    name: 'dataTemplateView',
    component: () => import('@/views/basicConfig/dataTemplate/view.vue'),
    meta: { title: '查看数据模板' }
  },
  {
    path: '/form-config',
    name: 'formConfig',
    component: () => import('@/views/basicConfig/formConfig/index.vue'),
    meta: { title: '表单配置' }
  },
  {
    path: '/form-config/edit/:id?',
    name: 'formConfigEdit',
    component: () => import('@/views/basicConfig/formConfig/edit.vue'),
    meta: { title: '表单配置编辑' }
  },
  {
    path: '/basic-config/service-process/process-config/:id',
    name: 'ServiceProcessConfig',
    component: () => import('@/views/basicConfig/serviceProcess/processConfig.vue'),
    meta: { title: '流程配置画布' }
  },
  {
    path: '/industry-limit',
    name: 'industryLimit',
    component: () => import('@/views/basicConfig/industryLimit.vue'),
    meta: { title: '行业限制管理' }
  },
  {
    path: '/industry-limit/config/:id?',
    name: 'industryLimitConfig',
    component: () => import('@/views/basicConfig/industryLimitConfig.vue'),
    meta: { title: '行业限制规则配置' }
  },
  {
    path: '/industry-risk',
    name: 'industryRisk',
    component: () => import('@/views/riskMatrix/industryRisk.vue'),
    meta: { title: '行业风险管理' }
  },
  {
    path: '/industry-risk/config/:industryLevel1Code?',
    name: 'industryRiskConfig',
    component: () => import('@/views/riskMatrix/industryRiskConfig.vue'),
    meta: { title: '行业风险配置' }
  }
];

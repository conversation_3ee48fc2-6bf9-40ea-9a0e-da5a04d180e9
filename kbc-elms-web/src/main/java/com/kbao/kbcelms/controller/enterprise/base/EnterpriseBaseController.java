package com.kbao.kbcelms.controller.enterprise.base;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBeneficiary;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseFinancial;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseShareholder;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBeneficiaryService;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseFinancialService;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseShareholderService;
import com.kbao.kbcelms.enterprise.base.service.TianyanchaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业基本信息管理控制器
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/enterprise/base")
@Api(tags = "企业基本信息管理")
public class EnterpriseBaseController extends BaseController {
    
    @Autowired
    private TianyanchaService tianyanchaService;
    
    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;
    
    @Autowired
    private EnterpriseShareholderService enterpriseShareholderService;
    
    @Autowired
    private EnterpriseBeneficiaryService enterpriseBeneficiaryService;
    
    @Autowired
    private EnterpriseFinancialService enterpriseFinancialService;

    /**
     * 分页查询企业基本信息列表
     */
    @ApiOperation(value = "分页查询企业基本信息列表", notes = "支持按企业名称、信用代码、企业状态等条件查询")
    @PostMapping("/info/page")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "查询", desc = "分页查询企业基本信息列表")
    public Result<PageInfo<EnterpriseBasicInfo>> page(@RequestBody PageRequest<EnterpriseBasicInfo> page) {
        PageInfo<EnterpriseBasicInfo> pageInfo = enterpriseBasicInfoService.page(page);
        return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 同步企业所有信息
     */
    @ApiOperation(value = "同步企业所有信息", notes = "从天眼查同步企业基本信息、股东信息、受益人信息、财务信息")
    @PostMapping("/sync/all")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "同步", desc = "同步企业所有信息")
    public Result<EnterpriseBasicInfo> syncEnterpriseAllInfo(
            @ApiParam(value = "企业名称", required = true) @RequestParam String companyName) {
        EnterpriseBasicInfo basicInfo = tianyanchaService.syncEnterpriseAllInfo(companyName);
        return Result.succeed(basicInfo, ResultStatusEnum.SUCCESS.getMsg());
    }


    /**
     * 根据企业信用代码查询企业完整信息
     */
    @ApiOperation(value = "根据企业信用代码查询企业完整信息", notes = "根据企业统一社会信用代码查询企业所有信息")
    @GetMapping("/complete/{creditCode}")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "查询", desc = "根据企业信用代码查询企业完整信息")
    public Result<Map<String, Object>> getEnterpriseCompleteInfoById(
            @ApiParam(value = "统一社会信用代码", required = true) @PathVariable String creditCode) {
        Map<String, Object> result = new HashMap<>();

        // 根据信用代码查询基本信息
        EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.findByCreditCode(creditCode);
        if (basicInfo == null) {
            return Result.failed("企业信息不存在");
        }
        result.put("basicInfo", basicInfo);

        // 股东信息
        List<EnterpriseShareholder> shareholderList = enterpriseShareholderService.findByCreditCode(creditCode);
        result.put("shareholderList", shareholderList);

        // 受益人信息
        List<EnterpriseBeneficiary> beneficiaryList = enterpriseBeneficiaryService.findByCreditCode(creditCode);
        result.put("beneficiaryList", beneficiaryList);

        // 财务信息
        List<EnterpriseFinancial> financialList = enterpriseFinancialService.findByCreditCode(creditCode);
        result.put("financialList", financialList);

        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "生成KYC PDF报告", notes = "根据企业统一社会信用代码生成KYC PDF报告并保存到数据库")
    @PostMapping("/generateKycReport")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "生成", desc = "生成KYC PDF报告")
    public Result<String> generateKycReport(@ApiParam(value = "统一社会信用代码", required = true) @RequestParam String creditCode) {
        try {
            // 1. 查询企业基本信息
            EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.findByCreditCode(creditCode);
            if (basicInfo == null) {
                return Result.failed("未找到该企业的基本信息");
            }

            // 2. 查询股东信息
            List<EnterpriseShareholder> shareholderList = enterpriseShareholderService.findByCreditCode(creditCode);

            // 3. 查询最终受益人信息
            List<EnterpriseBeneficiary> beneficiaryList = enterpriseBeneficiaryService.findByCreditCode(creditCode);

            // 4. 生成KYC PDF报告
            String reportUrl = enterpriseBasicInfoService.generateKycPdfReport(basicInfo, shareholderList, beneficiaryList);

            return Result.succeed(reportUrl, "KYC报告生成成功");
        } catch (Exception e) {
            log.error("生成KYC PDF报告失败，企业信用代码：{}", creditCode, e);
            return Result.failed("KYC报告生成失败：" + e.getMessage());
        }
    }
}

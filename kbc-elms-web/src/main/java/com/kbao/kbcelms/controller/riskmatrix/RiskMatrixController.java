package com.kbao.kbcelms.controller.riskmatrix;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixQuery;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixReportRequest;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixRequest;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixResultDTO;
import com.kbao.kbcelms.riskmatrix.service.RiskMatrixLevelService;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixResultVO;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixVO;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixCategoryVO;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixLevelVO;
import com.kbao.kbcelms.riskmatrix.service.RiskMatrixService;
import com.kbao.kbcelms.riskmatrix.service.RiskMatrixCategoryService;
import com.kbao.kbcelms.riskmatrix.service.RiskMatrixReportService;
import com.kbao.kbcelms.riskmatrix.model.RiskMatrixReport;
import com.kbao.tool.util.SysLoginUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 风险矩阵管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/api/elms/riskMatrix")
public class RiskMatrixController extends BaseController {
    
    @Autowired
    private RiskMatrixService riskMatrixService;

    @Autowired
    private RiskMatrixCategoryService riskMatrixCategoryService;

    @Autowired
    private RiskMatrixLevelService riskMatrixLevelService;

    @Autowired
    private RiskMatrixReportService riskMatrixReportService;
    
    /**
     * 分页查询风险矩阵列表
     */
    @PostMapping("/page")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "分页查询风险矩阵列表")
    public Result<PageInfo<RiskMatrixVO>> getPage(@RequestBody PageRequest<RiskMatrixQuery> request) {
        try {
            PageInfo<RiskMatrixVO> result = riskMatrixService.getPage(request);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("分页查询风险矩阵列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询风险矩阵详情
     */
    @GetMapping("/{id}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "查询风险矩阵详情")
    public Result<RiskMatrixVO> getById(@PathVariable Long id) {
        try {
            RiskMatrixVO result = riskMatrixService.getById(id);
            if (result == null) {
                return Result.failed("风险矩阵不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("查询风险矩阵详情失败，ID：{}", id, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 保存风险矩阵（新增或更新）
     */
    @PostMapping("/save")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "保存", desc = "保存风险矩阵")
    public Result<Void> save(@RequestBody @Validated RiskMatrixRequest request) {
        try {
            // 检查编码是否重复
            if (riskMatrixService.existsByCode(request.getName(), request.getId())) {
                return Result.failed("矩阵名称已存在，请修改后重试");
            }
            
            String currentUser = getCurrentUser();
            riskMatrixService.save(request, currentUser);
            
            String action = request.getId() != null ? "更新" : "新增";
            return Result.succeed(action + "成功");
        } catch (Exception e) {
            log.error("保存风险矩阵失败", e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID删除风险矩阵
     */
    @DeleteMapping("/{id}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "删除", desc = "删除风险矩阵")
    public Result<Void> deleteById(@PathVariable Long id) {
        try {
            boolean success = riskMatrixService.deleteById(id);
            if (success) {
                return Result.succeed(null, "删除成功");
            } else {
                return Result.failed("删除失败，风险矩阵不存在");
            }
        } catch (Exception e) {
            log.error("删除风险矩阵失败，ID：{}", id, e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
    

    

    


    /**
     * 保存风险矩阵类别配置
     */
    @PostMapping("/{matrixId}/categories")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "保存", desc = "保存风险矩阵类别配置")
    public Result<Void> saveCategories(@PathVariable Long matrixId,
                                     @RequestBody @Validated List<RiskMatrixRequest.CategoryRequest> categories) {
        try {
            String currentUser = getCurrentUser();
            boolean success = riskMatrixCategoryService.saveCategoriesForMatrix(matrixId, categories, currentUser);

            if (success) {
                return Result.succeed(null, "类别配置保存成功");
            } else {
                return Result.failed("类别配置保存失败");
            }
        } catch (Exception e) {
            log.error("保存风险矩阵类别配置失败，矩阵ID：{}", matrixId, e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }

    /**
     * 获取风险矩阵类别列表（仅基本信息）
     */
    @GetMapping("/{matrixId}/categories/list")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "获取类别列表")
    public Result<List<RiskMatrixCategoryVO>> getCategoryList(@PathVariable Long matrixId) {
        try {
            List<RiskMatrixCategoryVO> result = riskMatrixCategoryService.getCategoryListByMatrixId(matrixId);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取类别列表失败，矩阵ID：{}", matrixId, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取单个类别详情（包含档次配置和关联评分项）
     */
    @GetMapping("/{matrixId}/categories/{categoryId}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "获取类别详情")
    public Result<RiskMatrixCategoryVO> getCategoryDetail(@PathVariable Long matrixId, @PathVariable Long categoryId) {
        try {
            RiskMatrixCategoryVO result = riskMatrixCategoryService.getCategoryDetailById(categoryId);
            if (result == null) {
                return Result.failed("类别不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取类别详情失败，矩阵ID：{}，类别ID：{}", matrixId, categoryId, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 保存单个类别（新增或更新）
     */
    @PostMapping("/{matrixId}/categories/save")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "保存", desc = "保存单个类别")
    public Result<Void> saveSingleCategory(@PathVariable Long matrixId,
                                         @RequestBody @Validated RiskMatrixRequest.CategoryRequest categoryRequest) {
        try {
            String currentUser = getCurrentUser();
            boolean success = riskMatrixCategoryService.saveSingleCategory(matrixId, categoryRequest, currentUser);

            if (success) {
                String action = categoryRequest.getId() != null ? "更新" : "新增";
                return Result.succeed(null, "类别" + action + "成功");
            } else {
                return Result.failed("类别保存失败");
            }
        } catch (Exception e) {
            log.error("保存单个类别失败，矩阵ID：{}", matrixId, e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }

    /**
     * 删除单个类别
     */
    @DeleteMapping("/{matrixId}/categories/{categoryId}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "删除", desc = "删除单个类别")
    public Result<Void> deleteSingleCategory(@PathVariable Long matrixId, @PathVariable Long categoryId) {
        try {
            boolean success = riskMatrixCategoryService.deleteById(categoryId);
            if (success) {
                return Result.succeed(null, "类别删除成功");
            } else {
                return Result.failed("类别删除失败");
            }
        } catch (Exception e) {
            log.error("删除单个类别失败，矩阵ID：{}，类别ID：{}", matrixId, categoryId, e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据类别ID查询档次列表
     */
    @GetMapping("/{matrixId}/categories/{categoryId}/levels")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "根据类别ID查询档次列表")
    public Result<List<RiskMatrixLevelVO>> getLevelsByCategoryId(@PathVariable Long matrixId,
                                                               @PathVariable Long categoryId) {
        try {
            List<RiskMatrixLevelVO> levelVOs = riskMatrixLevelService.getLevelVOsByCategoryId(categoryId);
            return Result.succeed(levelVOs, "查询成功");
        } catch (Exception e) {
            log.error("根据类别ID查询档次列表失败，矩阵ID：{}，类别ID：{}", matrixId, categoryId, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 保存类别的档次配置
     */
    @PostMapping("/{matrixId}/categories/{categoryId}/levels")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "保存", desc = "保存类别档次配置")
    public Result<Void> saveLevelsForCategory(@PathVariable Long matrixId,
                                            @PathVariable Long categoryId,
                                            @RequestBody @Validated List<RiskMatrixRequest.LevelRequest> levels) {
        try {
            String currentUser = getCurrentUser();
            boolean success = riskMatrixCategoryService.saveLevelsForCategory(categoryId, levels, currentUser);

            if (success) {
                return Result.succeed(null, "档次配置保存成功");
            } else {
                return Result.failed("档次配置保存失败");
            }
        } catch (Exception e) {
            log.error("保存类别档次配置失败，矩阵ID：{}，类别ID：{}", matrixId, categoryId, e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }

    // ==================== 报告管理API ====================

    /**
     * 根据企业ID获取最新的风险矩阵报告
     */
    @GetMapping("/report/latest/{enterpriseId}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "获取最新风险矩阵报告")
    public Result<RiskMatrixReport> getLatestReport(@PathVariable Long enterpriseId) {
        try {
            RiskMatrixReport result = riskMatrixReportService.getLatestReportByEnterpriseId(enterpriseId);
            if (result == null) {
                return Result.failed("未找到该企业的风险矩阵报告");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取最新风险矩阵报告失败，企业ID：{}", enterpriseId, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据报告ID获取风险矩阵报告详情
     */
    @GetMapping("/report/{reportId}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "获取风险矩阵报告详情")
    public Result<RiskMatrixReport> getReportById(@PathVariable String reportId) {
        try {
            RiskMatrixReport result = riskMatrixReportService.findById(reportId);
            if (result == null) {
                return Result.failed("报告不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取风险矩阵报告详情失败，报告ID：{}", reportId, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据企业ID获取风险矩阵报告列表
     */
    @GetMapping("/report/list/{enterpriseId}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "获取企业风险矩阵报告列表")
    public Result<List<RiskMatrixReport>> getReportList(@PathVariable Long enterpriseId) {
        try {
            List<RiskMatrixReport> result = riskMatrixReportService.getReportsByEnterpriseId(enterpriseId);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取企业风险矩阵报告列表失败，企业ID：{}", enterpriseId, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "根据企业类型查询问卷", notes = "根据当前登录用户的企业类型查询对应的问卷详情")
    @PostMapping("/loadRisMatrixReport")
    @LogAnnotation(module = "web端问卷管理", recordRequestParam = true, action = "查询", desc = "根据企业类型查询问卷")
    public Result<RiskMatrixResultVO> loadRisMatrixReport(@RequestBody @Validated RiskMatrixReportRequest request) {
        RiskMatrixResultVO resultVO = riskMatrixService.loadRisMatrixReport(request.getEnterpriseId(), request.getEnterpriseType(), request.getQuestionnaireId());
        return Result.succeed(resultVO, "查询成功");
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        return SysLoginUtils.getUserId();
    }
}

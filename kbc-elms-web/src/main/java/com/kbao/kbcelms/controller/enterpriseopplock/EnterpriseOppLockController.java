package com.kbao.kbcelms.controller.enterpriseopplock;


import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockEditDTO;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock;
import com.kbao.kbcelms.enterpriseopplock.service.EnterpriseOppLockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 企业机会锁定Controller
 * <AUTHOR>
 * @date 2025-01-15
 */
@Api(tags = "企业机会锁定管理")
@RestController
@RequestMapping("/enterpriseOppLock")
public class EnterpriseOppLockController {
    
    private static final Logger logger = LoggerFactory.getLogger(EnterpriseOppLockController.class);
    
    @Autowired
    private EnterpriseOppLockService enterpriseOppLockService;

    /**
     * 根据ID查询企业机会锁定详情
     */
    @ApiOperation(value = "查询企业机会锁定详情", notes = "根据ID查询单条记录详情")
    @PostMapping("/getByOpportunityId")
    public Result<EnterpriseOppLock> getByOpportunityId(@ApiParam(value = "企业机会锁定信息", required = true) @RequestBody EnterpriseOppLock lockDTO) {
        try {
            EnterpriseOppLock lockRecord = enterpriseOppLockService.getByOpportunityId(lockDTO.getOpportunityId());
            if (lockRecord == null) {
                return Result.failed("该机会未锁定，无锁定记录");
            }
            return Result.succeed(lockRecord, "查询成功");
        } catch (BusinessException e) {
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            logger.error("查询企业机会锁定详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 锁定机会
     */
    @ApiOperation(value = "锁定机会", notes = "根据机会ID进行机会锁定操作，包括检查锁定状态、创建锁定记录、更新机会状态等")
    @PostMapping("/lockOpportunity")
    public Result<String> lockOpportunity(@Valid @RequestBody EnterpriseOppLock lockDTO) {
        return enterpriseOppLockService.lockOpportunity(lockDTO);
    }
    
    /**
     * 解锁机会
     */
    @ApiOperation(value = "解锁机会", notes = "根据机会ID进行机会解锁操作，删除锁定记录并更新机会状态")
    @PostMapping("/unlockOpportunity/{opportunityId}")
    public Result<String> unlockOpportunity(@ApiParam(value = "机会ID", required = true) @PathVariable Integer opportunityId) {
        return enterpriseOppLockService.unlockOpportunity(opportunityId);
    }
    
    /**
     * 编辑锁定信息
     */
    @ApiOperation(value = "编辑锁定信息", notes = "编辑已存在的锁定记录，支持修改锁定截止时间、文件清单和备注信息")
    @PostMapping("/editLockInfo")
    public Result<String> editLockInfo(@Valid @RequestBody EnterpriseOppLockEditDTO editDTO) {
        return enterpriseOppLockService.editLockInfo(editDTO);
    }
    

}

package com.kbao.kbcelms.controller.opportunity;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.constant.annotation.ElmsAuthAnnotation;
import com.kbao.kbcelms.opportunitysummary.model.OpportunitySummary;
import com.kbao.kbcelms.opportunitysummary.service.OpportunitySummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机会总结控制器类
 * <AUTHOR>
 */
@Slf4j
@Api(tags = {"机会总结管理"})
@RestController
@RequestMapping("/api/opportunity/summary")
public class OpportunitySummaryController {

    @Autowired
    OpportunitySummaryService opportunitySummaryService;

    /**
     * 分页查询机会总结
     * @param requestPage 分页请求参数，包含查询条件和分页信息
     * @return 包含机会总结列表和分页信息的 Pagination 对象
     */
    @ApiOperation("分页查询机会总结")
    @PostMapping("/page")
    public Result<PageInfo<OpportunitySummary>> pageOpportunitySummary(@RequestBody PageRequest<OpportunitySummary> requestPage) {
        PageInfo<OpportunitySummary> pageInfo = opportunitySummaryService.pageOpportunitySummary(requestPage);
        return Result.succeed(pageInfo, "查询成功");
    }

    /**
     * 根据主键查询机会总结
     * @return 机会总结对象
     */
    @ApiOperation("根据主键查询机会总结")
    @PostMapping("/get")
    public Result<OpportunitySummary> getOpportunitySummary(@RequestBody OpportunitySummary opportunitySummary) {
        opportunitySummary = opportunitySummaryService.getOpportunitySummary(opportunitySummary.getId());
        return Result.succeed(opportunitySummary, "查询成功");
    }

    /**
     * 新增机会总结
     * @param opportunitySummary 机会总结对象
     * @return 新增后的机会总结对象
     */
    @ApiOperation("新增机会总结")
    @PostMapping("/add")
    @ElmsAuthAnnotation(auths = { "elms:opportunity:summary:upload" })
    public Result<OpportunitySummary> addOpportunitySummary(@RequestBody OpportunitySummary opportunitySummary) {
        OpportunitySummary newOpportunitySummary = opportunitySummaryService.addOpportunitySummary(opportunitySummary);
        return Result.succeed(newOpportunitySummary, "新增成功");
    }

    /**
     * 修改机会总结
     * @param opportunitySummary 机会总结对象
     * @return 更新后的机会总结对象
     */
    @ApiOperation("修改机会总结")
    @PostMapping("/update")
    public Result<OpportunitySummary> updateOpportunitySummary(@RequestBody OpportunitySummary opportunitySummary) {
        OpportunitySummary updatedOpportunitySummary = opportunitySummaryService.updateOpportunitySummary(opportunitySummary);
        return Result.succeed(updatedOpportunitySummary, "更新成功");
    }

    /**
     * 删除机会总结
     * @return 删除成功返回 true，若记录不存在则返回 false
     */
    @ApiOperation("删除机会总结")
    @PostMapping("/delete")
    @ElmsAuthAnnotation(auths = { "elms:opportunity:summary:delete" })
    public Result<Boolean> deleteOpportunitySummary(@RequestBody OpportunitySummary opportunitySummary) {
        opportunitySummaryService.deleteOpportunitySummary(opportunitySummary.getId());
        return Result.succeed(true, "删除成功");
    }
}
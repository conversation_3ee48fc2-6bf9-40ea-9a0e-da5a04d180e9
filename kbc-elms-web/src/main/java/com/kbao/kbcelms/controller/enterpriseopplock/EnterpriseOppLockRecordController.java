package com.kbao.kbcelms.controller.enterpriseopplock;

import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLockRecord;
import com.kbao.kbcelms.enterpriseopplock.service.EnterpriseOppLockRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 企业机会锁定记录Controller
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/enterprise/opp/lock/record")
@Api(tags = "企业机会锁定记录管理")
public class EnterpriseOppLockRecordController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(EnterpriseOppLockRecordController.class);

    @Autowired
    private EnterpriseOppLockRecordService enterpriseOppLockRecordService;

    /**
     * 根据机会ID查询企业机会锁定记录列表
     */
    @ApiOperation(value = "根据机会ID查询企业机会锁定记录列表", notes = "根据机会ID查询对应的锁定记录列表")
    @PostMapping("/listByOpportunityId")
    @LogAnnotation(module = "企业机会锁定记录", recordRequestParam = true, action = "查询", desc = "根据机会ID查询企业机会锁定记录列表")
    public Result<List<EnterpriseOppLockRecord>> getListByOpportunityId(@ApiParam(value = "机会ID", required = true) @RequestParam Integer opportunityId) {
        try {
            List<EnterpriseOppLockRecord> records = enterpriseOppLockRecordService.getListByOpportunityId(opportunityId);
            if (records.isEmpty()) {
                return Result.succeed(records, "该机会未锁定，无锁定记录");
            }
            return Result.succeed(records, "查询成功");
        } catch (BusinessException e) {
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            logger.error("根据机会ID查询企业机会锁定记录列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
}

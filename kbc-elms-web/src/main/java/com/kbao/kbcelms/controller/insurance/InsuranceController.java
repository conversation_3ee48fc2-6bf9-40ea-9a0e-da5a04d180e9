package com.kbao.kbcelms.controller.insurance;

import java.util.ArrayList;
import java.util.List;

import com.kbao.kbcelms.insurance.vo.InsuranceResponseVO;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.insurance.model.Insurance;
import com.kbao.kbcelms.insurance.service.InsuranceService;
import com.kbao.kbcelms.insurance.vo.InsuranceResquestVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/8/28 15:42
 */
@Slf4j
@Api(tags = "险种管理")
@RestController
@RequestMapping("/api/insurance")
public class InsuranceController extends BaseController {

    @Autowired
    private InsuranceService insuranceService;

    @LogAnnotation(module = "险种管理", recordRequestParam = true, action = "搜索", desc = "搜索险种列表")
    @ApiOperation(value = "搜索险种列表", notes = "搜索险种列表")
    @PostMapping("/list")
    public Result<List<InsuranceResponseVO>> list() {
        List<InsuranceResponseVO> list = insuranceService.list();
        if(EmptyUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return Result.succeed(list, "查询成功");
    }

    @LogAnnotation(module = "险种管理", recordRequestParam = true, action = "新增", desc = "险种新增")
    @ApiOperation(value = "险种新增", notes = "险种新增")
    @PostMapping("/save")
    public Result<String> save(@RequestBody Insurance insurance) {
        insuranceService.saveInsurance(insurance);
        return Result.succeed("新增成功");
    }

    @LogAnnotation(module = "险种管理", recordRequestParam = true, action = "删除", desc = "险种删除")
    @ApiOperation(value = "险种删除", notes = "险种删除")
    @PostMapping("/delete")
    public Result<String> delete(@RequestBody Insurance insurance) {
        insuranceService.deleteInsurance(insurance.getInsuranceId());
        return Result.succeed("新增成功");
    }

    @LogAnnotation(module = "险种管理", recordRequestParam = true, action = "修改", desc = "险种排序修改")
    @ApiOperation(value = "险种排序修改", notes = "险种排序修改")
    @PostMapping("/updateSort")
    public Result<String> updateSort(@RequestBody InsuranceResquestVO resquestVO) {
        insuranceService.updateSort(resquestVO);
        return Result.succeed("修改成功");
    }

}

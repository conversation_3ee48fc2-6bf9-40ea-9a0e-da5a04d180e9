-- 行业限制管理模块数据库表结构
-- 创建时间：2025-01-15
-- 描述：行业限制规则管理相关表

-- 1. 行业限制规则主表
DROP TABLE IF EXISTS `t_industry_limit`;
CREATE TABLE `t_industry_limit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '规则名称',
  `code` varchar(50) NOT NULL COMMENT '规则编码',
  `description` varchar(500) DEFAULT NULL COMMENT '规则描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(50) DEFAULT NULL COMMENT '租户ID',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除,1-已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`,`is_deleted`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='行业限制规则表';

-- 2. 行业限制条件表
DROP TABLE IF EXISTS `t_industry_limit_condition`;
CREATE TABLE `t_industry_limit_condition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
  `field` varchar(50) NOT NULL COMMENT '字段名称',
  `operator` varchar(20) NOT NULL COMMENT '操作符(eq-等于,ne-不等于,gt-大于,lt-小于,gte-大于等于,lte-小于等于,contains-包含,range-区间)',
  `value` varchar(500) NOT NULL COMMENT '匹配值',
  `description` varchar(200) DEFAULT NULL COMMENT '条件描述',
  `sort_order` int(11) NOT NULL DEFAULT '1' COMMENT '排序号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tenant_id` varchar(50) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_field` (`field`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_tenant_id` (`tenant_id`),
  CONSTRAINT `fk_industry_limit_condition_rule_id` FOREIGN KEY (`rule_id`) REFERENCES `t_industry_limit` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='行业限制条件表';

-- 3. 行业限制执行动作表
DROP TABLE IF EXISTS `t_industry_limit_action`;
CREATE TABLE `t_industry_limit_action` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
  `is_visible` varchar(20) NOT NULL COMMENT '是否可见(Y-可见,N-不可见)',
  `is_locked` varchar(20) DEFAULT 'Y' COMMENT '是否锁定(Y-锁定,N-不锁定)',
  `service_ids` varchar(1000) DEFAULT NULL COMMENT '服务流程ID列表(JSON格式)',
  `description` varchar(200) DEFAULT NULL COMMENT '动作描述',
  `sort_order` int(11) NOT NULL DEFAULT '1' COMMENT '排序号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tenant_id` varchar(50) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_type` (`type`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_tenant_id` (`tenant_id`),
  CONSTRAINT `fk_industry_limit_action_rule_id` FOREIGN KEY (`rule_id`) REFERENCES `t_industry_limit` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='行业限制执行动作表';

-- 插入测试数据
INSERT INTO `t_industry_limit` (
  `name`, `code`, `description`, `status`, `create_user`, `update_user`, `tenant_id`
) VALUES 
(
  '企业类型限额规则', 
  'ENTERPRISE_TYPE_LIMIT', 
  '根据企业类型设置不同的限额规则，动态添加企业字段，设置匹配规则，指定可用服务流程', 
  1, 
  'admin', 
  'admin', 
  'default'
),
(
  '行业风险限制规则', 
  'INDUSTRY_RISK_LIMIT', 
  '根据行业风险等级设置不同的限制规则', 
  1, 
  'admin', 
  'admin', 
  'default'
);

-- 插入条件测试数据
INSERT INTO `t_industry_limit_condition` (
  `rule_id`, `field`, `operator`, `value`, `description`, `sort_order`, `tenant_id`
) VALUES 
-- 第一个规则的条件
(1, 'enterpriseType', 'eq', 'A', '企业类型等于A类', 1, 'default'),
(1, 'employeeCount', 'range', '5000-10000', '员工数量在5000-10000之间', 2, 'default'),
-- 第二个规则的条件
(2, 'industryCode', 'eq', 'FINANCE', '行业代码等于金融', 1, 'default');

-- 插入执行动作测试数据
INSERT INTO `t_industry_limit_action` (
  `rule_id`, `type`, `is_locked`, `service_ids`, `description`, `sort_order`, `tenant_id`
) VALUES 
-- 第一个规则的动作
(1, 'Y', 'Y', '["1", "2"]', '服务1和2可见', 1, 'default'),
(1, 'N', 'N', '["3"]', '服务3不可见', 2, 'default'),
-- 第二个规则的动作
(2, 'Y', 'Y', '["2", "3"]', '服务2和3可见', 1, 'default');

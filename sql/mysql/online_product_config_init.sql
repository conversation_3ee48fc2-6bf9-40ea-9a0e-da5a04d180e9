-- 线上产品配置模块数据库初始化脚本（优化版本）
-- 创建时间：2025-01-21
-- 更新说明：修复字段长度限制，优化存储格式

-- 删除已存在的表
DROP TABLE IF EXISTS `t_online_product_config`;

-- 创建线上产品配置表（优化版本）
CREATE TABLE `t_online_product_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `industry_code` TEXT NOT NULL COMMENT '行业代码(支持多个,紧凑格式存储：用逗号分隔)',
  `industry_name` varchar(200) NOT NULL COMMENT '行业名称(智能合并显示)',
  `probability` varchar(20) NOT NULL COMMENT '风险发生概率(高/中/低)',
  `impact` varchar(20) NOT NULL COMMENT '风险影响程度(高/中/低)',
  `level` varchar(20) NOT NULL COMMENT '风险等级(高/中/低)',
  `insurance_types` TEXT DEFAULT NULL COMMENT '险种类型列表(JSON格式)',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(1-启用,0-禁用)',
  `description` varchar(500) DEFAULT NULL COMMENT '描述信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(50) DEFAULT NULL COMMENT '租户ID',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除,1-已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_industry_code` (`industry_code`(100)) COMMENT '行业代码索引',
  KEY `idx_probability` (`probability`),
  KEY `idx_impact` (`impact`),
  KEY `idx_level` (`level`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线上产品配置表（优化版本）';

-- 插入测试数据（使用新的紧凑格式）
INSERT INTO `t_online_product_config` (
  `industry_code`, 
  `industry_name`, 
  `probability`, 
  `impact`, 
  `level`, 
  `insurance_types`, 
  `enabled`, 
  `description`, 
  `create_user`, 
  `update_user`, 
  `tenant_id`
) VALUES 
-- 1. 农业相关配置
(
  '01', 
  '农业', 
  '中', 
  '中', 
  '中', 
  '["employer","group"]', 
  1, 
  '农业整体风险配置，适用于所有农业相关业务', 
  'system', 
  'system', 
  'default'
),
-- 2. 谷物种植细分配置
(
  '0111,0112,0113', 
  '稻谷种植，小麦种植，玉米种植', 
  '低', 
  '中', 
  '低', 
  '["employer","group","overseas"]', 
  1, 
  '主要谷物种植风险配置，风险相对较低', 
  'system', 
  'system', 
  'default'
),
-- 3. 制造业高风险配置
(
  '13', 
  '农副食品加工业', 
  '高', 
  '高', 
  '高', 
  '["employer","group","health","property"]', 
  1, 
  '制造业存在机械操作风险，需要全面保障', 
  'system', 
  'system', 
  'default'
),
-- 4. 建筑业配置
(
  '47,48', 
  '房屋建筑业，土木工程建筑业', 
  '高', 
  '高', 
  '高', 
  '["employer","group","health","property"]', 
  1, 
  '建筑业高风险行业，需要重点关注安全保障', 
  'system', 
  'system', 
  'default'
),
-- 5. 信息技术服务业
(
  '65', 
  '软件和信息技术服务业', 
  '低', 
  '低', 
  '低', 
  '["group","health","pension"]', 
  1, 
  'IT行业风险较低，主要关注职业健康', 
  'system', 
  'system', 
  'default'
),
-- 6. 金融业配置
(
  '66,67,68', 
  '货币金融服务，资本市场服务，保险业', 
  '中', 
  '中', 
  '中', 
  '["group","health","pension"]', 
  1, 
  '金融服务业风险适中，注重长期保障', 
  'system', 
  'system', 
  'default'
),
-- 7. 交通运输业
(
  '53,54', 
  '道路运输业，水上运输业', 
  '高', 
  '高', 
  '高', 
  '["employer","group","overseas","property"]', 
  1, 
  '交通运输业存在较高安全风险', 
  'system', 
  'system', 
  'default'
),
-- 8. 教育行业
(
  '83', 
  '教育', 
  '低', 
  '中', 
  '低', 
  '["group","health","pension"]', 
  1, 
  '教育行业风险较低，重点关注长期保障', 
  'system', 
  'system', 
  'default'
),
-- 9. 医疗卫生
(
  '84', 
  '卫生和社会工作', 
  '中', 
  '高', 
  '中', 
  '["employer","group","health"]', 
  1, 
  '医疗行业需要专业风险保障', 
  'system', 
  'system', 
  'default'
),
-- 10. 批发零售业
(
  '51,52', 
  '批发业，零售业', 
  '中', 
  '中', 
  '中', 
  '["employer","group","property"]', 
  1, 
  '商贸流通行业风险适中', 
  'system', 
  'system', 
  'default'
),
-- 11. 混合行业示例（多个不同层级）
(
  '01,021,0311,47', 
  '农业，林业，畜牧业，房屋建筑业', 
  '高', 
  '高', 
  '高', 
  '["employer","group","health","property"]', 
  1, 
  '跨行业经营企业的综合风险配置', 
  'system', 
  'system', 
  'default'
),
-- 12. 大量细分行业示例（测试长字符串）
(
  '0111,0112,0113,0114,0115,0116,0119,0121,0122,0123,0131,0132,0133,0134,0135,0141,0142,0143,0149,0151,0152,0153,0154,0159,0161,0162,0163,0164,0169,0171,0172,0173,0174,0175,0179,0181,0182,0183,0184,0190', 
  '农作物种植业', 
  '中', 
  '中', 
  '中', 
  '["employer","group"]', 
  1, 
  '农作物种植业详细分类配置，覆盖所有主要作物类型', 
  'system', 
  'system', 
  'default'
);

-- 验证数据插入
SELECT 
  id,
  LEFT(industry_code, 50) as industry_code_preview,
  industry_name,
  probability,
  impact,
  level,
  enabled,
  description
FROM t_online_product_config 
ORDER BY id;

-- 统计信息
SELECT 
  COUNT(*) as total_records,
  AVG(LENGTH(industry_code)) as avg_industry_code_length,
  MAX(LENGTH(industry_code)) as max_industry_code_length,
  MIN(LENGTH(industry_code)) as min_industry_code_length
FROM t_online_product_config;

-- 显示表结构
DESCRIBE t_online_product_config;

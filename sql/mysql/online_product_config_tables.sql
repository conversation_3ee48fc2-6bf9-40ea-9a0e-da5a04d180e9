-- 线上产品配置模块数据库表结构
-- 创建时间：2025-01-15
-- 描述：线上产品配置管理相关表

-- 线上产品配置表（优化版本）
DROP TABLE IF EXISTS `t_online_product_config`;
CREATE TABLE `t_online_product_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `industry_code` TEXT NOT NULL COMMENT '行业代码(支持多个,紧凑格式存储：用逗号分隔)',
  `industry_name` varchar(200) NOT NULL COMMENT '行业名称(智能合并显示)',
  `probability` varchar(20) NOT NULL COMMENT '风险发生概率(高/中/低)',
  `impact` varchar(20) NOT NULL COMMENT '风险影响程度(高/中/低)',
  `level` varchar(20) NOT NULL COMMENT '风险等级(高/中/低)',
  `insurance_types` TEXT DEFAULT NULL COMMENT '险种类型列表(JSON格式)',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(1-启用,0-禁用)',
  `description` varchar(500) DEFAULT NULL COMMENT '描述信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(50) DEFAULT NULL COMMENT '租户ID',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除,1-已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_industry_code` (`industry_code`(100)) COMMENT '行业代码索引（前100字符）',
  KEY `idx_probability` (`probability`),
  KEY `idx_impact` (`impact`),
  KEY `idx_level` (`level`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线上产品配置表（优化版本）';

-- 插入测试数据（使用真实的国民经济行业分类代码）
INSERT INTO `t_online_product_config` (
  `industry_code`, `industry_name`, `probability`, `impact`, `level`, 
  `insurance_types`, `enabled`, `description`, `create_user`, `update_user`, `tenant_id`
) VALUES 
-- 信息传输、软件和信息技术服务业配置
(
  'I65', '软件和信息技术服务业', '中', '中', '中',
  '["employer", "group", "health"]',
  1,
  'IT行业中等风险配置，适用于软件开发、系统集成等企业',
  'admin', 'admin', 'default'
),
(
  'I65', '软件和信息技术服务业', '高', '高', '高',
  '["employer", "group", "health", "property"]',
  1,
  'IT行业高风险配置，适用于网络安全、数据中心等高风险业务',
  'admin', 'admin', 'default'
),

-- 金融业配置
(
  'J66', '货币金融服务', '高', '高', '高',
  '["employer", "group", "health", "property", "overseas"]',
  1,
  '金融行业高风险配置，适用于银行、证券、保险等金融机构',
  'admin', 'admin', 'default'
),
(
  'J68', '保险业', '中', '高', '高',
  '["employer", "group", "health", "property"]',
  1,
  '保险行业中等概率高影响配置，适用于保险公司、保险代理等',
  'admin', 'admin', 'default'
),

-- 制造业配置
(
  'C33', '金属制品业', '中', '中', '中',
  '["employer", "group", "property"]',
  1,
  '制造业中等风险配置，适用于一般制造加工企业',
  'admin', 'admin', 'default'
),
(
  'C26', '化学原料和化学制品制造业', '高', '高', '高',
  '["employer", "group", "property", "health"]',
  1,
  '制造业高风险配置，适用于化工、重工业等高危制造业',
  'admin', 'admin', 'default'
),

-- 批发和零售业配置
(
  'F51', '批发业', '低', '中', '中',
  '["employer", "group", "overseas"]',
  1,
  '批发贸易行业中等风险配置，适用于进出口贸易企业',
  'admin', 'admin', 'default'
),

-- 租赁和商务服务业配置
(
  'L72', '商务服务业', '中', '中', '中',
  '["employer", "group", "health"]',
  1,
  '商务服务行业中等风险配置，适用于咨询、广告等服务企业',
  'admin', 'admin', 'default'
),

-- 教育行业配置
(
  'P83', '职业技能培训', '低', '低', '低',
  '["employer", "group"]',
  1,
  '教育行业低风险配置，适用于培训机构、职业教育等',
  'admin', 'admin', 'default'
),

-- 卫生和社会工作配置
(
  'Q84', '卫生', '高', '高', '高',
  '["employer", "group", "health", "property"]',
  1,
  '医疗健康行业高风险配置，适用于医院、诊所等医疗机构',
  'admin', 'admin', 'default'
),

-- 多行业配置示例
(
  '["I65", "L72"]', '软件和商务服务业', '中', '中', '中',
  '["employer", "group", "health"]',
  1,
  '软件开发与商务服务混合业务配置',
  'admin', 'admin', 'default'
),
(
  '["C26", "C33"]', '化工和金属制品业', '高', '高', '高',
  '["employer", "group", "property", "health"]',
  1,
  '化工和金属制品混合业务高风险配置',
  'admin', 'admin', 'default'
);

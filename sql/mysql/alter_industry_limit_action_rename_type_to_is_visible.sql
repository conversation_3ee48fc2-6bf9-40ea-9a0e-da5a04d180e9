-- 数据库迁移脚本：将t_industry_limit_action表的type字段重命名为is_visible
-- 创建时间：2025-01-22
-- 描述：重命名IndustryLimitAction实体的type字段为is_visible

-- 重命名type字段为is_visible
ALTER TABLE `t_industry_limit_action` 
CHANGE COLUMN `type` `is_visible` varchar(20) NOT NULL COMMENT '是否可见(Y-可见,N-不可见)';

-- 验证字段重命名结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 't_industry_limit_action' 
    AND COLUMN_NAME IN ('type', 'is_visible');

-- 查看表结构确认修改
DESC t_industry_limit_action;
-- 为风险矩阵类别表添加description字段
-- 执行时间：2025-01-08
-- 作者：system

-- 检查表是否存在description列，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 't_risk_matrix_category' 
         AND COLUMN_NAME = 'description') = 0,
        'ALTER TABLE t_risk_matrix_category ADD COLUMN description VARCHAR(500) COMMENT ''类别描述'' AFTER name;',
        'SELECT ''Column description already exists'' AS message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 't_risk_matrix_category' 
  AND COLUMN_NAME = 'description';

-- 如果表不存在，创建完整的表结构
CREATE TABLE IF NOT EXISTS `t_risk_matrix_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `matrix_id` bigint(20) NOT NULL COMMENT '风险矩阵ID',
  `name` varchar(100) NOT NULL COMMENT '类别名称',
  `description` varchar(500) DEFAULT NULL COMMENT '类别描述',
  `weight` decimal(5,2) DEFAULT '1.00' COMMENT '权重',
  `calculation_method` varchar(20) DEFAULT 'sum' COMMENT '计算方法：sum-求和，avg-平均值，max-最大值',
  `sort_order` int(11) DEFAULT '1' COMMENT '排序序号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_matrix_id` (`matrix_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风险矩阵类别表';

-- 添加外键约束（如果风险矩阵主表存在）
-- ALTER TABLE t_risk_matrix_category 
-- ADD CONSTRAINT fk_category_matrix 
-- FOREIGN KEY (matrix_id) REFERENCES t_risk_matrix(id) 
-- ON DELETE CASCADE ON UPDATE CASCADE;

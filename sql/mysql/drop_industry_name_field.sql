-- 删除industryName字段的SQL脚本
-- 创建时间：2025-01-26
-- 描述：从相关表中删除industryName字段

-- 1. 删除t_online_product_config表中的industry_name字段
ALTER TABLE `t_online_product_config` 
DROP COLUMN `industry_name`;

-- 验证字段删除结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 't_online_product_config' 
    AND COLUMN_NAME = 'industry_name';

-- 查看表结构确认修改
DESC t_online_product_config;

-- 显示修改后的表结构信息
SHOW CREATE TABLE t_online_product_config;
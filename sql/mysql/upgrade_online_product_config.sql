-- 线上产品配置表升级脚本
-- 用途：将现有表结构升级为优化版本
-- 执行时间：2025-01-21

-- 第一步：备份现有数据
CREATE TABLE `t_online_product_config_backup` AS 
SELECT * FROM `t_online_product_config`;

-- 第二步：修改表结构
-- 扩展 industry_code 字段长度
ALTER TABLE `t_online_product_config` 
MODIFY COLUMN `industry_code` TEXT NOT NULL COMMENT '行业代码(支持多个,紧凑格式存储：用逗号分隔)';

-- 扩展 industry_name 字段长度（支持更长的智能合并名称）
ALTER TABLE `t_online_product_config` 
MODIFY COLUMN `industry_name` varchar(200) NOT NULL COMMENT '行业名称(智能合并显示)';

-- 确保 insurance_types 字段支持长内容
ALTER TABLE `t_online_product_config` 
MODIFY COLUMN `insurance_types` TEXT DEFAULT NULL COMMENT '险种类型列表(JSON格式)';

-- 第三步：优化索引
-- 删除可能存在的旧唯一约束（如果有冲突）
-- ALTER TABLE `t_online_product_config` DROP INDEX `uk_industry_risk` IF EXISTS;

-- 添加优化的索引
ALTER TABLE `t_online_product_config` 
ADD INDEX `idx_industry_code` (`industry_code`(100)) COMMENT '行业代码索引（前100字符）';

-- 第四步：数据格式迁移（可选）
-- 将现有JSON格式转换为紧凑格式
UPDATE `t_online_product_config` 
SET `industry_code` = REPLACE(REPLACE(REPLACE(`industry_code`, '["', ''), '"]', ''), '","', ',')
WHERE `industry_code` LIKE '[%' AND `industry_code` IS NOT NULL;

-- 第五步：验证升级结果
-- 检查数据完整性
SELECT 
  'Before Upgrade' as phase,
  COUNT(*) as record_count
FROM `t_online_product_config_backup`
UNION ALL
SELECT 
  'After Upgrade' as phase,
  COUNT(*) as record_count
FROM `t_online_product_config`;

-- 检查字段长度分布
SELECT 
  'industry_code字段长度统计' as info,
  AVG(LENGTH(industry_code)) as avg_length,
  MAX(LENGTH(industry_code)) as max_length,
  MIN(LENGTH(industry_code)) as min_length
FROM `t_online_product_config`;

-- 检查表结构
DESCRIBE `t_online_product_config`;

-- 第六步：清理（可选，确认无问题后执行）
-- DROP TABLE `t_online_product_config_backup`;

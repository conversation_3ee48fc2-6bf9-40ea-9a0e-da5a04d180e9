-- =====================================================
-- 公式引擎数据库脚本
-- =====================================================

-- 1. 公式表
CREATE TABLE t_formula (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '公式名称',
    description VARCHAR(500) COMMENT '公式描述',
    formula TEXT NOT NULL COMMENT '公式内容',
    category TINYINT NOT NULL COMMENT '分类：1-战略文化与治理框架，2-组织责任与指标体系，3-风险评估与流程执行，4-沟通系统与技术支持',
    enterprise_type VARCHAR(10) COMMENT '企业类型：A-大型企业，B-中型企业，C-小型企业',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    last_test_time DATETIME COMMENT '最后测试时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user VARCHAR(50) COMMENT '创建人',
    update_user VARCHAR(50) COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识：0-未删除，1-已删除',
    INDEX idx_category (category),
    INDEX idx_enterprise_type_id (enterprise_type_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT '公式表';

-- 2. 公式变量表
CREATE TABLE t_formula_variable (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    formula_id BIGINT NOT NULL COMMENT '公式ID',
    name VARCHAR(50) NOT NULL COMMENT '变量名称',
    type VARCHAR(20) NOT NULL COMMENT '变量类型：number-数值，variable-变量，constant-常数',
    default_value DECIMAL(10,4) COMMENT '默认值',
    min_value DECIMAL(10,4) COMMENT '最小值',
    max_value DECIMAL(10,4) COMMENT '最大值',
    description VARCHAR(200) COMMENT '变量描述',
    unit VARCHAR(20) COMMENT '单位',
    sort_order INT DEFAULT 0 COMMENT '排序',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识：0-未删除，1-已删除',
    INDEX idx_formula_id (formula_id),
    FOREIGN KEY (formula_id) REFERENCES t_formula(id) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT '公式变量表';

-- 3. 公式计算记录表
CREATE TABLE t_formula_calculation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    formula_id BIGINT NOT NULL COMMENT '公式ID',
    formula_name VARCHAR(100) COMMENT '公式名称',
    input_variables JSON COMMENT '输入变量',
    calculation_result DECIMAL(10,4) COMMENT '计算结果',
    calculation_time DATETIME COMMENT '计算时间',
    execution_time_ms INT COMMENT '执行时间（毫秒）',
    status TINYINT COMMENT '计算状态：1-成功，0-失败',
    error_message TEXT COMMENT '错误信息',
    user_id VARCHAR(50) COMMENT '用户ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_formula_id (formula_id),
    INDEX idx_calculation_time (calculation_time),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (formula_id) REFERENCES t_formula(id) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT '公式计算记录表';

-- =====================================================
-- 初始数据插入
-- =====================================================

-- 插入示例公式
INSERT INTO t_formula (name, description, formula, category, enterprise_type, status) VALUES
('管理层承诺与文化导向公式', '用于评估管理层承诺与文化导向的风险管理公式', 
 '(A * pow(x, 4) + B * sqrt(pow(x, 3) + 1) + C * sin(2 * PI * x / 5) + D * factorial(x) * log(x + 1) + E * pow(2, x)) / 3 * 100',
 1, 'A', 1),
('风险责任明确公式', '用于风险责任明确的风险管理公式',
 'a * X + b * sqrt(X) + c * log(X + 1)',
 2, 'A', 1),
('风险识别评估公式', '用于风险识别和评估的计算公式',
 'sqrt(pow(risk_probability, 2) + pow(risk_impact, 2)) * severity_factor',
 3, 'B', 1),
('技术风险监控公式', '用于技术系统风险监控的计算公式',
 'log(system_complexity + 1) * error_rate * availability_factor',
 4, 'C', 1),
('通用风险评估公式', '适用于所有企业类型的通用风险评估公式',
 'base_score * risk_factor + adjustment_value',
 1, NULL, 1);

-- 插入公式变量
INSERT INTO t_formula_variable (formula_id, name, type, default_value, description, sort_order) VALUES
-- 公式1的变量
(1, 'x', 'variable', 5.0, '问卷得分', 1),
-- 公式2的变量
(2, 'a', 'number', 0.5, '线性权重系数', 1),
(2, 'b', 'number', 0.3, '平方根权重系数', 2),
(2, 'c', 'number', 0.2, '对数权重系数', 3),
(2, 'X', 'variable', 5.0, '问卷得分', 4),
-- 公式3的变量
(3, 'risk_probability', 'variable', 0.5, '风险概率', 1),
(3, 'risk_impact', 'variable', 0.7, '风险影响', 2),
(3, 'severity_factor', 'number', 1.2, '严重性因子', 3),
-- 公式4的变量
(4, 'system_complexity', 'variable', 3.0, '系统复杂度', 1),
(4, 'error_rate', 'variable', 0.1, '错误率', 2),
(4, 'availability_factor', 'number', 0.99, '可用性因子', 3),
-- 公式5的变量
(5, 'base_score', 'variable', 10.0, '基础分数', 1),
(5, 'risk_factor', 'number', 1.5, '风险因子', 2),
(5, 'adjustment_value', 'number', 2.0, '调整值', 3);

-- 插入示例计算记录
INSERT INTO t_formula_calculation_log (formula_id, formula_name, input_variables, calculation_result, calculation_time, execution_time_ms, status, user_id) VALUES
(1, '管理层承诺与文化导向公式', 
 '{"x": 5.0, "A": 0.25, "B": 0.35, "C": 0.15, "D": 0.1, "E": 0.15}', 
 85.67, '2024-01-15 10:30:00', 25, 1, 'admin'),
(2, '风险责任明确公式', 
 '{"a": 0.5, "b": 0.3, "c": 0.2, "X": 5.0}', 
 12.45, '2024-01-15 11:15:00', 15, 1, 'admin'),
(3, '风险识别评估公式', 
 '{"risk_probability": 0.6, "risk_impact": 0.8, "severity_factor": 1.2}', 
 1.2, '2024-01-15 14:20:00', 18, 1, 'admin'),
(5, '通用风险评估公式', 
 '{"base_score": 8.0, "risk_factor": 1.3, "adjustment_value": 1.5}', 
 11.9, '2024-01-15 16:45:00', 12, 1, 'admin');

-- 表单配置表
CREATE TABLE `t_form_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `config_code` varchar(50) NOT NULL COMMENT '配置编码',
  `type` varchar(10) NOT NULL DEFAULT '1' COMMENT '分类信息：1-企业信息，2-员福配置，3-企业补充信息',
  `status` varchar(10) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `remark` varchar(500) DEFAULT NULL COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_id` varchar(50) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  `tenant_id` varchar(50) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_config_code` (`config_code`) COMMENT '配置编码索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单配置表';

-- MongoDB集合说明
-- 集合名：FormConfigField
-- 字段说明：
-- {
--   "_id": ObjectId,              // MongoDB主键
--   "configId": Integer,          // 配置ID
--   "fieldName": String,          // 字段名称
--   "fieldCode": String,          // 字段编码
--   "showType": String,           // 展示类型：1-输入框，2-单选框，3-下拉框，4-日期选择，5-日期范围，6-文本域
--   "required": String,           // 是否必填：0-否，1-是
--   "change": String,             // 是否可编辑：0-否，1-是
--   "defaultValue": String,       // 默认值
--   "validation": String,         // 校验规则
--   "additional": Object,         // 附加属性（JSON对象）
--   "remark": String,             // 备注
--   "sort": Integer,              // 排序
--   "createTime": Date            // 创建时间
-- }

-- 示例数据
INSERT INTO `t_form_config` (`config_name`, `config_code`, `type`, `status`, `remark`, `create_id`, `tenant_id`) VALUES
('团意险投保信息表单', 'group_accident_insurance', '2', '1', '团体意外险投保信息H5表单配置', 'system', 'default'),
('雇主险投保信息表单', 'employer_liability_insurance', '2', '1', '雇主责任险投保信息H5表单配置', 'system', 'default'),
('团体高医投保信息表单', 'group_high_medical_insurance', '2', '1', '团体高端医疗险投保信息H5表单配置', 'system', 'default'),
('企业基本信息表单', 'enterprise_basic', '1', '1', '企业基本信息H5表单配置', 'system', 'default'),
('客户信息表单', 'customer_info', '1', '1', '客户信息H5表单配置', 'system', 'default'),
('企业补充信息表单', 'enterprise_supplement_info', '3', '1', '企业补充信息H5表单配置', 'system', 'default');

-- MongoDB字段配置示例（需要根据实际生成的configId进行调整）
-- 团意险字段配置 (configId: 1)
/*
db.FormConfigField.insertMany([
  {
    "configId": 1,
    "fieldName": "职业类别",
    "fieldCode": "occupationType",
    "showType": "3",
    "required": "1",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "selectOptions": ["一类职业", "二类职业", "三类职业", "四类职业", "五类职业", "六类职业"]
    },
    "remark": "请选择职业类别",
    "sort": 1,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "人数",
    "fieldCode": "personCount",
    "showType": "1",
    "required": "1",
    "change": "1",
    "defaultValue": "",
    "validation": "^[1-9]\\d*$",
    "additional": {
      "inputUnit": "人"
    },
    "remark": "投保人数",
    "sort": 2,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "公众责任每付限额",
    "fieldCode": "publicLiabilityLimit",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "万元"
    },
    "remark": "公众责任每付限额",
    "sort": 3,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "每次事故赔付限额",
    "fieldCode": "accidentCompensationLimit",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "元"
    },
    "remark": "每次事故赔付限额",
    "sort": 4,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "每人伤赔付限额",
    "fieldCode": "personalInjuryLimit",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "元"
    },
    "remark": "每人伤赔付限额",
    "sort": 5,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "投保面积",
    "fieldCode": "insuredArea",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "平米"
    },
    "remark": "投保面积",
    "sort": 6,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "火灾/爆炸损失保额",
    "fieldCode": "fireExplosionCoverage",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "万元"
    },
    "remark": "火灾/爆炸损失保额",
    "sort": 7,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "物品损失保额",
    "fieldCode": "propertyLossCoverage",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "万元"
    },
    "remark": "物品损失保额",
    "sort": 8,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "意外医疗保额",
    "fieldCode": "accidentMedicalCoverage",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "万元"
    },
    "remark": "意外医疗保额",
    "sort": 9,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "意外身故保额",
    "fieldCode": "accidentDeathCoverage",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "万元"
    },
    "remark": "意外身故保额",
    "sort": 10,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "意外伤残保额",
    "fieldCode": "accidentDisabilityCoverage",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "万元"
    },
    "remark": "意外伤残保额",
    "sort": 11,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "交通意外身故保额",
    "fieldCode": "trafficAccidentDeathCoverage",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "万元"
    },
    "remark": "交通意外身故保额",
    "sort": 12,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "每日住院津贴",
    "fieldCode": "dailyHospitalAllowance",
    "showType": "1",
    "required": "0",
    "change": "1",
    "defaultValue": "",
    "validation": "",
    "additional": {
      "inputUnit": "元/天"
    },
    "remark": "每日住院津贴",
    "sort": 13,
    "createTime": new Date()
  },
  {
    "configId": 1,
    "fieldName": "救护车费用",
    "fieldCode": "ambulanceFee",
    "showType": "2",
    "required": "0",
    "change": "1",
    "defaultValue": "不附加",
    "validation": "",
    "additional": {
      "selectOptions": ["附加", "不附加"]
    },
    "remark": "是否附加救护车费用",
    "sort": 14,
    "createTime": new Date()
  }
]);
*/
